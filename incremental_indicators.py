"""
增量技术指标计算器 - 高性能实时指标更新
避免重复计算历史数据，显著降低CPU负载
"""
import logging
from typing import Optional, Tuple
from datetime import datetime


class IncrementalATR:
    """
    增量ATR计算器 - 基于指数移动平均的实时更新
    
    算法原理：
    - True Range = max(H-L, |H-C_prev|, |L-C_prev|)
    - ATR = EMA(True Range, period)
    - EMA更新: new_ema = α × new_value + (1-α) × old_ema
    - α = 2/(period+1) 或 <PERSON>'s EMA: α = 1/period
    """
    
    def __init__(self, period: int = 14, use_wilder: bool = True):
        """
        初始化增量ATR计算器
        
        Args:
            period: ATR周期
            use_wilder: 是否使用Wilder's EMA (α=1/period)，否则使用标准EMA (α=2/(period+1))
        """
        self.period = period
        self.use_wilder = use_wilder
        
        # EMA平滑系数
        if use_wilder:
            self.alpha = 1.0 / period  # <PERSON>'s EMA
        else:
            self.alpha = 2.0 / (period + 1)  # 标准EMA
        
        # 状态变量
        self.current_atr: Optional[float] = None
        self.prev_close: Optional[float] = None
        self.initialized: bool = False
        self.data_count: int = 0
        
        # 初始化期间的缓存（用于启动期的准确计算）
        self.initial_true_ranges = []
        
    def update(self, high: float, low: float, close: float) -> Optional[float]:
        """
        增量更新ATR值
        
        Args:
            high: 当前最高价
            low: 当前最低价  
            close: 当前收盘价
            
        Returns:
            float: 更新后的ATR值，如果数据不足则返回None
        """
        try:
            # 计算真实范围
            if self.prev_close is not None:
                # 标准True Range计算
                tr1 = high - low  # 当日最高价 - 最低价
                tr2 = abs(high - self.prev_close)  # |当日最高价 - 昨日收盘价|
                tr3 = abs(low - self.prev_close)   # |当日最低价 - 昨日收盘价|
                true_range = max(tr1, tr2, tr3)
            else:
                # 第一个数据点，只能用高低价差
                true_range = high - low
            
            self.data_count += 1
            
            # 启动期处理
            if not self.initialized:
                self.initial_true_ranges.append(true_range)
                
                if len(self.initial_true_ranges) >= self.period:
                    # 用简单移动平均启动ATR
                    self.current_atr = sum(self.initial_true_ranges) / len(self.initial_true_ranges)
                    self.initialized = True
                    # 清空缓存以释放内存
                    self.initial_true_ranges.clear()
            else:
                # 增量更新ATR
                self.current_atr = self.alpha * true_range + (1 - self.alpha) * self.current_atr
            
            # 更新状态
            self.prev_close = close
            
            return self.current_atr
            
        except Exception as e:
            # 简单错误处理，避免中断整个系统
            return self.current_atr
    
    def get_value(self) -> Optional[float]:
        """获取当前ATR值"""
        return self.current_atr if self.initialized else None
    
    def is_ready(self) -> bool:
        """检查ATR是否已初始化完成"""
        return self.initialized
    
    def reset(self):
        """重置计算器状态"""
        self.current_atr = None
        self.prev_close = None
        self.initialized = False
        self.data_count = 0
        self.initial_true_ranges.clear()


class IncrementalADX:
    """
    增量ADX计算器 - 基于Wilder's EMA的实时更新
    
    算法原理：
    1. 计算方向性移动：+DM, -DM
    2. 计算真实范围：TR
    3. 平滑计算：+DI, -DI (基于EMA)
    4. 计算DX = |+DI - -DI| / (+DI + -DI) * 100
    5. ADX = EMA(DX)
    """
    
    def __init__(self, period: int = 14):
        """
        初始化增量ADX计算器
        
        Args:
            period: ADX周期
        """
        self.period = period
        self.alpha = 1.0 / period  # Wilder's EMA系数
        
        # 状态变量
        self.prev_high: Optional[float] = None
        self.prev_low: Optional[float] = None
        self.prev_close: Optional[float] = None
        
        # EMA状态
        self.plus_dm_ema: Optional[float] = None
        self.minus_dm_ema: Optional[float] = None
        self.tr_ema: Optional[float] = None
        self.adx_ema: Optional[float] = None
        
        # 初始化状态
        self.initialized: bool = False
        self.data_count: int = 0
        
        # 启动期缓存
        self.initial_plus_dm = []
        self.initial_minus_dm = []
        self.initial_tr = []
        self.initial_dx = []
        
    def update(self, high: float, low: float, close: float) -> Optional[float]:
        """
        增量更新ADX值
        
        Args:
            high: 当前最高价
            low: 当前最低价
            close: 当前收盘价
            
        Returns:
            float: 更新后的ADX值，如果数据不足则返回None
        """
        try:
            self.data_count += 1
            
            if self.prev_high is not None:
                # 计算方向性移动
                up_move = high - self.prev_high
                down_move = self.prev_low - low
                
                plus_dm = up_move if (up_move > down_move and up_move > 0) else 0
                minus_dm = down_move if (down_move > up_move and down_move > 0) else 0
                
                # 计算真实范围
                tr1 = high - low
                tr2 = abs(high - self.prev_close)
                tr3 = abs(low - self.prev_close)
                true_range = max(tr1, tr2, tr3)
                
                # 启动期处理
                if not self.initialized:
                    self.initial_plus_dm.append(plus_dm)
                    self.initial_minus_dm.append(minus_dm)
                    self.initial_tr.append(true_range)
                    
                    if len(self.initial_plus_dm) >= self.period:
                        # 初始化EMA
                        self.plus_dm_ema = sum(self.initial_plus_dm) / len(self.initial_plus_dm)
                        self.minus_dm_ema = sum(self.initial_minus_dm) / len(self.initial_minus_dm)
                        self.tr_ema = sum(self.initial_tr) / len(self.initial_tr)
                        
                        # 计算初始DI值并开始收集DX
                        if self.tr_ema > 0:
                            plus_di = 100 * self.plus_dm_ema / self.tr_ema
                            minus_di = 100 * self.minus_dm_ema / self.tr_ema
                            
                            di_sum = plus_di + minus_di
                            if di_sum > 0:
                                dx = 100 * abs(plus_di - minus_di) / di_sum
                                self.initial_dx.append(dx)
                        
                        # 检查是否可以初始化ADX
                        if len(self.initial_dx) >= self.period:
                            self.adx_ema = sum(self.initial_dx) / len(self.initial_dx)
                            self.initialized = True
                            # 清空缓存
                            self.initial_plus_dm.clear()
                            self.initial_minus_dm.clear()
                            self.initial_tr.clear()
                            self.initial_dx.clear()
                            
                else:
                    # 增量更新EMA
                    self.plus_dm_ema = self.alpha * plus_dm + (1 - self.alpha) * self.plus_dm_ema
                    self.minus_dm_ema = self.alpha * minus_dm + (1 - self.alpha) * self.minus_dm_ema
                    self.tr_ema = self.alpha * true_range + (1 - self.alpha) * self.tr_ema
                    
                    # 计算DI值
                    if self.tr_ema > 0:
                        plus_di = 100 * self.plus_dm_ema / self.tr_ema
                        minus_di = 100 * self.minus_dm_ema / self.tr_ema
                        
                        # 计算DX
                        di_sum = plus_di + minus_di
                        if di_sum > 0:
                            dx = 100 * abs(plus_di - minus_di) / di_sum
                            
                            # 更新ADX
                            self.adx_ema = self.alpha * dx + (1 - self.alpha) * self.adx_ema
            
            # 更新状态
            self.prev_high = high
            self.prev_low = low
            self.prev_close = close
            
            return self.adx_ema
            
        except Exception as e:
            return self.adx_ema
    
    def get_value(self) -> Optional[float]:
        """获取当前ADX值"""
        return self.adx_ema if self.initialized else None
    
    def get_directional_indicators(self) -> Tuple[Optional[float], Optional[float]]:
        """
        获取方向指标值
        
        Returns:
            Tuple[+DI, -DI]: 正向和负向方向指标
        """
        if not self.initialized or self.tr_ema is None or self.tr_ema <= 0:
            return None, None
        
        plus_di = 100 * self.plus_dm_ema / self.tr_ema
        minus_di = 100 * self.minus_dm_ema / self.tr_ema
        return plus_di, minus_di
    
    def is_ready(self) -> bool:
        """检查ADX是否已初始化完成"""
        return self.initialized
    
    def reset(self):
        """重置计算器状态"""
        self.prev_high = None
        self.prev_low = None
        self.prev_close = None
        
        self.plus_dm_ema = None
        self.minus_dm_ema = None
        self.tr_ema = None
        self.adx_ema = None
        
        self.initialized = False
        self.data_count = 0
        
        self.initial_plus_dm.clear()
        self.initial_minus_dm.clear()
        self.initial_tr.clear()
        self.initial_dx.clear()


class IndicatorManager:
    """
    指标管理器 - 统一管理多个增量指标
    """
    
    def __init__(self, atr_period: int = 14, adx_period: int = 14):
        """
        初始化指标管理器
        
        Args:
            atr_period: ATR周期
            adx_period: ADX周期
        """
        self.atr_calculator = IncrementalATR(period=atr_period, use_wilder=True)
        self.adx_calculator = IncrementalADX(period=adx_period)
        
        self.last_update: Optional[datetime] = None
        self.update_count: int = 0
    
    def update_all(self, high: float, low: float, close: float) -> dict:
        """
        同时更新所有指标

        Args:
            high: 最高价
            low: 最低价
            close: 收盘价

        Returns:
            dict: 包含所有指标值的字典
        """
        current_time = datetime.now()
        self.update_count += 1

        # 记录更新前的状态（用于调试）
        atr_ready_before = self.atr_calculator.is_ready()
        adx_ready_before = self.adx_calculator.is_ready()

        # 同时更新所有指标
        atr_value = self.atr_calculator.update(high, low, close)
        adx_value = self.adx_calculator.update(high, low, close)

        self.last_update = current_time

        # 获取方向指标
        plus_di, minus_di = self.adx_calculator.get_directional_indicators()

        # 🔥 检测状态变化并记录
        atr_ready_after = self.atr_calculator.is_ready()
        adx_ready_after = self.adx_calculator.is_ready()

        # 如果有指标刚刚变为就绪状态，记录日志
        if not atr_ready_before and atr_ready_after:
            print(f"🎉 ATR指标初始化完成！更新#{self.update_count}, 值={atr_value:.6f}")
        if not adx_ready_before and adx_ready_after:
            print(f"🎉 ADX指标初始化完成！更新#{self.update_count}, 值={adx_value:.2f}")

        return {
            'atr': atr_value,
            'adx': adx_value,
            'plus_di': plus_di,
            'minus_di': minus_di,
            'atr_ready': atr_ready_after,
            'adx_ready': adx_ready_after,
            'update_count': self.update_count,
            'last_update': self.last_update,
            # 🔥 新增调试信息
            'debug_info': {
                'atr_data_count': self.atr_calculator.data_count,
                'adx_data_count': self.adx_calculator.data_count,
                'input_prices': {'high': high, 'low': low, 'close': close},
                'state_changed': {
                    'atr_became_ready': not atr_ready_before and atr_ready_after,
                    'adx_became_ready': not adx_ready_before and adx_ready_after
                }
            }
        }
    
    def get_current_values(self) -> dict:
        """获取当前所有指标值 - 增强版本，提供调试信息和默认值"""
        plus_di, minus_di = self.adx_calculator.get_directional_indicators()
        
        # 获取原始值
        atr_value = self.atr_calculator.get_value()
        adx_value = self.adx_calculator.get_value()
        atr_ready = self.atr_calculator.is_ready()
        adx_ready = self.adx_calculator.is_ready()
        
        # 提供调试信息
        debug_info = {
            'atr_data_count': self.atr_calculator.data_count,
            'adx_data_count': self.adx_calculator.data_count,
            'atr_initialized': self.atr_calculator.initialized,
            'adx_initialized': self.adx_calculator.initialized,
            'update_count': self.update_count
        }
        
        return {
            'atr': atr_value,
            'adx': adx_value,
            'plus_di': plus_di,
            'minus_di': minus_di,
            'atr_ready': atr_ready,
            'adx_ready': adx_ready,
            'debug_info': debug_info
        }
    
    def is_all_ready(self) -> bool:
        """检查所有指标是否都已就绪"""
        return self.atr_calculator.is_ready() and self.adx_calculator.is_ready()
    
    def get_dashboard_ready_values(self) -> dict:
        """获取仪表盘可用的技术指标值 - 提供合理的默认值和状态信息"""
        plus_di, minus_di = self.adx_calculator.get_directional_indicators()
        
        # 获取原始值
        atr_value = self.atr_calculator.get_value()
        adx_value = self.adx_calculator.get_value()
        atr_ready = self.atr_calculator.is_ready()
        adx_ready = self.adx_calculator.is_ready()
        
        # 计算数据就绪百分比
        atr_progress = min(100, (self.atr_calculator.data_count / self.atr_calculator.period) * 100)
        adx_progress = min(100, (self.adx_calculator.data_count / self.adx_calculator.period) * 100)
        
        # 🔥 对于部分初始化的指标，提供有意义的估算值
        atr_estimate = None
        adx_estimate = None

        # ATR估算值：如果有部分数据，计算当前可用数据的简单平均
        if not atr_ready and len(self.atr_calculator.initial_true_ranges) > 0:
            atr_estimate = sum(self.atr_calculator.initial_true_ranges) / len(self.atr_calculator.initial_true_ranges)

        # ADX估算值：如果ATR已就绪但ADX未就绪，提供基于当前DI值的估算
        if atr_ready and not adx_ready and self.adx_calculator.plus_dm_ema is not None:
            if (self.adx_calculator.tr_ema and self.adx_calculator.tr_ema > 0 and
                len(self.adx_calculator.initial_dx) > 0):
                adx_estimate = sum(self.adx_calculator.initial_dx) / len(self.adx_calculator.initial_dx)
        
        if not atr_ready and self.atr_calculator.data_count >= 3:
            # ATR至少有3个数据点时可以给出粗略估算
            if len(self.atr_calculator.initial_true_ranges) >= 3:
                atr_estimate = sum(self.atr_calculator.initial_true_ranges) / len(self.atr_calculator.initial_true_ranges)
        
        if not adx_ready and self.adx_calculator.data_count >= 3:
            # ADX需要更多数据，但可以给出DM相关的估算
            adx_estimate = 15.0  # 使用中性值作为估算
        
        return {
            # 实际指标值（None表示尚未就绪）
            'atr': atr_value,
            'adx': adx_value,
            'plus_di': plus_di,
            'minus_di': minus_di,
            
            # 估算值（仅在未就绪时提供，供调试用）
            'atr_estimate': atr_estimate if not atr_ready else None,
            'adx_estimate': adx_estimate if not adx_ready else None,
            
            # 就绪状态
            'atr_ready': atr_ready,
            'adx_ready': adx_ready,
            'all_ready': self.is_all_ready(),
            
            # 进度信息（用于调试和状态显示）
            'atr_progress': atr_progress,
            'adx_progress': adx_progress,
            'atr_data_count': self.atr_calculator.data_count,
            'adx_data_count': self.adx_calculator.data_count,
            'total_updates': self.update_count,
            
            # 状态消息
            'status_message': self._get_status_message(atr_ready, adx_ready, atr_progress, adx_progress)
        }
    
    def _get_status_message(self, atr_ready: bool, adx_ready: bool, 
                           atr_progress: float, adx_progress: float) -> str:
        """生成状态消息"""
        if atr_ready and adx_ready:
            return "所有指标就绪"
        elif not atr_ready and not adx_ready:
            return f"正在初始化... ATR:{atr_progress:.0f}% ADX:{adx_progress:.0f}%"
        elif not atr_ready:
            return f"ATR初始化中... {atr_progress:.0f}%"
        else:
            return f"ADX初始化中... {adx_progress:.0f}%"
    
    def reset_all(self):
        """重置所有指标"""
        self.atr_calculator.reset()
        self.adx_calculator.reset()
        self.last_update = None
        self.update_count = 0