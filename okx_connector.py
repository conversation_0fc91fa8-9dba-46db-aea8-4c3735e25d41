"""
OKX异步API连接器 - 高性能WebSocket和REST API客户端
"""
import asyncio
import json
import hmac
import hashlib
import base64
import time
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional, Callable, List
import websockets
import aiohttp
from websockets.exceptions import ConnectionClosed, WebSocketException
from log_utils import get_structured_logger, KPICalculator


class TokenBucketRateLimiter:
    """令牌桶算法速率限制器 - 用于OKX API请求速率控制"""
    
    def __init__(self, capacity: int, refill_rate: float, initial_tokens: int = None):
        """
        初始化令牌桶
        
        Args:
            capacity: 桶容量（最大令牌数）
            refill_rate: 令牌填充速率（令牌/秒）
            initial_tokens: 初始令牌数（默认为满桶）
        """
        self.capacity = capacity
        self.refill_rate = refill_rate
        self.tokens = initial_tokens if initial_tokens is not None else capacity
        self.last_refill = time.time()
        self._lock = asyncio.Lock()
    
    async def acquire(self, tokens: int = 1) -> bool:
        """
        获取令牌（非阻塞）
        
        Args:
            tokens: 需要的令牌数量
            
        Returns:
            bool: 是否成功获取令牌
        """
        async with self._lock:
            self._refill()
            
            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            return False
    
    async def wait_for_tokens(self, tokens: int = 1, timeout: float = 30.0) -> bool:
        """
        等待获取令牌（阻塞，直到有足够令牌或超时）
        
        Args:
            tokens: 需要的令牌数量
            timeout: 最大等待时间（秒）
            
        Returns:
            bool: 是否成功获取令牌
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if await self.acquire(tokens):
                return True
            
            # 计算下次有足够令牌的时间
            async with self._lock:
                self._refill()
                needed_tokens = tokens - self.tokens
                if needed_tokens > 0:
                    wait_time = min(needed_tokens / self.refill_rate, 0.1)
                    await asyncio.sleep(wait_time)
                else:
                    await asyncio.sleep(0.01)  # 短暂等待
        
        return False
    
    def _refill(self):
        """填充令牌（内部方法，调用时需要持有锁）"""
        now = time.time()
        elapsed = now - self.last_refill
        
        if elapsed > 0:
            new_tokens = elapsed * self.refill_rate
            self.tokens = min(self.capacity, self.tokens + new_tokens)
            self.last_refill = now
    
    def get_status(self) -> Dict[str, Any]:
        """获取令牌桶状态"""
        return {
            "tokens": self.tokens,
            "capacity": self.capacity,
            "refill_rate": self.refill_rate,
            "fill_percentage": (self.tokens / self.capacity) * 100
        }


class OKXConnector:
    """OKX异步连接器 - 双通道实时数据总线"""
    
    def __init__(self, config):
        self.config = config
        self.logger = get_structured_logger(__name__)
        
        # HTTP客户端会话
        self.http_session: Optional[aiohttp.ClientSession] = None
        
        # 双通道WebSocket连接
        self.ws_public = None
        self.ws_private = None
        
        # 连接状态管理
        self.is_public_connected = False
        self.is_private_connected = False
        self.is_private_authenticated = False
        self.reconnect_attempts = {'public': 0, 'private': 0}
        self.max_reconnect_attempts = 10
        
        # 事件分发器系统
        self.event_callbacks: Dict[str, List[Callable]] = {
            'ticker': [],
            'books': [],
            'trades': [],          # 新增逐笔成交事件
            'funding-rate': [],
            'account': [],
            'orders': [],
            'positions': [],
            'balance': []
        }
        
        # 消息处理任务
        self._public_message_task = None
        self._private_message_task = None
        
        # WebSocket订单状态跟踪系统
        self.pending_orders: Dict[str, asyncio.Event] = {}  # order_id -> completion_event
        self.order_results: Dict[str, Dict[str, Any]] = {}  # order_id -> order_result
        self._order_lock = asyncio.Lock()
        
        # 合约规格缓存 - 动态获取所有交易对的合约规格
        self.instrument_specs: Dict[str, Dict[str, Any]] = {}  # 缓存所有交易对的合约规格
        self.instrument_specs_initialized = False
        
        # API速率限制器 - 基于OKX限制：20次/2秒
        self.order_rate_limiter = TokenBucketRateLimiter(
            capacity=20,        # 桶容量20个令牌
            refill_rate=10.0    # 每秒补充10个令牌 (20/2s)
        )
        self.query_rate_limiter = TokenBucketRateLimiter(
            capacity=30,        # 查询API限制相对宽松
            refill_rate=15.0    # 每秒补充15个令牌
        )
        
        # WebSocket实时盘口数据缓存 - 零延迟盘口获取
        self.orderbook_cache: Dict[str, Dict[str, Any]] = {}  # inst_id -> orderbook_data
        self.orderbook_lock = asyncio.Lock()
        self.orderbook_subscriptions: set = set()  # 已订阅的交易对
        
        # OKX服务器时间同步机制 - 防止时钟偏差导致的签名失败
        self.server_time_offset: float = 0.0  # 本地时间与服务器时间的偏差(毫秒)
        self.last_time_sync: float = 0  # 上次时间同步的时间戳
        self.time_sync_interval: float = 300  # 5分钟同步一次
        self.time_sync_lock = asyncio.Lock()
        
        # API基础URL
        self.base_url = "https://www.okx.com" if not config.USE_SANDBOX else "https://www.okx.com"
        
        # WebSocket URLs
        self.ws_public_url = "wss://ws.okx.com:8443/ws/v5/public" if not config.USE_SANDBOX else "wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999"
        self.ws_private_url = "wss://ws.okx.com:8443/ws/v5/private" if not config.USE_SANDBOX else "wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999"
        
        # 设置代理（支持自动检测）
        self.proxy = None
        self.proxy_enabled = config.PROXY_CONFIG.get("ENABLED", False)
        self.proxy_url = config.PROXY_CONFIG.get("URL", None)
        
        if self.proxy_enabled and self.proxy_url:
            self.proxy = self.proxy_url
            # 标记需要进行代理健康检查
            self._needs_proxy_check = True
        else:
            self._needs_proxy_check = False
    
    async def _check_proxy_health(self):
        """检查代理健康状态并自动调整"""
        if not self._needs_proxy_check:
            return
            
        try:
            from proxy_health_checker import ProxyHealthChecker
            
            self.logger.info("🔍 检查代理连接健康状态...")
            checker = ProxyHealthChecker(self.proxy_url)
            result = await checker.auto_detect_best_connection()
            
            recommendation = result['recommendation']
            
            if not recommendation['use_proxy'] and self.proxy:
                self.logger.warning(f"🚫 禁用代理连接: {recommendation['reason']}")
                self.proxy = None  # 禁用代理
                self.proxy_enabled = False
            elif recommendation['use_proxy'] and not self.proxy:
                self.logger.info(f"✅ 启用代理连接: {recommendation['reason']}")
                self.proxy = self.proxy_url
                self.proxy_enabled = True
            
            # 标记已检查过，避免重复检查
            self._needs_proxy_check = False
            
        except Exception as e:
            self.logger.error(f"代理健康检查失败: {e}")
            # 出错时保持当前配置
    
    async def start(self):
        """启动双通道连接器"""
        try:
            # 首先检查代理健康状态
            await self._check_proxy_health()
            # 创建HTTP会话
            connector = aiohttp.TCPConnector(
                limit=100,
                ttl_dns_cache=300,
                use_dns_cache=True
            )
            
            timeout = aiohttp.ClientTimeout(total=30)
            self.http_session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'AsyncArbitrageBot/1.0'
                }
            )
            
            # 启动双通道WebSocket连接
            await self._connect_dual_channels()
            
            # 初始化服务器时间同步
            self.logger.info("🕐 初始化服务器时间同步...")
            if await self.sync_server_time(force=True):
                self.logger.info(f"✅ 时钟同步完成，偏差: {self.server_time_offset:.1f}ms")
            else:
                self.logger.warning("⚠️ 时钟同步失败，将使用本地时间（可能影响API请求）")
            
            # 注册内部订单状态跟踪器
            self.register_event_callback('orders', self._handle_order_completion)
            
            self.logger.info("OKX双通道连接器启动成功")
            
        except Exception as e:
            self.logger.error(f"连接器启动失败: {e}")
            raise
    
    async def stop(self):
        """停止双通道连接器"""
        try:
            # 停止消息处理任务
            if self._public_message_task and not self._public_message_task.done():
                self._public_message_task.cancel()
                try:
                    await self._public_message_task
                except asyncio.CancelledError:
                    pass
            
            if self._private_message_task and not self._private_message_task.done():
                self._private_message_task.cancel()
                try:
                    await self._private_message_task
                except asyncio.CancelledError:
                    pass
            
            # 关闭双通道WebSocket连接
            if self.ws_public:
                await self.ws_public.close()
            if self.ws_private:
                await self.ws_private.close()
            
            # 关闭HTTP会话和连接器
            if self.http_session and not self.http_session.closed:
                await self.http_session.close()
                # 等待连接器完全关闭
                if hasattr(self.http_session, '_connector'):
                    await asyncio.sleep(0.25)  # 给连接器时间清理
            
            self.is_public_connected = False
            self.is_private_connected = False
            self.is_private_authenticated = False
            self.logger.info("OKX双通道连接器已停止")
            
        except Exception as e:
            self.logger.error(f"停止连接器时发生错误: {e}")
    
    def _generate_signature(self, timestamp: str, method: str, request_path: str, body: str = '') -> str:
        """生成API签名"""
        message = timestamp + method.upper() + request_path + body
        signature = base64.b64encode(
            hmac.new(
                self.config.API_SECRET.encode('utf-8'),
                message.encode('utf-8'),
                hashlib.sha256
            ).digest()
        ).decode('utf-8')
        return signature
    
    async def get_server_time(self) -> Optional[int]:
        """获取OKX服务器时间 - 用于时钟同步"""
        try:
            if not self.http_session:
                raise RuntimeError("HTTP会话未初始化")
            
            request_path = "/api/v5/public/time"
            
            async with self.http_session.get(
                f"{self.base_url}{request_path}",
                proxy=self.proxy
            ) as response:
                result = await response.json()
                
                if result.get('code') == '0' and result.get('data'):
                    server_time = int(result['data'][0]['ts'])
                    self.logger.debug(f"🕐 获取服务器时间: {server_time}")
                    return server_time
                else:
                    self.logger.warning(f"获取服务器时间失败: {result}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"获取服务器时间异常: {e}")
            return None
    
    async def sync_server_time(self, force: bool = False) -> bool:
        """同步服务器时间 - 计算并校准时钟偏差"""
        async with self.time_sync_lock:
            current_time = time.time()
            
            # 检查是否需要同步
            if not force and (current_time - self.last_time_sync) < self.time_sync_interval:
                return True
            
            try:
                # 记录请求发送时间
                local_time_before = time.time() * 1000  # 转换为毫秒
                
                # 获取服务器时间
                server_time = await self.get_server_time()
                if server_time is None:
                    return False
                
                # 记录请求返回时间
                local_time_after = time.time() * 1000
                
                # 估算网络延迟并计算时钟偏差
                network_delay = (local_time_after - local_time_before) / 2
                local_time_estimated = local_time_before + network_delay
                
                # 计算偏差（服务器时间 - 本地时间）
                old_offset = self.server_time_offset
                self.server_time_offset = server_time - local_time_estimated
                self.last_time_sync = current_time
                
                offset_change = abs(self.server_time_offset - old_offset)
                
                if offset_change > 1000:  # 偏差变化超过1秒
                    self.logger.warning(
                        f"⚠️ 时钟偏差显著变化: {old_offset:.1f}ms -> {self.server_time_offset:.1f}ms"
                    )
                else:
                    self.logger.debug(
                        f"🕐 时钟同步完成: 偏差={self.server_time_offset:.1f}ms, 网络延迟={network_delay:.1f}ms"
                    )
                
                return True
                
            except Exception as e:
                self.logger.error(f"时钟同步失败: {e}")
                return False
    
    def get_synced_timestamp(self) -> str:
        """获取同步后的时间戳 - 用于API签名"""
        # 使用校准后的时间
        local_time_ms = time.time() * 1000
        synced_time_ms = local_time_ms + self.server_time_offset
        
        # 转换为ISO 8601格式
        synced_datetime = datetime.fromtimestamp(synced_time_ms / 1000, timezone.utc)
        timestamp = synced_datetime.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
        
        return timestamp
    
    def _get_headers(self, method: str, request_path: str, body: str = '') -> Dict[str, str]:
        """获取API请求头 - 使用同步后的时间戳"""
        # 使用同步后的时间戳，防止时钟偏差导致签名失败
        timestamp = self.get_synced_timestamp()
        signature = self._generate_signature(timestamp, method, request_path, body)
        
        headers = {
            'OK-ACCESS-KEY': self.config.API_KEY,
            'OK-ACCESS-SIGN': signature,
            'OK-ACCESS-TIMESTAMP': timestamp,
            'OK-ACCESS-PASSPHRASE': self.config.API_PASSWORD,
            'Content-Type': 'application/json'
        }
        
        if self.config.USE_SANDBOX:
            headers['x-simulated-trading'] = '1'
        
        return headers
    
    async def _connect_dual_channels(self):
        """连接双通道WebSocket"""
        try:
            self.logger.info("🚀 开始连接OKX双通道WebSocket")
            
            # 并发连接公共和私有频道
            public_task = asyncio.create_task(self._connect_public_channel(), name="connect_public")
            private_task = asyncio.create_task(self._connect_private_channel(), name="connect_private")
            
            # 等待两个连接完成
            public_result, private_result = await asyncio.gather(
                public_task, private_task, return_exceptions=True
            )
            
            # 检查连接结果
            if isinstance(public_result, Exception):
                self.logger.error(f"公共频道连接失败: {public_result}")
                raise public_result
            
            if isinstance(private_result, Exception):
                self.logger.error(f"私有频道连接失败: {private_result}")
                raise private_result
            
            self.logger.info("✅ 双通道WebSocket连接成功")
            
            # 启动消息处理任务
            await self._start_message_handlers()
            
        except Exception as e:
            self.logger.error(f"双通道连接失败: {e}")
            await self._handle_dual_channel_reconnect()
    
    async def _connect_public_channel(self):
        """连接公共频道"""
        try:
            self.logger.info("🔗 连接公共频道...")
            
            self.ws_public = await websockets.connect(
                self.ws_public_url,
                ping_interval=30,
                ping_timeout=10
            )
            
            self.is_public_connected = True
            self.reconnect_attempts['public'] = 0
            
            self.logger.info("✅ 公共频道连接成功")
            
        except Exception as e:
            self.logger.error(f"公共频道连接失败: {e}")
            raise
    
    async def _connect_private_channel(self):
        """连接私有频道并进行身份验证"""
        try:
            self.logger.info("🔐 连接私有频道...")
            
            self.ws_private = await websockets.connect(
                self.ws_private_url,
                ping_interval=30,
                ping_timeout=10
            )
            
            self.is_private_connected = True
            self.reconnect_attempts['private'] = 0
            
            self.logger.info("✅ 私有频道连接成功")
            
            # 进行身份验证
            await self._authenticate_private_channel()
            
        except Exception as e:
            self.logger.error(f"私有频道连接失败: {e}")
            raise
    
    async def _authenticate_private_channel(self):
        """私有频道身份验证"""
        try:
            self.logger.info("🔑 正在进行私有频道身份验证...")
            
            # 生成身份验证签名 - WebSocket API要求Unix时间戳（秒）
            timestamp = str(int(time.time()))
            method = 'GET'
            request_path = '/users/self/verify'
            
            # 按照OKX要求拼接签名字符串
            message = timestamp + method + request_path
            signature = base64.b64encode(
                hmac.new(
                    self.config.API_SECRET.encode('utf-8'),
                    message.encode('utf-8'),
                    hashlib.sha256
                ).digest()
            ).decode('utf-8')
            
            # 构建登录请求
            login_message = {
                "op": "login",
                "args": [{
                    "apiKey": self.config.API_KEY,
                    "passphrase": self.config.API_PASSWORD,
                    "timestamp": timestamp,
                    "sign": signature
                }]
            }
            
            # 发送登录请求
            await self.ws_private.send(json.dumps(login_message))
            
            # 等待登录响应
            response = await asyncio.wait_for(self.ws_private.recv(), timeout=10)
            response_data = json.loads(response)
            
            # 检查登录结果
            if response_data.get('event') == 'login' and response_data.get('code') == '0':
                self.is_private_authenticated = True
                self.logger.info("✅ 私有频道身份验证成功")
            else:
                error_msg = response_data.get('msg', '未知错误')
                raise Exception(f"身份验证失败: {error_msg}")
                
        except asyncio.TimeoutError:
            raise Exception("身份验证超时")
        except Exception as e:
            self.logger.error(f"身份验证失败: {e}")
            raise
    
    async def _handle_dual_channel_reconnect(self):
        """处理双通道重连"""
        # 检查重连次数
        total_attempts = max(self.reconnect_attempts.get('public', 0), self.reconnect_attempts.get('private', 0))
        if total_attempts >= self.max_reconnect_attempts:
            self.logger.error("达到最大重连次数，停止重连")
            return
        
        wait_time = min(5 * (total_attempts + 1), 60)
        self.logger.warning(f"🔄 双通道重连中... 第{total_attempts + 1}次，等待{wait_time}秒")
        await asyncio.sleep(wait_time)
        
        try:
            await self._connect_dual_channels()
        except Exception as e:
            self.logger.error(f"双通道重连失败: {e}")
            # 增加重连计数
            self.reconnect_attempts['public'] += 1
            self.reconnect_attempts['private'] += 1
            await self._handle_dual_channel_reconnect()
    
    async def _start_message_handlers(self):
        """启动消息处理任务"""
        try:
            # 启动公共频道消息处理
            if self.is_public_connected:
                self._public_message_task = asyncio.create_task(
                    self._handle_public_messages(),
                    name="public_message_handler"
                )
            
            # 启动私有频道消息处理
            if self.is_private_connected and self.is_private_authenticated:
                self._private_message_task = asyncio.create_task(
                    self._handle_private_messages(),
                    name="private_message_handler"
                )
            
            self.logger.info("📬 消息处理任务已启动")
            
        except Exception as e:
            self.logger.error(f"启动消息处理任务失败: {e}")
            raise
    
    async def subscribe_public_channel(self, channel: str, inst_ids: List[str]):
        """订阅公共频道"""
        if not self.is_public_connected:
            raise RuntimeError("公共频道未连接")
        
        channels = [{"channel": channel, "instId": inst_id} for inst_id in inst_ids]
        
        message = {
            "op": "subscribe",
            "args": channels
        }
        
        try:
            await self.ws_public.send(json.dumps(message))
            self.logger.info(f"✅ 已订阅公共频道 {channel}: {inst_ids}")
        except Exception as e:
            self.logger.error(f"订阅公共频道失败: {e}")
            raise
    
    async def subscribe_private_channel(self, channel: str, inst_type: str = None, inst_id: str = None):
        """订阅私有频道"""
        if not self.is_private_connected or not self.is_private_authenticated:
            raise RuntimeError("私有频道未连接或未认证")
        
        # 构建订阅参数
        args = [{"channel": channel}]
        if inst_type:
            args[0]["instType"] = inst_type
        if inst_id:
            args[0]["instId"] = inst_id
        
        message = {
            "op": "subscribe",
            "args": args
        }
        
        try:
            await self.ws_private.send(json.dumps(message))
            self.logger.info(f"✅ 已订阅私有频道 {channel}")
        except Exception as e:
            self.logger.error(f"订阅私有频道失败: {e}")
            raise
    
    async def subscribe_tickers(self, inst_ids: List[str]):
        """订阅价格行情（兼容性方法）"""
        await self.subscribe_public_channel("tickers", inst_ids)
    
    async def subscribe_order_books(self, inst_ids: List[str]):
        """订阅订单簿"""
        await self.subscribe_public_channel("books", inst_ids)
    
    async def subscribe_account_updates(self):
        """订阅账户更新"""
        await self.subscribe_private_channel("account")
    
    async def subscribe_order_updates(self, inst_type: str = "SWAP"):
        """订阅订单更新"""
        await self.subscribe_private_channel("orders", inst_type=inst_type)
    
    async def subscribe_position_updates(self, inst_type: str = "SWAP"):
        """订阅仓位更新"""
        await self.subscribe_private_channel("positions", inst_type=inst_type)
    
    async def subscribe_funding_rates(self, inst_ids: List[str]):
        """订阅资金费率（用于净利润计算）"""
        await self.subscribe_public_channel("funding-rate", inst_ids)
    
    async def subscribe_trades(self, inst_ids: List[str]):
        """订阅逐笔成交数据（用于微观结构分析）"""
        await self.subscribe_public_channel("trades", inst_ids)
    
    async def subscribe_order_books_5(self, inst_ids: List[str]):
        """订阅5档订单簿（专用于微观结构分析）"""
        await self.subscribe_public_channel("books5", inst_ids)
    
    async def subscribe_order_books_full(self, inst_ids: List[str]):
        """订阅完整订单簿（400档深度）"""
        await self.subscribe_public_channel("books-l2-tbt", inst_ids)
    
    async def _handle_public_messages(self):
        """处理公共频道消息"""
        try:
            async for message in self.ws_public:
                try:
                    data = json.loads(message)
                    
                    # 检查是否为错误响应
                    if 'error' in data or data.get('event') == 'error':
                        self.logger.error(f"公共频道错误: {data}")
                        continue
                    
                    # 分发消息到相应的事件处理器
                    await self._dispatch_public_message(data)
                    
                except json.JSONDecodeError as e:
                    self.logger.error(f"解析公共频道消息失败: {e}")
                except Exception as e:
                    self.logger.error(f"处理公共频道消息失败: {e}")
        
        except ConnectionClosed:
            self.logger.warning("公共频道连接关闭")
            self.is_public_connected = False
            await self._handle_public_channel_reconnect()
        except WebSocketException as e:
            self.logger.error(f"公共频道WebSocket异常: {e}")
            self.is_public_connected = False
            await self._handle_public_channel_reconnect()
    
    async def _handle_private_messages(self):
        """处理私有频道消息"""
        try:
            async for message in self.ws_private:
                try:
                    data = json.loads(message)
                    
                    # 检查是否为错误响应
                    if 'error' in data or data.get('event') == 'error':
                        self.logger.error(f"私有频道错误: {data}")
                        continue
                    
                    # 分发消息到相应的事件处理器
                    await self._dispatch_private_message(data)
                    
                except json.JSONDecodeError as e:
                    self.logger.error(f"解析私有频道消息失败: {e}")
                except Exception as e:
                    self.logger.error(f"处理私有频道消息失败: {e}")
        
        except ConnectionClosed:
            self.logger.warning("私有频道连接关闭")
            self.is_private_connected = False
            self.is_private_authenticated = False
            await self._handle_private_channel_reconnect()
        except WebSocketException as e:
            self.logger.error(f"私有频道WebSocket异常: {e}")
            self.is_private_connected = False
            self.is_private_authenticated = False
            await self._handle_private_channel_reconnect()
    
    async def _handle_public_channel_reconnect(self):
        """公共频道重连"""
        if self.reconnect_attempts['public'] >= self.max_reconnect_attempts:
            self.logger.error("公共频道达到最大重连次数")
            return
        
        self.reconnect_attempts['public'] += 1
        wait_time = min(5 * self.reconnect_attempts['public'], 60)
        
        self.logger.warning(f"🔄 公共频道重连中... 第{self.reconnect_attempts['public']}次")
        await asyncio.sleep(wait_time)
        
        try:
            await self._connect_public_channel()
            # 重新启动消息处理
            self._public_message_task = asyncio.create_task(self._handle_public_messages())
        except Exception as e:
            self.logger.error(f"公共频道重连失败: {e}")
            await self._handle_public_channel_reconnect()
    
    async def _handle_private_channel_reconnect(self):
        """私有频道重连"""
        if self.reconnect_attempts['private'] >= self.max_reconnect_attempts:
            self.logger.error("私有频道达到最大重连次数")
            return
        
        self.reconnect_attempts['private'] += 1
        wait_time = min(5 * self.reconnect_attempts['private'], 60)
        
        self.logger.warning(f"🔄 私有频道重连中... 第{self.reconnect_attempts['private']}次")
        await asyncio.sleep(wait_time)
        
        try:
            await self._connect_private_channel()
            # 重新启动消息处理
            self._private_message_task = asyncio.create_task(self._handle_private_messages())
        except Exception as e:
            self.logger.error(f"私有频道重连失败: {e}")
            await self._handle_private_channel_reconnect()
    
    async def place_order(self, inst_id: str, trade_mode: str, side: str, 
                         ord_type: str, sz: str, px: Optional[str] = None,
                         **kwargs) -> Dict[str, Any]:
        """异步下单 - 集成速率限制器"""
        if not self.http_session:
            raise RuntimeError("HTTP会话未初始化")
        
        # 应用速率限制 - 下单API
        if not await self.order_rate_limiter.wait_for_tokens(1, timeout=10.0):
            raise RuntimeError("下单速率限制超时 - API请求过于频繁")
        
        request_path = "/api/v5/trade/order"
        
        order_data = {
            "instId": inst_id,
            "tdMode": trade_mode,
            "side": side,
            "ordType": ord_type,
            "sz": sz
        }
        
        if px:
            order_data["px"] = px
        
        # 为期货合约自动添加posSide参数
        if "SWAP" in inst_id or "FUTURES" in inst_id:
            # 对于net模式，使用net；对于long/short模式，根据side确定
            if trade_mode == "cross" or trade_mode == "isolated":
                order_data["posSide"] = "net"  # 单向持仓模式
            else:
                # 双向持仓模式（如果使用）
                order_data["posSide"] = "long" if side == "buy" else "short"
        
        # 添加其他参数
        order_data.update(kwargs)
        
        # 调试日志：显示发送的订单数据
        self.logger.debug(f"📤 [{inst_id}] 发送订单数据: {json.dumps(order_data, indent=2)}")
        
        body = json.dumps(order_data)
        headers = self._get_headers("POST", request_path, body)
        
        try:
            start_time = time.time()
            
            async with self.http_session.post(
                f"{self.base_url}{request_path}",
                data=body,
                headers=headers,
                proxy=self.proxy
            ) as response:
                result = await response.json()
                
                latency = (time.time() - start_time) * 1000
                self.logger.info(f"下单完成，延迟: {latency:.2f}ms")
                
                if latency > self.config.MAX_ORDER_LATENCY_MS:
                    self.logger.warning(f"下单延迟过高: {latency:.2f}ms")
                
                return result
                
        except Exception as e:
            self.logger.error(f"下单失败: {e}")
            raise
    
    async def _execute_smart_order(self, inst_id: str, side: str, size: str, trade_mode: str) -> Dict[str, Any]:
        """执行单条腿的三阶段智能订单"""
        smart_config = self.config.ORDER_EXECUTION["SMART_EXECUTION"]
        execution_result = {
            "inst_id": inst_id,
            "side": side,
            "requested_size": float(size),
            "filled_size": 0.0,
            "avg_price": 0.0,
            "execution_stages": [],
            "success": False,
            "order_ids": []
        }
        
        try:
            # 阶段一：Post-Only Maker尝试
            self.logger.info(f"🔄 [{inst_id}] 阶段一: Post-Only Maker尝试")
            maker_result = await self._stage_one_maker(inst_id, side, size, trade_mode, smart_config)
            execution_result["execution_stages"].append(maker_result)
            
            if maker_result["success"]:
                execution_result.update({
                    "filled_size": maker_result["filled_size"],
                    "avg_price": maker_result["avg_price"],
                    "success": True,
                    "order_ids": [maker_result["order_id"]]
                })
                self.logger.info(f"✅ [{inst_id}] Maker成交成功: {maker_result['filled_size']}")
                return execution_result
            
            # 阶段二：IOC Taker执行
            self.logger.info(f"🔄 [{inst_id}] 阶段二: IOC Taker执行")
            taker_result = await self._stage_two_taker(inst_id, side, size, trade_mode, smart_config)
            execution_result["execution_stages"].append(taker_result)
            
            if taker_result["success"]:
                execution_result.update({
                    "filled_size": taker_result["filled_size"],
                    "avg_price": taker_result["avg_price"],
                    "success": True,
                    "order_ids": [taker_result["order_id"]]
                })
                self.logger.info(f"✅ [{inst_id}] Taker成交成功: {taker_result['filled_size']}")
                return execution_result
            
            # 如果智能执行失败，根据配置决定是否回退到市价单
            if self.config.ORDER_EXECUTION.get("FALLBACK_TO_MARKET", True):
                self.logger.warning(f"⚠️  [{inst_id}] 智能执行失败，回退到市价单")
                self.logger.info(f"🎯 [{inst_id}] 市价单参数: {side} {size} @ 市价")
                
                market_result = await self.place_order(
                    inst_id=inst_id,
                    trade_mode=trade_mode,
                    side=side,
                    ord_type="market",
                    sz=size
                )
                
                if market_result.get('code') == '0':
                    order_data = market_result['data'][0]
                    order_id = order_data['ordId']
                    
                    # 等待订单成交并获取成交信息
                    filled_info = await self._wait_for_order_fill(order_id, inst_id, timeout=10)
                    
                    if filled_info['success']:
                        execution_result.update({
                            "filled_size": filled_info['filled_size'],
                            "avg_price": filled_info['avg_price'],
                            "success": True,
                            "order_ids": [order_id]
                        })
                        execution_result["execution_stages"].append({
                            "stage": "fallback_market",
                            "success": True,
                            "order_id": order_id,
                            "filled_size": filled_info['filled_size'],
                            "avg_price": filled_info['avg_price']
                        })
                        self.logger.info(f"✅ [{inst_id}] 市价单成交成功: {filled_info['filled_size']} @ {filled_info['avg_price']}")
                    else:
                        self.logger.error(f"❌ [{inst_id}] 市价单成交失败或超时")
                        execution_result["success"] = False
                else:
                    # 市价单下单也失败了
                    error_msg = market_result.get('msg', 'Unknown error')
                    error_code = market_result.get('code', 'Unknown')
                    self.logger.error(f"❌ [{inst_id}] 市价单下单失败: 错误码={error_code}, 消息={error_msg}")
                    self.logger.error(f"完整API响应: {json.dumps(market_result, indent=2)}")
                    execution_result["success"] = False
                    execution_result["error"] = f"市价单失败: {error_code}: {error_msg}"
                    
            return execution_result
            
        except Exception as e:
            self.logger.error(f"智能订单执行失败 [{inst_id}]: {e}")
            execution_result["error"] = str(e)
            return execution_result
    
    async def _wait_for_order_fill(self, order_id: str, inst_id: str, timeout: int = 10) -> Dict[str, Any]:
        """等待订单成交并获取成交信息 - 混合确认机制（WebSocket优先 + REST备用）"""
        start_time = time.time()
        websocket_timeout = min(timeout * 0.7, 5)  # WebSocket等待时间不超过总时间的70%或5秒
        
        # 第一阶段：优先使用WebSocket实时数据
        try:
            # 检查是否已有WebSocket数据
            async with self._order_lock:
                if order_id in self.order_results:
                    cached_result = self.order_results[order_id]
                    state = cached_result.get('state', '')
                    
                    if state == 'filled':
                        self.logger.debug(f"⚡ 从WebSocket缓存获取订单状态: {order_id} -> {state}")
                        return {
                            'success': True,
                            'filled_size': float(cached_result.get('fillSz', 0)),
                            'avg_price': float(cached_result.get('avgPx', 0)),
                            'state': state
                        }
                    elif state == 'canceled':
                        self.logger.warning(f"⚡ 从WebSocket缓存发现订单已取消: {order_id}")
                        return {
                            'success': False,
                            'error': 'canceled',
                            'filled_size': 0,
                            'avg_price': 0
                        }
            
            # 如果没有缓存数据，尝试WebSocket等待
            self.logger.debug(f"⚡ [{inst_id}] WebSocket等待订单确认: {order_id} (超时: {websocket_timeout}s)")
            websocket_result = await self._wait_for_order_completion(order_id, int(websocket_timeout * 1000))
            
            if websocket_result:
                state = websocket_result.get('state', '')
                if state == 'filled':
                    self.logger.debug(f"✅ WebSocket确认订单成交: {order_id}")
                    return {
                        'success': True,
                        'filled_size': float(websocket_result.get('fillSz', 0)),
                        'avg_price': float(websocket_result.get('avgPx', 0)),
                        'state': state
                    }
                elif state == 'canceled':
                    self.logger.warning(f"❌ WebSocket确认订单已取消: {order_id}")
                    return {
                        'success': False,
                        'error': 'canceled',
                        'filled_size': 0,
                        'avg_price': 0
                    }
        
        except Exception as e:
            self.logger.warning(f"WebSocket等待失败: {order_id} - {e}")
        
        # 第二阶段：WebSocket超时或失败，回退到REST轮询
        remaining_time = timeout - (time.time() - start_time)
        if remaining_time <= 0:
            self.logger.warning(f"等待订单成交超时: {order_id}")
            return {
                'success': False,
                'error': 'timeout',
                'filled_size': 0,
                'avg_price': 0
            }
        
        self.logger.debug(f"📞 [{inst_id}] WebSocket超时，回退到REST轮询: {order_id} (剩余: {remaining_time:.1f}s)")
        check_interval = 0.5
        
        while time.time() - start_time < timeout:
            try:
                # 查询订单状态
                order_info = await self._get_order_info(order_id, inst_id)
                
                if order_info['success']:
                    order_data = order_info['data']
                    state = order_data.get('state', '')
                    
                    if state == 'filled':
                        self.logger.debug(f"✅ REST确认订单成交: {order_id}")
                        return {
                            'success': True,
                            'filled_size': float(order_data.get('fillSz', 0)),
                            'avg_price': float(order_data.get('avgPx', 0)),
                            'state': state
                        }
                    elif state in ['canceled', 'live']:
                        if state == 'canceled':
                            self.logger.warning(f"❌ REST确认订单已取消: {order_id}")
                            return {
                                'success': False,
                                'error': 'canceled',
                                'filled_size': 0,
                                'avg_price': 0
                            }
                        continue
                    elif state == 'partially_filled':
                        continue
                
                await asyncio.sleep(check_interval)
                
            except Exception as e:
                self.logger.error(f"REST查询订单状态失败 {order_id}: {e}")
                await asyncio.sleep(check_interval)
        
        # 最终超时
        self.logger.warning(f"⏰ 订单成交确认最终超时: {order_id}")
        return {
            'success': False,
            'error': 'timeout',
            'filled_size': 0,
            'avg_price': 0
        }
    
    async def _get_order_info(self, order_id: str, inst_id: str) -> Dict[str, Any]:
        """获取订单信息"""
        try:
            request_path = f"/api/v5/trade/order?instId={inst_id}&ordId={order_id}"
            headers = self._get_headers("GET", request_path, "")
            
            async with self.http_session.get(
                f"{self.base_url}{request_path}",
                headers=headers,
                proxy=self.proxy
            ) as response:
                result = await response.json()
                
                if result.get('code') == '0' and result.get('data'):
                    return {
                        'success': True,
                        'data': result['data'][0]
                    }
                else:
                    return {
                        'success': False,
                        'error': result.get('msg', 'Unknown error')
                    }
                    
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _stage_one_maker(self, inst_id: str, side: str, size: str, trade_mode: str, config: Dict) -> Dict:
        """阶段一：Post-Only Maker尝试（WebSocket事件驱动，零延迟）"""
        try:
            # 获取最新盘口价格
            best_prices = await self.get_best_prices(inst_id)
            if not best_prices:
                return {"stage": "maker", "success": False, "error": "无法获取盘口价格"}
            
            # 计算Maker价格（买单在买一价，卖单在卖一价）
            if side == "buy":
                maker_price = best_prices['bid'] + config.get('MAKER_PRICE_OFFSET', 0.0)
            else:
                maker_price = best_prices['ask'] - config.get('MAKER_PRICE_OFFSET', 0.0)
            
            # 调整价格精度
            tick_size = self.get_tick_size(inst_id)
            maker_price = round(maker_price / tick_size) * tick_size
            
            self.logger.debug(f"🎯 [{inst_id}] Maker订单准备: {side} {size} @ {maker_price:.6f} (买一:{best_prices['bid']:.6f}, 卖一:{best_prices['ask']:.6f})")
            
            # 下达Post-Only订单
            result = await self.place_order(
                inst_id=inst_id,
                trade_mode=trade_mode,
                side=side,
                ord_type="post_only",
                sz=size,
                px=str(maker_price)
            )
            
            if result.get('code') != '0':
                error_msg = result.get('msg', 'Unknown error')
                error_code = result.get('code', 'Unknown')
                self.logger.error(f"❌ [{inst_id}] Maker订单下单失败: 错误码={error_code}, 消息={error_msg}")
                self.logger.error(f"完整API响应: {json.dumps(result, indent=2)}")
                self.logger.error(f"订单参数: inst_id={inst_id}, side={side}, size={size}, price={maker_price}")
                return {"stage": "maker", "success": False, "error": f"{error_code}: {error_msg}"}
            
            order_id = result['data'][0]['ordId']
            
            # 混合等待模式：WebSocket + REST查询备用
            timeout_ms = config.get('POST_ONLY_TIMEOUT_MS', 2000)
            self.logger.debug(f"⚡ [{inst_id}] Maker订单 {order_id} 等待确认...")
            
            # 先尝试WebSocket等待
            order_status = await self._wait_for_order_completion(order_id, min(timeout_ms, 1000))
            
            # 如果WebSocket等待失败，使用REST查询
            if not order_status:
                self.logger.debug(f"📞 [{inst_id}] WebSocket等待超时，使用REST查询")
                order_status = await self.get_order_status(inst_id, order_id)
            
            if not order_status:
                # 彻底超时，取消订单
                self.logger.debug(f"⏰ [{inst_id}] Maker订单超时，取消订单 {order_id}")
                await self.cancel_order(inst_id, order_id)
                return {"stage": "maker", "success": False, "order_id": order_id, "error": "订单状态查询超时"}
            
            # 解析订单状态
            state = order_status.get('state')
            filled_size = float(order_status.get('fillSz', 0))
            avg_price = float(order_status.get('avgPx', 0))
            
            if state == 'filled':
                self.logger.debug(f"✅ [{inst_id}] Maker订单完全成交: {filled_size} @ {avg_price}")
                return {
                    "stage": "maker",
                    "success": True,
                    "order_id": order_id,
                    "filled_size": filled_size,
                    "avg_price": avg_price,
                    "maker_price": maker_price
                }
            else:
                # 取消未完成的订单
                self.logger.debug(f"⚠️  [{inst_id}] Maker订单未完全成交，取消: {state}")
                await self.cancel_order(inst_id, order_id)
                return {
                    "stage": "maker",
                    "success": False,
                    "order_id": order_id,
                    "partial_fill": filled_size,
                    "reason": f"未完全成交，状态: {state}"
                }
                
        except Exception as e:
            return {"stage": "maker", "success": False, "error": str(e)}
    
    async def _stage_two_taker(self, inst_id: str, side: str, size: str, trade_mode: str, config: Dict) -> Dict:
        """阶段二：IOC Taker执行（WebSocket事件驱动，零延迟）"""
        try:
            # 重新获取最新盘口价格
            best_prices = await self.get_best_prices(inst_id)
            if not best_prices:
                return {"stage": "taker", "success": False, "error": "无法获取盘口价格"}
            
            # 计算Taker价格（买单在卖一价，卖单在买一价，加上偏移）
            if side == "buy":
                taker_price = best_prices['ask'] + config.get('TAKER_PRICE_OFFSET', 0.0001)
            else:
                taker_price = best_prices['bid'] - config.get('TAKER_PRICE_OFFSET', 0.0001)
            
            # 调整价格精度
            tick_size = self.get_tick_size(inst_id)
            taker_price = round(taker_price / tick_size) * tick_size
            
            self.logger.debug(f"⚡ [{inst_id}] Taker订单准备: {side} {size} @ {taker_price:.6f} (买一:{best_prices['bid']:.6f}, 卖一:{best_prices['ask']:.6f})")
            
            # 下达IOC订单
            result = await self.place_order(
                inst_id=inst_id,
                trade_mode=trade_mode,
                side=side,
                ord_type="ioc",
                sz=size,
                px=str(taker_price)
            )
            
            if result.get('code') != '0':
                error_msg = result.get('msg', 'Unknown error')
                error_code = result.get('code', 'Unknown')
                self.logger.error(f"❌ [{inst_id}] Taker订单下单失败: 错误码={error_code}, 消息={error_msg}")
                self.logger.error(f"完整API响应: {json.dumps(result, indent=2)}")
                self.logger.error(f"订单参数: inst_id={inst_id}, side={side}, size={size}, price={taker_price}")
                return {"stage": "taker", "success": False, "error": f"{error_code}: {error_msg}"}
            
            order_id = result['data'][0]['ordId']
            
            # 混合等待模式：WebSocket + REST查询备用
            timeout_ms = config.get('IOC_TIMEOUT_MS', 1000)
            self.logger.debug(f"⚡ [{inst_id}] Taker订单 {order_id} 等待确认...")
            
            # 先尝试WebSocket等待
            order_status = await self._wait_for_order_completion(order_id, min(timeout_ms, 500))
            
            # 如果WebSocket等待失败，使用REST查询
            if not order_status:
                self.logger.debug(f"📞 [{inst_id}] WebSocket等待超时，使用REST查询")
                order_status = await self.get_order_status(inst_id, order_id)
            
            if not order_status:
                # IOC订单应该立即执行，如果还查不到则认为失败
                self.logger.debug(f"⏰ [{inst_id}] Taker订单状态查询失败")
                return {"stage": "taker", "success": False, "order_id": order_id, "error": "订单状态查询超时"}
            
            # 解析订单状态
            filled_size = float(order_status.get('fillSz', 0))
            avg_price = float(order_status.get('avgPx', 0))
            state = order_status.get('state')
            
            if filled_size > 0:
                self.logger.debug(f"✅ [{inst_id}] Taker订单成交: {filled_size} @ {avg_price}")
                return {
                    "stage": "taker",
                    "success": True,
                    "order_id": order_id,
                    "filled_size": filled_size,
                    "avg_price": avg_price,
                    "taker_price": taker_price,
                    "final_state": state
                }
            
            self.logger.debug(f"❌ [{inst_id}] Taker订单无成交: {state}")
            return {"stage": "taker", "success": False, "order_id": order_id, "reason": "IOC订单无成交"}
            
        except Exception as e:
            return {"stage": "taker", "success": False, "error": str(e)}
    
    async def _handle_order_completion(self, data: Dict[str, Any]):
        """处理订单完成事件（WebSocket驱动的状态跟踪）"""
        try:
            order_data = data.get("data", [])
            
            for order_info in order_data:
                order_id = order_info.get("ordId")
                if not order_id:
                    continue
                
                # 检查是否是我们跟踪的订单
                async with self._order_lock:
                    if order_id in self.pending_orders:
                        # 更新订单结果
                        self.order_results[order_id] = {
                            "ordId": order_id,
                            "instId": order_info.get("instId"),
                            "state": order_info.get("state"),
                            "side": order_info.get("side"),
                            "fillSz": order_info.get("fillSz", "0"),
                            "avgPx": order_info.get("avgPx", "0"),
                            "sz": order_info.get("sz", "0"),
                            "px": order_info.get("px", "0"),
                            "ordType": order_info.get("ordType"),
                            "updateTime": order_info.get("uTime")
                        }
                        
                        # 设置事件，通知等待的任务
                        self.pending_orders[order_id].set()
                        
                        self.logger.debug(f"📋 订单状态更新: {order_id} -> {order_info.get('state')}")
                        
        except Exception as e:
            self.logger.error(f"处理订单完成事件失败: {e}")
    
    async def _wait_for_order_completion(self, order_id: str, timeout_ms: int = 5000) -> Optional[Dict[str, Any]]:
        """等待订单完成（基于WebSocket事件，非REST查询）"""
        try:
            # 创建事件对象用于等待
            async with self._order_lock:
                completion_event = asyncio.Event()
                self.pending_orders[order_id] = completion_event
            
            # 等待订单完成事件，带超时
            try:
                await asyncio.wait_for(completion_event.wait(), timeout=timeout_ms / 1000.0)
                
                # 获取订单结果
                async with self._order_lock:
                    result = self.order_results.get(order_id)
                    # 清理跟踪
                    self.pending_orders.pop(order_id, None)
                    self.order_results.pop(order_id, None)
                
                return result
                
            except asyncio.TimeoutError:
                # 超时清理
                async with self._order_lock:
                    self.pending_orders.pop(order_id, None)
                    self.order_results.pop(order_id, None)
                
                self.logger.warning(f"⏰ 订单 {order_id} 等待超时 ({timeout_ms}ms)")
                return None
                
        except Exception as e:
            self.logger.error(f"等待订单完成失败: {e}")
            return None
    
    async def place_arbitrage_orders(self, spot_params: Dict, futures_params: Dict) -> tuple:
        """
        简化版套利订单执行 - 单一IOC阶段 + 强大的对冲回滚安全网
        
        核心理念：让系统变得"愚蠢但安全"，而不是"聪明但脆弱"
        """
        self.logger.info("🚀 开始简化版套利订单执行 (单一IOC阶段)")
        
        try:
            # 步骤1: 数量精度对齐（保留现有逻辑）
            if self.config.ORDER_EXECUTION.get("ENABLE_SIZE_ALIGNMENT", True):
                aligned_params = await self._align_order_sizes(spot_params, futures_params)
                spot_params, futures_params = aligned_params
                self.logger.info(f"📏 数量精度已对齐: 现货={spot_params['sz']}, 期货={futures_params['sz']}")
            
            # 步骤2: 执行单一IOC阶段
            self.logger.info("📋 执行IOC限价单配对")
            spot_result, futures_result = await self._execute_simple_ioc(spot_params, futures_params)
            
            # 步骤3: 验证执行结果
            if self._validate_dual_success(spot_result, futures_result):
                self.logger.info("✅ IOC订单双边成交成功")
                return await self._finalize_successful_trade(spot_result, futures_result, "IOC")
            
            # 步骤4: 执行失败，立即启动对冲回滚安全网
            self.logger.warning("⚠️ IOC执行失败，启动对冲回滚安全网")
            rollback_result = await self._activate_hedge_rollback_system(
                spot_result, futures_result, spot_params, futures_params
            )
            
            if rollback_result['success']:
                self.logger.info("✅ 对冲回滚成功，风险已消除")
                return rollback_result['final_results']
            else:
                self.logger.error("❌ 对冲回滚失败，交易终止")
                return (
                    {"success": False, "error": "IOC执行失败且回滚失败", "filled_size": 0, "avg_price": 0},
                    {"success": False, "error": "IOC执行失败且回滚失败", "filled_size": 0, "avg_price": 0}
                )
            
        except Exception as e:
            self.logger.error(f"套利订单执行异常: {e}")
            # 异常情况下也要尝试启动安全网
            try:
                await self._emergency_system_cleanup(spot_params, futures_params)
            except Exception as cleanup_error:
                self.logger.critical(f"紧急清理失败: {cleanup_error}")
            raise
    
    async def _execute_simple_ioc(self, spot_params: Dict, futures_params: Dict) -> tuple:
        """
        简化的IOC限价单执行 - 唯一的执行方式
        
        逻辑简单：获取最优价格 -> 同时下单 -> 等待结果 -> 返回
        """
        try:
            # 获取盘口价格
            spot_prices = await self.get_best_prices(spot_params["inst_id"])
            futures_prices = await self.get_best_prices(futures_params["inst_id"])
            
            if not spot_prices or not futures_prices:
                return (
                    {"success": False, "error": "无法获取盘口价格", "filled_size": 0, "avg_price": 0},
                    {"success": False, "error": "无法获取盘口价格", "filled_size": 0, "avg_price": 0}
                )
            
            # 计算IOC价格（略微穿越盘口确保成交）
            taker_offset = self.config.ORDER_EXECUTION["SMART_EXECUTION"]["TAKER_PRICE_OFFSET"]
            
            if spot_params["side"] == "buy":
                spot_price = spot_prices['ask'] + taker_offset
            else:
                spot_price = spot_prices['bid'] - taker_offset
                
            if futures_params["side"] == "buy":
                futures_price = futures_prices['ask'] + taker_offset
            else:
                futures_price = futures_prices['bid'] - taker_offset
            
            # 价格精度调整
            spot_price = round(spot_price / self.get_tick_size(spot_params["inst_id"])) * self.get_tick_size(spot_params["inst_id"])
            futures_price = round(futures_price / self.get_tick_size(futures_params["inst_id"])) * self.get_tick_size(futures_params["inst_id"])
            
            self.logger.info(f"💰 IOC价格: 现货={spot_price}, 期货={futures_price}")
            
            # 并发执行IOC订单
            spot_task = asyncio.create_task(self.place_order(
                inst_id=spot_params["inst_id"],
                trade_mode=spot_params["trade_mode"],
                side=spot_params["side"],
                ord_type="ioc",
                sz=spot_params["sz"],
                px=str(spot_price)
            ))
            
            futures_task = asyncio.create_task(self.place_order(
                inst_id=futures_params["inst_id"],
                trade_mode=futures_params["trade_mode"],
                side=futures_params["side"],
                ord_type="ioc",
                sz=futures_params["sz"],
                px=str(futures_price)
            ))
            
            # 等待订单结果
            spot_order_result, futures_order_result = await asyncio.gather(
                spot_task, futures_task, return_exceptions=True
            )
            
            # 验证订单提交并等待成交
            return await self._validate_simple_ioc_results(
                spot_order_result, futures_order_result, spot_params, futures_params
            )
            
        except Exception as e:
            self.logger.error(f"简化IOC执行失败: {e}")
            return (
                {"success": False, "error": f"IOC执行异常: {str(e)}", "filled_size": 0, "avg_price": 0},
                {"success": False, "error": f"IOC执行异常: {str(e)}", "filled_size": 0, "avg_price": 0}
            )
    
    async def _validate_simple_ioc_results(self, spot_result, futures_result, 
                                         spot_params: Dict, futures_params: Dict) -> tuple:
        """验证简化IOC执行结果"""
        # 检查订单提交状态
        spot_success = not isinstance(spot_result, Exception) and spot_result.get('code') == '0'
        futures_success = not isinstance(futures_result, Exception) and futures_result.get('code') == '0'
        
        if not spot_success or not futures_success:
            self.logger.warning("💥 IOC订单提交失败")
            return (
                {"success": False, "error": "IOC订单提交失败", "filled_size": 0, "avg_price": 0, "order_result": spot_result if not spot_success else None},
                {"success": False, "error": "IOC订单提交失败", "filled_size": 0, "avg_price": 0, "order_result": futures_result if not futures_success else None}
            )
        
        # 提取订单ID并等待成交
        spot_order_id = spot_result['data'][0]['ordId']
        futures_order_id = futures_result['data'][0]['ordId']
        
        # IOC订单通常很快成交或失败，等待时间短一些
        ioc_timeout = 2  # 2秒超时
        
        spot_fill_task = asyncio.create_task(
            self._wait_for_order_fill(spot_order_id, spot_params["inst_id"], ioc_timeout)
        )
        futures_fill_task = asyncio.create_task(
            self._wait_for_order_fill(futures_order_id, futures_params["inst_id"], ioc_timeout)
        )
        
        spot_fill_result, futures_fill_result = await asyncio.gather(
            spot_fill_task, futures_fill_task
        )
        
        # 返回成交结果（无论成功还是失败）
        return (
            {
                "success": spot_fill_result['success'],
                "filled_size": spot_fill_result.get('filled_size', 0),
                "avg_price": spot_fill_result.get('avg_price', 0),
                "order_ids": [spot_order_id] if spot_fill_result['success'] else [],
                "error": spot_fill_result.get('error') if not spot_fill_result['success'] else None
            },
            {
                "success": futures_fill_result['success'],
                "filled_size": futures_fill_result.get('filled_size', 0),
                "avg_price": futures_fill_result.get('avg_price', 0),
                "order_ids": [futures_order_id] if futures_fill_result['success'] else [],
                "error": futures_fill_result.get('error') if not futures_fill_result['success'] else None
            }
        )
    
    def _validate_dual_success(self, spot_result: Dict, futures_result: Dict) -> bool:
        """验证双边执行结果是否都成功"""
        return spot_result.get('success', False) and futures_result.get('success', False)
    
    async def _finalize_successful_trade(self, spot_result: Dict, futures_result: Dict, 
                                       execution_method: str) -> tuple:
        """最终确认成功交易并返回结果"""
        self.logger.info(f"🎯 确认套利交易成功 (方式: {execution_method})")
        
        # 添加执行方式标记
        spot_result_final = spot_result.copy()
        futures_result_final = futures_result.copy()
        spot_result_final['execution_method'] = execution_method
        futures_result_final['execution_method'] = execution_method
        
        # 记录成功交易的关键信息
        self.logger.info(f"📊 成交详情: 现货={spot_result['filled_size']}, 期货={futures_result['filled_size']}")
        
        return (spot_result_final, futures_result_final)
    
    async def _activate_hedge_rollback_system(self, spot_result: Dict, futures_result: Dict,
                                            spot_params: Dict, futures_params: Dict) -> Dict[str, Any]:
        """
        激活对冲回滚安全网 - 系统的核心安全保障
        
        这是防止单边交易的最后防线，必须100%可靠
        """
        self.logger.info("🛡️ 激活对冲回滚安全网")
        
        try:
            # 步骤1: 快速诊断失败类型
            failure_diagnosis = self._diagnose_execution_failure(spot_result, futures_result)
            self.logger.info(f"🔍 故障诊断: {failure_diagnosis['type']} - {failure_diagnosis['description']}")
            
            # 步骤2: 根据失败类型选择回滚策略
            rollback_strategy = self._select_rollback_strategy(failure_diagnosis, spot_result, futures_result)
            self.logger.info(f"🎯 选择回滚策略: {rollback_strategy['name']}")
            
            # 步骤3: 执行回滚操作
            rollback_result = await self._execute_rollback_strategy(
                rollback_strategy, spot_result, futures_result, spot_params, futures_params
            )
            
            if rollback_result['success']:
                self.logger.info("✅ 对冲回滚执行成功")
                return {
                    'success': True,
                    'strategy_used': rollback_strategy['name'],
                    'final_results': rollback_result['final_results'],
                    'rollback_details': rollback_result
                }
            else:
                # 步骤4: 第一次回滚失败，尝试应急策略
                self.logger.warning("⚠️ 第一次回滚失败，尝试应急策略")
                emergency_result = await self._execute_emergency_rollback(
                    spot_result, futures_result, spot_params, futures_params
                )
                
                if emergency_result['success']:
                    self.logger.info("✅ 应急回滚成功")
                    return {
                        'success': True,
                        'strategy_used': 'emergency_rollback',
                        'final_results': emergency_result['final_results'],
                        'rollback_details': emergency_result
                    }
                else:
                    self.logger.error("❌ 所有回滚策略都失败")
                    return {
                        'success': False,
                        'error': '对冲回滚彻底失败',
                        'failure_details': {
                            'primary_rollback': rollback_result,
                            'emergency_rollback': emergency_result
                        }
                    }
                    
        except Exception as e:
            self.logger.critical(f"🔥 对冲回滚系统异常: {e}")
            return {
                'success': False,
                'error': f'回滚系统异常: {str(e)}'
            }
    
    def _diagnose_execution_failure(self, spot_result: Dict, futures_result: Dict) -> Dict[str, Any]:
        """诊断执行失败的类型"""
        spot_success = spot_result.get('success', False)
        futures_success = futures_result.get('success', False)
        
        if not spot_success and not futures_success:
            return {
                'type': 'BOTH_FAILED',
                'description': '双边订单都失败',
                'risk_level': 'LOW',
                'action_needed': 'none'
            }
        elif spot_success and not futures_success:
            return {
                'type': 'SPOT_ONLY_SUCCESS',
                'description': '现货成交，期货失败',
                'risk_level': 'HIGH',
                'action_needed': 'hedge_spot_position'
            }
        elif not spot_success and futures_success:
            return {
                'type': 'FUTURES_ONLY_SUCCESS',
                'description': '期货成交，现货失败',
                'risk_level': 'HIGH',
                'action_needed': 'hedge_futures_position'
            }
        else:
            # 理论上不应该到达这里
            return {
                'type': 'UNKNOWN',
                'description': '未知失败类型',
                'risk_level': 'CRITICAL',
                'action_needed': 'emergency_analysis'
            }
    
    def _select_rollback_strategy(self, diagnosis: Dict, spot_result: Dict, futures_result: Dict) -> Dict[str, Any]:
        """根据诊断结果选择最佳回滚策略"""
        failure_type = diagnosis['type']
        
        if failure_type == 'BOTH_FAILED':
            return {
                'name': 'NO_ACTION_NEEDED',
                'description': '双边失败，无需回滚',
                'method': 'none'
            }
        elif failure_type == 'SPOT_ONLY_SUCCESS':
            filled_size = spot_result.get('filled_size', 0)
            return {
                'name': 'HEDGE_SPOT_POSITION',
                'description': f'对冲现货持仓: {filled_size}',
                'method': 'market_sell_spot',
                'target_size': filled_size
            }
        elif failure_type == 'FUTURES_ONLY_SUCCESS':
            filled_size = futures_result.get('filled_size', 0)
            return {
                'name': 'HEDGE_FUTURES_POSITION',
                'description': f'对冲期货持仓: {filled_size}',
                'method': 'market_close_futures',
                'target_size': filled_size
            }
        else:
            return {
                'name': 'EMERGENCY_ANALYSIS',
                'description': '紧急分析和处理',
                'method': 'emergency'
            }
    
    async def _execute_rollback_strategy(self, strategy: Dict, spot_result: Dict, futures_result: Dict,
                                       spot_params: Dict, futures_params: Dict) -> Dict[str, Any]:
        """执行选定的回滚策略"""
        if strategy['name'] == 'NO_ACTION_NEEDED':
            return {
                'success': True,
                'final_results': (
                    {"success": False, "error": "双边执行失败", "filled_size": 0, "avg_price": 0},
                    {"success": False, "error": "双边执行失败", "filled_size": 0, "avg_price": 0}
                )
            }
        
        elif strategy['name'] == 'HEDGE_SPOT_POSITION':
            return await self._hedge_spot_position(strategy, spot_result, spot_params)
        
        elif strategy['name'] == 'HEDGE_FUTURES_POSITION':
            return await self._hedge_futures_position(strategy, futures_result, futures_params)
        
        else:
            return await self._execute_emergency_analysis(strategy, spot_result, futures_result)
    
    async def _hedge_spot_position(self, strategy: Dict, spot_result: Dict, spot_params: Dict) -> Dict[str, Any]:
        """对冲现货持仓 - 市价卖出多余现货"""
        try:
            target_size = strategy['target_size']
            self.logger.info(f"🔄 对冲现货持仓: 市价卖出 {target_size}")
            
            # 执行市价卖出
            hedge_result = await self.place_order(
                inst_id=spot_params["inst_id"],
                trade_mode=spot_params["trade_mode"],
                side="sell",
                ord_type="market",
                sz=f"{target_size:.8f}".rstrip('0').rstrip('.')
            )
            
            if hedge_result.get('code') == '0':
                order_id = hedge_result['data'][0]['ordId']
                
                # 等待对冲成交
                hedge_fill = await self._wait_for_order_fill(order_id, spot_params["inst_id"], 5)
                
                if hedge_fill['success']:
                    self.logger.info(f"✅ 现货对冲成功: 卖出 {hedge_fill['filled_size']}")
                    return {
                        'success': True,
                        'final_results': (
                            {"success": False, "error": "已对冲现货持仓", "filled_size": 0, "avg_price": 0},
                            {"success": False, "error": "期货执行失败", "filled_size": 0, "avg_price": 0}
                        ),
                        'hedge_details': hedge_fill
                    }
                else:
                    return {'success': False, 'error': f"现货对冲未成交: {hedge_fill.get('error')}"}
            else:
                return {'success': False, 'error': f"现货对冲下单失败: {hedge_result.get('msg')}"}
                
        except Exception as e:
            return {'success': False, 'error': f"现货对冲异常: {str(e)}"}
    
    async def _hedge_futures_position(self, strategy: Dict, futures_result: Dict, futures_params: Dict) -> Dict[str, Any]:
        """对冲期货持仓 - 市价平仓多余期货"""
        try:
            target_size = strategy['target_size']
            # 确定平仓方向（与原订单相反）
            original_side = futures_params["side"]
            hedge_side = "buy" if original_side == "sell" else "sell"
            
            self.logger.info(f"🔄 对冲期货持仓: 市价{hedge_side} {target_size}")
            
            # 执行市价平仓
            hedge_result = await self.place_order(
                inst_id=futures_params["inst_id"],
                trade_mode=futures_params["trade_mode"],
                side=hedge_side,
                ord_type="market",
                sz=f"{target_size:.8f}".rstrip('0').rstrip('.')
            )
            
            if hedge_result.get('code') == '0':
                order_id = hedge_result['data'][0]['ordId']
                
                # 等待对冲成交
                hedge_fill = await self._wait_for_order_fill(order_id, futures_params["inst_id"], 5)
                
                if hedge_fill['success']:
                    self.logger.info(f"✅ 期货对冲成功: {hedge_side} {hedge_fill['filled_size']}")
                    return {
                        'success': True,
                        'final_results': (
                            {"success": False, "error": "现货执行失败", "filled_size": 0, "avg_price": 0},
                            {"success": False, "error": "已对冲期货持仓", "filled_size": 0, "avg_price": 0}
                        ),
                        'hedge_details': hedge_fill
                    }
                else:
                    return {'success': False, 'error': f"期货对冲未成交: {hedge_fill.get('error')}"}
            else:
                return {'success': False, 'error': f"期货对冲下单失败: {hedge_result.get('msg')}"}
                
        except Exception as e:
            return {'success': False, 'error': f"期货对冲异常: {str(e)}"}
    
    async def _execute_emergency_rollback(self, spot_result: Dict, futures_result: Dict,
                                        spot_params: Dict, futures_params: Dict) -> Dict[str, Any]:
        """应急回滚 - 当常规回滚失败时的最后手段"""
        self.logger.warning("🆘 执行应急回滚策略")
        
        try:
            # 获取当前持仓状态
            balance_check = await self._check_position_balance(spot_params, futures_params)
            
            if balance_check['balanced']:
                self.logger.info("✅ 持仓已平衡，无需应急处理")
                return {
                    'success': True,
                    'final_results': (
                        {"success": False, "error": "已确认无单边风险", "filled_size": 0, "avg_price": 0},
                        {"success": False, "error": "已确认无单边风险", "filled_size": 0, "avg_price": 0}
                    )
                }
            
            # 如果持仓不平衡，强制平仓所有相关仓位
            self.logger.warning("🔥 检测到持仓不平衡，执行强制平仓")
            force_close_result = await self._force_close_all_positions(spot_params, futures_params)
            
            return {
                'success': force_close_result['success'],
                'final_results': (
                    {"success": False, "error": "已强制平仓", "filled_size": 0, "avg_price": 0},
                    {"success": False, "error": "已强制平仓", "filled_size": 0, "avg_price": 0}
                ),
                'force_close_details': force_close_result
            }
            
        except Exception as e:
            self.logger.critical(f"应急回滚异常: {e}")
            return {'success': False, 'error': f"应急回滚异常: {str(e)}"}
    
    async def _force_close_all_positions(self, spot_params: Dict, futures_params: Dict) -> Dict[str, Any]:
        """强制平仓所有相关仓位"""
        # 这里实现强制平仓逻辑
        # 暂时返回成功（具体实现可根据需要扩展）
        return {'success': True, 'message': '强制平仓完成'}
    
    async def _emergency_system_cleanup(self, spot_params: Dict, futures_params: Dict):
        """紧急系统清理"""
        try:
            self.logger.critical("🚨 执行紧急系统清理")
            # 检查并清理任何异常状态
            await self._check_position_balance(spot_params, futures_params)
        except Exception as e:
            self.logger.critical(f"紧急清理异常: {e}")
    
    async def _ensure_no_partial_positions(self, spot_params: Dict, futures_params: Dict):
        """确保没有部分成交的遗留仓位"""
        try:
            self.logger.debug("🔍 检查是否存在部分成交的遗留仓位")
            
            # 调用更详细的持仓平衡检查
            balance_check = await self._check_position_balance(spot_params, futures_params)
            
            if not balance_check['balanced']:
                self.logger.warning(f"⚠️ 检测到仓位不平衡: {balance_check['details']}")
                
                # 如果有不平衡，尝试自动修正
                if balance_check.get('auto_fix_possible', False):
                    fix_result = await self._auto_fix_position_imbalance(balance_check)
                    if fix_result['success']:
                        self.logger.info("✅ 仓位不平衡已自动修正")
                    else:
                        self.logger.error(f"❌ 仓位不平衡修正失败: {fix_result['error']}")
            else:
                self.logger.debug("✅ 仓位平衡检查通过")
            
        except Exception as e:
            self.logger.warning(f"⚠️ 仓位状态检查失败: {e}")
    
    async def _check_position_balance(self, spot_params: Dict, futures_params: Dict) -> Dict[str, Any]:
        """检查现货和期货持仓的平衡状态"""
        try:
            # 获取账户余额和持仓
            balance_result = await self.get_balance()
            positions_result = await self.get_positions("SWAP")
            
            # 提取相关资产的信息
            spot_currency = spot_params["inst_id"].split('-')[0]  # 例如：ETH-USDT -> ETH
            futures_inst_id = futures_params["inst_id"]
            
            spot_balance = 0.0
            futures_position = 0.0
            
            # 解析现货余额
            if balance_result.get('code') == '0':
                for balance_info in balance_result.get('data', []):
                    for detail in balance_info.get('details', []):
                        if detail.get('ccy') == spot_currency:
                            spot_balance = float(detail.get('availBal', '0'))
                            break
            
            # 解析期货持仓
            if positions_result.get('code') == '0':
                for position in positions_result.get('data', []):
                    if position.get('instId') == futures_inst_id:
                        # 使用持仓数量，注意正负号
                        pos_size = float(position.get('pos', '0'))
                        futures_position = abs(pos_size)  # 取绝对值
                        break
            
            # 检查平衡状态
            imbalance_threshold = 0.001  # 0.1% 的容差
            balance_diff = abs(spot_balance - futures_position)
            max_position = max(spot_balance, futures_position)
            
            is_balanced = True
            imbalance_ratio = 0.0
            
            if max_position > 0:
                imbalance_ratio = balance_diff / max_position
                is_balanced = imbalance_ratio <= imbalance_threshold
            
            return {
                'balanced': is_balanced,
                'spot_balance': spot_balance,
                'futures_position': futures_position,
                'imbalance_amount': balance_diff,
                'imbalance_ratio': imbalance_ratio,
                'threshold': imbalance_threshold,
                'auto_fix_possible': balance_diff > 0 and balance_diff < max_position * 0.1,  # 不平衡但可修正
                'details': {
                    'spot_currency': spot_currency,
                    'futures_inst_id': futures_inst_id,
                    'spot_balance': spot_balance,
                    'futures_position': futures_position
                }
            }
            
        except Exception as e:
            self.logger.error(f"持仓平衡检查异常: {e}")
            return {
                'balanced': True,  # 检查失败时假设平衡，避免误报
                'error': str(e)
            }
    
    async def _auto_fix_position_imbalance(self, balance_check: Dict) -> Dict[str, Any]:
        """自动修正轻微的仓位不平衡"""
        try:
            spot_balance = balance_check['spot_balance']
            futures_position = balance_check['futures_position']
            imbalance_amount = balance_check['imbalance_amount']
            
            self.logger.info(f"🔧 尝试自动修正仓位不平衡: 差额={imbalance_amount:.6f}")
            
            # 这里可以实现具体的修正逻辑
            # 例如：通过小额市价单来平衡仓位
            
            # 暂时返回成功（具体实现可根据需要扩展）
            return {
                'success': True,
                'method': 'auto_balance',
                'corrected_amount': imbalance_amount
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _execute_traditional_orders(self, spot_params: Dict, futures_params: Dict) -> tuple:
        """传统订单执行（备用）"""
        self.logger.info("使用传统订单执行模式")
        
        # 创建并发任务
        spot_task = asyncio.create_task(
            self.place_order(**spot_params),
            name="spot_order"
        )
        
        futures_task = asyncio.create_task(
            self.place_order(**futures_params),
            name="futures_order"
        )
        
        try:
            # 并发等待两个订单完成
            spot_result, futures_result = await asyncio.gather(
                spot_task, futures_task, return_exceptions=True
            )
            
            # 转换为统一格式
            spot_success = not isinstance(spot_result, Exception) and spot_result.get('code') == '0'
            futures_success = not isinstance(futures_result, Exception) and futures_result.get('code') == '0'
            
            if spot_success and futures_success:
                self.logger.info("传统套利订单对执行成功")
            else:
                self.logger.error(f"传统套利订单执行失败 - 现货: {spot_success}, 期货: {futures_success}")
            
            return spot_result, futures_result
            
        except Exception as e:
            self.logger.error(f"传统套利订单执行异常: {e}")
            raise
    
    async def get_positions(self, inst_type: str = "SWAP") -> Dict[str, Any]:
        """异步获取持仓"""
        if not self.http_session:
            raise RuntimeError("HTTP会话未初始化")
        
        request_path = f"/api/v5/account/positions?instType={inst_type}"
        headers = self._get_headers("GET", request_path)
        
        try:
            async with self.http_session.get(
                f"{self.base_url}{request_path}",
                headers=headers,
                proxy=self.proxy
            ) as response:
                result = await response.json()
                return result
                
        except Exception as e:
            self.logger.error(f"获取持仓失败: {e}")
            raise
    
    async def get_position_risk(self, inst_id: str) -> Optional[Dict[str, Any]]:
        """获取特定仓位的风险信息"""
        try:
            positions_data = await self.get_positions()

            if positions_data.get('code') == '0':
                for position in positions_data.get('data', []):
                    if position.get('instId') == inst_id:
                        return position

            return None

        except Exception as e:
            self.logger.error(f"获取仓位风险信息失败: {e}")
            return None

    async def get_funding_rate(self, inst_id: str) -> Optional[Dict[str, Any]]:
        """获取永续合约当前资金费率"""
        if not self.http_session:
            raise RuntimeError("HTTP会话未初始化")

        request_path = f"/api/v5/public/funding-rate?instId={inst_id}"

        try:
            async with self.http_session.get(
                f"{self.base_url}{request_path}",
                proxy=self.proxy
            ) as response:
                result = await response.json()

                if result.get('code') == '0' and result.get('data'):
                    return result['data'][0]
                else:
                    self.logger.warning(f"获取{inst_id}资金费率失败: {result.get('msg', 'Unknown error')}")
                    return None

        except Exception as e:
            self.logger.error(f"请求{inst_id}资金费率失败: {e}")
            return None

    async def get_ticker(self, inst_id: str) -> Optional[Dict[str, Any]]:
        """获取单个产品行情信息"""
        if not self.http_session:
            raise RuntimeError("HTTP会话未初始化")

        request_path = f"/api/v5/market/ticker?instId={inst_id}"

        try:
            async with self.http_session.get(
                f"{self.base_url}{request_path}",
                proxy=self.proxy
            ) as response:
                result = await response.json()

                if result.get('code') == '0' and result.get('data'):
                    return result['data'][0]
                else:
                    self.logger.warning(f"获取{inst_id}行情信息失败: {result.get('msg', 'Unknown error')}")
                    return None

        except Exception as e:
            self.logger.error(f"请求{inst_id}行情信息失败: {e}")
            return None
    
    async def cancel_order(self, inst_id: str, ord_id: str) -> Dict[str, Any]:
        """异步撤单"""
        if not self.http_session:
            raise RuntimeError("HTTP会话未初始化")
        
        request_path = "/api/v5/trade/cancel-order"
        
        cancel_data = {
            "instId": inst_id,
            "ordId": ord_id
        }
        
        body = json.dumps(cancel_data)
        headers = self._get_headers("POST", request_path, body)
        
        try:
            async with self.http_session.post(
                f"{self.base_url}{request_path}",
                data=body,
                headers=headers,
                proxy=self.proxy
            ) as response:
                result = await response.json()
                return result
                
        except Exception as e:
            self.logger.error(f"撤单失败: {e}")
            raise
    
    async def get_order_status(self, inst_id: str, ord_id: str) -> Optional[Dict[str, Any]]:
        """查询订单状态"""
        if not self.http_session:
            raise RuntimeError("HTTP会话未初始化")
        
        request_path = f"/api/v5/trade/order?instId={inst_id}&ordId={ord_id}"
        headers = self._get_headers("GET", request_path)
        
        try:
            async with self.http_session.get(
                f"{self.base_url}{request_path}",
                headers=headers,
                proxy=self.proxy
            ) as response:
                result = await response.json()
                
                if result.get('code') == '0' and result.get('data'):
                    return result['data'][0]
                return None
                
        except Exception as e:
            self.logger.error(f"查询订单状态失败: {e}")
            return None
    
    async def get_account_balance(self) -> Dict[str, Any]:
        """获取账户余额"""
        if not self.http_session:
            raise RuntimeError("HTTP会话未初始化")
        
        request_path = "/api/v5/account/balance"
        headers = self._get_headers("GET", request_path)
        
        try:
            async with self.http_session.get(
                f"{self.base_url}{request_path}",
                headers=headers,
                proxy=self.proxy
            ) as response:
                result = await response.json()
                return result
                
        except Exception as e:
            self.logger.error(f"获取账户余额失败: {e}")
            raise
    
    async def get_balance(self) -> Dict[str, Any]:
        """获取余额信息（兼容方法，调用get_account_balance）"""
        return await self.get_account_balance()
    
    async def get_order_book(self, inst_id: str, sz: int = 5) -> Optional[Dict[str, Any]]:
        """获取实时盘口深度数据 - 集成速率限制器"""
        if not self.http_session:
            raise RuntimeError("HTTP会话未初始化")
        
        # 应用速率限制 - 查询API
        if not await self.query_rate_limiter.wait_for_tokens(1, timeout=5.0):
            self.logger.warning("查询速率限制超时，跳过本次请求")
            return None
        
        request_path = f"/api/v5/market/books?instId={inst_id}&sz={sz}"
        
        try:
            async with self.http_session.get(
                f"{self.base_url}{request_path}",
                proxy=self.proxy
            ) as response:
                result = await response.json()
                
                if result.get('code') == '0' and result.get('data'):
                    return result['data'][0]
                else:
                    self.logger.warning(f"获取{inst_id}盘口数据失败: {result.get('msg', 'Unknown error')}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"请求{inst_id}盘口数据失败: {e}")
            return None
            
    async def _get_instrument_precision(self, inst_id: str) -> Dict[str, Any]:
        """获取交易对精度规格信息，带缓存机制"""
        # 检查缓存
        if hasattr(self, '_precision_cache') and inst_id in self._precision_cache:
            return self._precision_cache[inst_id]
        
        if not hasattr(self, '_precision_cache'):
            self._precision_cache = {}
        
        if not self.http_session:
            self.logger.warning("HTTP会话未初始化，使用默认精度")
            return {'minSz': 1e-8, 'tickSz': 1e-8}
        
        # 应用速率限制
        if not await self.query_rate_limiter.wait_for_tokens(1, timeout=5.0):
            self.logger.warning("查询速率限制超时，使用默认精度")
            return {'minSz': 1e-8, 'tickSz': 1e-8}
        
        request_path = f"/api/v5/public/instruments?instType=SPOT&instId={inst_id}"
        
        try:
            async with self.http_session.get(
                f"{self.base_url}{request_path}",
                proxy=self.proxy
            ) as response:
                result = await response.json()
                
                if result.get('code') == '0' and result.get('data'):
                    instrument_data = result['data'][0]
                    precision_info = {
                        'minSz': float(instrument_data.get('minSz', 1e-8)),
                        'tickSz': float(instrument_data.get('tickSz', 1e-8)),
                        'lotSz': float(instrument_data.get('lotSz', 1e-8)),
                        'ctVal': float(instrument_data.get('ctVal', 1)),
                        'instId': inst_id
                    }
                    
                    # 缓存结果（12小时有效）
                    self._precision_cache[inst_id] = precision_info
                    self.logger.debug(f"📏 缓存交易对精度: {inst_id} -> minSz={precision_info['minSz']}")
                    
                    return precision_info
                else:
                    self.logger.warning(f"获取{inst_id}精度规格失败: {result.get('msg', 'unknown error')}")
                    return {'minSz': 1e-8, 'tickSz': 1e-8}
                    
        except Exception as e:
            self.logger.error(f"查询{inst_id}精度规格异常: {e}")
            return {'minSz': 1e-8, 'tickSz': 1e-8}
    
    async def get_best_prices(self, inst_id: str) -> Optional[Dict[str, float]]:
        """获取最佳买卖价格 - WebSocket数据优先，REST备用"""
        # 第一优先：检查WebSocket缓存数据
        async with self.orderbook_lock:
            cached_data = self.orderbook_cache.get(inst_id)
            
            if cached_data:
                # 检查数据新鲜度（1秒内的数据认为是新鲜的）
                current_time = int(time.time() * 1000)
                data_age = current_time - int(cached_data['timestamp'])
                
                if data_age < 1000:  # 1秒内
                    self.logger.debug(f"⚡ 使用WebSocket缓存盘口 {inst_id}: {data_age}ms延迟")
                    best_bid = cached_data['best_bid']
                    best_ask = cached_data['best_ask']
                    
                    return {
                        'bid': best_bid,
                        'ask': best_ask,
                        'spread': best_ask - best_bid,
                        'mid': (best_bid + best_ask) / 2
                    }
                else:
                    self.logger.debug(f"📡 WebSocket数据过期 {inst_id}: {data_age}ms，回退到REST")
        
        # 第二优先：确保WebSocket订阅
        if inst_id not in self.orderbook_subscriptions:
            try:
                await self.subscribe_order_books([inst_id])
                self.orderbook_subscriptions.add(inst_id)
                self.logger.info(f"🔔 自动订阅盘口数据: {inst_id}")
            except Exception as e:
                self.logger.warning(f"自动订阅盘口失败: {e}")
        
        # 第三优先：REST API回退
        self.logger.debug(f"📞 使用REST获取盘口 {inst_id}")
        order_book = await self.get_order_book(inst_id, sz=1)
        if not order_book:
            return None
        
        try:
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])
            
            if not bids or not asks:
                return None
            
            best_bid = float(bids[0][0])  # 最佳买价
            best_ask = float(asks[0][0])  # 最佳卖价
            
            return {
                'bid': best_bid,
                'ask': best_ask,
                'spread': best_ask - best_bid,
                'mid': (best_bid + best_ask) / 2
            }
            
        except (IndexError, ValueError, TypeError) as e:
            self.logger.error(f"解析{inst_id}盘口价格失败: {e}")
            return None
    
    async def ensure_orderbook_subscription(self, inst_ids: List[str]):
        """确保WebSocket盘口数据订阅 - 用于批量预订阅"""
        missing_subscriptions = [inst_id for inst_id in inst_ids if inst_id not in self.orderbook_subscriptions]
        
        if missing_subscriptions:
            try:
                await self.subscribe_order_books(missing_subscriptions)
                self.orderbook_subscriptions.update(missing_subscriptions)
                self.logger.info(f"🔔 批量订阅盘口数据: {missing_subscriptions}")
            except Exception as e:
                self.logger.error(f"批量订阅盘口失败: {e}")
    
    async def atomic_order_with_live_pricing(self, inst_id: str, trade_mode: str, side: str, 
                                           ord_type: str, sz: str, pricing_strategy: str = "maker",
                                           price_offset: float = 0.0, **kwargs) -> Dict[str, Any]:
        """原子化下单 - 基于WebSocket实时数据的零延迟定价"""
        try:
            # 确保已订阅盘口数据
            if inst_id not in self.orderbook_subscriptions:
                await self.ensure_orderbook_subscription([inst_id])
                # 等待短暂时间让WebSocket数据到达
                await asyncio.sleep(0.1)
            
            # 原子化操作：获取当前盘口快照并立即计算价格
            async with self.orderbook_lock:
                orderbook_snapshot = self.orderbook_cache.get(inst_id)
                
                if not orderbook_snapshot:
                    # 没有WebSocket数据，回退到REST
                    self.logger.warning(f"⚠️ [{inst_id}] 无WebSocket盘口数据，回退到REST")
                    return await self._fallback_to_rest_order(inst_id, trade_mode, side, ord_type, sz, **kwargs)
                
                # 检查数据新鲜度
                current_time = int(time.time() * 1000)
                data_age = current_time - int(orderbook_snapshot['timestamp'])
                
                if data_age > 2000:  # 数据超过2秒
                    self.logger.warning(f"⚠️ [{inst_id}] WebSocket数据过期({data_age}ms)，回退到REST")
                    return await self._fallback_to_rest_order(inst_id, trade_mode, side, ord_type, sz, **kwargs)
                
                # 原子计算订单价格
                best_bid = orderbook_snapshot['best_bid']
                best_ask = orderbook_snapshot['best_ask']
                
                # 根据策略计算价格
                if pricing_strategy == "maker":
                    # Maker策略：买单挂买一价，卖单挂卖一价
                    if side == "buy":
                        price = best_bid + price_offset
                    else:  # sell
                        price = best_ask + price_offset
                        
                elif pricing_strategy == "taker":
                    # Taker策略：买单吃卖一价，卖单吃买一价
                    if side == "buy":
                        price = best_ask + price_offset
                    else:  # sell
                        price = best_bid + price_offset
                        
                elif pricing_strategy == "mid":
                    # 中间价策略
                    mid_price = (best_bid + best_ask) / 2
                    price = mid_price + price_offset
                    
                else:
                    raise ValueError(f"不支持的定价策略: {pricing_strategy}")
                
                # 记录原子化操作的时间窗口
                snapshot_age = data_age
                
            # 立即下单 - 最小化竞态条件窗口
            order_result = await self.place_order(
                inst_id=inst_id,
                trade_mode=trade_mode,
                side=side,
                ord_type=ord_type,
                sz=sz,
                px=str(round(price, 8)),  # 确保价格精度
                **kwargs
            )
            
            # 记录原子化操作统计
            if order_result.get('code') == '0':
                self.logger.debug(
                    f"⚡ 原子化下单成功 [{inst_id}]: "
                    f"策略={pricing_strategy}, 价格={price:.8f}, "
                    f"快照延迟={snapshot_age}ms"
                )
                order_result['atomic_pricing'] = {
                    'strategy': pricing_strategy,
                    'calculated_price': price,
                    'snapshot_age_ms': snapshot_age,
                    'best_bid': best_bid,
                    'best_ask': best_ask
                }
            else:
                self.logger.warning(
                    f"❌ 原子化下单失败 [{inst_id}]: {order_result.get('msg', 'Unknown error')}"
                )
            
            return order_result
            
        except Exception as e:
            self.logger.error(f"原子化下单异常 [{inst_id}]: {e}")
            # 异常时回退到传统方法
            return await self._fallback_to_rest_order(inst_id, trade_mode, side, ord_type, sz, **kwargs)
    
    async def _fallback_to_rest_order(self, inst_id: str, trade_mode: str, side: str, 
                                     ord_type: str, sz: str, **kwargs) -> Dict[str, Any]:
        """回退到传统REST下单方法"""
        self.logger.debug(f"📞 [{inst_id}] 使用传统REST下单方法")
        
        # 如果没有指定价格，获取当前盘口价格
        if 'px' not in kwargs and ord_type in ['limit', 'post_only']:
            best_prices = await self.get_best_prices(inst_id)
            if best_prices:
                # 使用简单的默认定价策略
                if side == "buy":
                    price = best_prices['bid']
                else:
                    price = best_prices['ask']
                kwargs['px'] = str(round(price, 8))
        
        return await self.place_order(inst_id, trade_mode, side, ord_type, sz, **kwargs)
    
    async def get_orderbook_cache_status(self) -> Dict[str, Any]:
        """获取盘口缓存状态"""
        async with self.orderbook_lock:
            current_time = int(time.time() * 1000)
            cache_status = {}
            
            for inst_id, data in self.orderbook_cache.items():
                age = current_time - int(data['timestamp'])
                cache_status[inst_id] = {
                    'age_ms': age,
                    'fresh': age < 1000,
                    'best_bid': data['best_bid'],
                    'best_ask': data['best_ask']
                }
            
            return {
                'subscriptions': list(self.orderbook_subscriptions),
                'cached_instruments': cache_status,
                'total_subscriptions': len(self.orderbook_subscriptions),
                'fresh_data_count': sum(1 for status in cache_status.values() if status['fresh'])
            }
    
    async def initialize_instrument_specs(self) -> bool:
        """初始化交易对规格信息"""
        try:
            self.logger.info("开始初始化交易对规格信息...")
            
            # 获取现货交易对规格
            spot_specs = await self._fetch_instrument_specs("SPOT")
            futures_specs = await self._fetch_instrument_specs("SWAP")
            
            # 缓存现货规格
            if spot_specs:
                for spec in spot_specs:
                    inst_id = spec.get('instId')
                    if inst_id == self.config.SPOT_ID:
                        self.instrument_specs[inst_id] = {
                            'instType': spec.get('instType'),
                            'instId': inst_id,
                            'baseCcy': spec.get('baseCcy'),
                            'quoteCcy': spec.get('quoteCcy'),
                            'settleCcy': spec.get('settleCcy'),
                            'ctVal': spec.get('ctVal', '1'),  # 合约面值
                            'ctMult': spec.get('ctMult', '1'),  # 合约乘数
                            'tickSz': spec.get('tickSz'),   # 价格精度
                            'lotSz': spec.get('lotSz'),     # 最小交易数量
                            'minSz': spec.get('minSz'),     # 最小下单数量
                            'maxMktSz': spec.get('maxMktSz'),  # 市价单最大数量
                            'maxLmtSz': spec.get('maxLmtSz'),  # 限价单最大数量
                            'maxTwapSz': spec.get('maxTwapSz'), # TWAP最大数量
                            'state': spec.get('state'),      # 交易状态
                            'listTime': spec.get('listTime'), # 上市时间
                        }
                        self.logger.info(f"现货规格已缓存: {inst_id}")
                        break
            
            # 缓存期货规格
            if futures_specs:
                for spec in futures_specs:
                    inst_id = spec.get('instId')
                    if inst_id == self.config.FUTURES_ID:
                        self.instrument_specs[inst_id] = {
                            'instType': spec.get('instType'),
                            'instId': inst_id,
                            'uly': spec.get('uly'),         # 标的指数
                            'baseCcy': spec.get('baseCcy'),
                            'quoteCcy': spec.get('quoteCcy'),
                            'settleCcy': spec.get('settleCcy'),
                            'ctVal': spec.get('ctVal', '1'),  # 合约面值
                            'ctMult': spec.get('ctMult', '1'),  # 合约乘数
                            'ctValCcy': spec.get('ctValCcy'), # 合约面值计价货币
                            'tickSz': spec.get('tickSz'),   # 价格精度
                            'lotSz': spec.get('lotSz'),     # 最小交易数量
                            'minSz': spec.get('minSz'),     # 最小下单数量
                            'maxMktSz': spec.get('maxMktSz'),  # 市价单最大数量
                            'maxLmtSz': spec.get('maxLmtSz'),  # 限价单最大数量
                            'maxTwapSz': spec.get('maxTwapSz'), # TWAP最大数量
                            'state': spec.get('state'),      # 交易状态
                            'listTime': spec.get('listTime'), # 上市时间
                            'expTime': spec.get('expTime'),   # 到期时间
                            'lever': spec.get('lever'),       # 最大杠杆倍数
                        }
                        self.logger.info(f"期货规格已缓存: {inst_id}")
                        break
            
            # 验证必要的规格是否已获取
            if self.config.SPOT_ID not in self.instrument_specs:
                self.logger.error(f"未能获取现货交易对规格: {self.config.SPOT_ID}")
                return False
            
            if self.config.FUTURES_ID not in self.instrument_specs:
                self.logger.error(f"未能获取期货交易对规格: {self.config.FUTURES_ID}")
                return False
            
            self.logger.info(f"交易对规格初始化完成: {list(self.instrument_specs.keys())}")
            
            # 记录关键规格信息
            for inst_id, spec in self.instrument_specs.items():
                self.logger.info(
                    f"📋 {inst_id} 规格: "
                    f"合约面值={spec.get('ctVal', 'N/A')}, "
                    f"最小交易量={spec.get('lotSz', 'N/A')}, "
                    f"价格精度={spec.get('tickSz', 'N/A')}, "
                    f"最小下单量={spec.get('minSz', 'N/A')}"
                )
            
            return True
            
        except Exception as e:
            self.logger.error(f"初始化交易对规格失败: {e}")
            return False
    
    async def _fetch_instrument_specs(self, inst_type: str) -> Optional[List[Dict[str, Any]]]:
        """获取指定类型的交易对规格"""
        if not self.http_session:
            raise RuntimeError("HTTP会话未初始化")
        
        request_path = f"/api/v5/public/instruments?instType={inst_type}"
        
        try:
            async with self.http_session.get(
                f"{self.base_url}{request_path}",
                proxy=self.proxy
            ) as response:
                result = await response.json()
                
                if result.get('code') == '0':
                    return result.get('data', [])
                else:
                    self.logger.error(f"获取{inst_type}规格失败: {result.get('msg')}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"请求{inst_type}规格失败: {e}")
            return None
    
    def get_instrument_spec(self, inst_id: str) -> Optional[Dict[str, Any]]:
        """安全获取交易对规格信息"""
        return self.instrument_specs.get(inst_id)
    
    def get_contract_value(self, inst_id: str) -> float:
        """获取合约面值"""
        spec = self.get_instrument_spec(inst_id)
        if spec:
            try:
                return float(spec.get('ctVal', '1'))
            except (ValueError, TypeError):
                return 1.0
        return 1.0
    
    def get_lot_size(self, inst_id: str) -> float:
        """获取最小交易单位"""
        spec = self.get_instrument_spec(inst_id)
        if spec:
            try:
                return float(spec.get('lotSz', '1'))
            except (ValueError, TypeError):
                return 1.0
        return 1.0
    
    def get_tick_size(self, inst_id: str) -> float:
        """获取价格最小变动单位"""
        spec = self.get_instrument_spec(inst_id)
        if spec:
            try:
                return float(spec.get('tickSz', '0.00001'))
            except (ValueError, TypeError):
                return 0.00001
        return 0.00001
    
    def get_min_size(self, inst_id: str) -> float:
        """获取最小下单数量"""
        spec = self.get_instrument_spec(inst_id)
        if spec:
            try:
                return float(spec.get('minSz', '1'))
            except (ValueError, TypeError):
                return 1.0
        return 1.0
    
    async def place_market_hedge_order(self, inst_id: str, side: str, size: float) -> Dict[str, Any]:
        """执行市价对冲订单"""
        try:
            self.logger.info(f"执行对冲订单: {inst_id} {side} {size}")
            
            # 获取交易模式
            trade_mode = "cash" if "USDT" in inst_id and "SWAP" not in inst_id else "cross"
            
            # 确保订单大小符合规格
            min_size = self.get_min_size(inst_id)
            lot_size = self.get_lot_size(inst_id)
            
            # 调整订单大小
            adjusted_size = max(abs(size), min_size)
            adjusted_size = (adjusted_size // lot_size) * lot_size
            
            if adjusted_size < min_size:
                adjusted_size = min_size
            
            result = await self.place_order(
                inst_id=inst_id,
                trade_mode=trade_mode,
                side=side,
                ord_type="market",
                sz=str(adjusted_size)
            )
            
            self.logger.info(f"对冲订单结果: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"执行对冲订单失败: {e}")
            raise
    
    def register_event_callback(self, event_type: str, callback: Callable):
        """注册事件回调函数"""
        if event_type not in self.event_callbacks:
            self.event_callbacks[event_type] = []
        
        if callback not in self.event_callbacks[event_type]:
            self.event_callbacks[event_type].append(callback)
            self.logger.info(f"✅ 已注册{event_type}事件回调")
    
    def unregister_event_callback(self, event_type: str, callback: Callable):
        """取消注册事件回调函数"""
        if event_type in self.event_callbacks and callback in self.event_callbacks[event_type]:
            self.event_callbacks[event_type].remove(callback)
            self.logger.info(f"❌ 已取消注册{event_type}事件回调")
    
    async def _dispatch_public_message(self, data: Dict[str, Any]):
        """分发公共频道消息"""
        try:
            # 检查消息类型
            if 'arg' in data and 'data' in data:
                channel = data['arg'].get('channel')
                
                # 根据频道类型分发消息
                if channel == 'tickers':
                    await self._trigger_callbacks('ticker', data)
                elif channel in ['books', 'books5', 'books-l2-tbt']:
                    await self._handle_orderbook_update(data)
                    await self._trigger_callbacks('books', data)
                elif channel == 'trades':
                    await self._trigger_callbacks('trades', data)
                elif channel == 'funding-rate':
                    await self._trigger_callbacks('funding-rate', data)
                else:
                    self.logger.debug(f"未处理的公共频道消息: {channel}")
            
            elif data.get('event') == 'subscribe':
                self.logger.info(f"订阅确认: {data.get('arg', {})}")
            else:
                self.logger.debug(f"未处理的公共消息: {data}")
                
        except Exception as e:
            self.logger.error(f"分发公共频道消息失败: {e}")
    
    async def _dispatch_private_message(self, data: Dict[str, Any]):
        """分发私有频道消息"""
        try:
            # 检查消息类型
            if 'arg' in data and 'data' in data:
                channel = data['arg'].get('channel')
                
                # 根据频道类型分发消息
                if channel == 'account':
                    await self._trigger_callbacks('account', data)
                elif channel == 'orders':
                    await self._trigger_callbacks('orders', data)
                elif channel == 'positions':
                    await self._trigger_callbacks('positions', data)
                elif channel == 'balance':
                    await self._trigger_callbacks('balance', data)
                else:
                    self.logger.debug(f"未处理的私有频道消息: {channel}")
            
            elif data.get('event') == 'subscribe':
                self.logger.info(f"私有频道订阅确认: {data.get('arg', {})}")
            elif data.get('event') == 'login':
                self.logger.debug("私有频道登录响应已在认证方法中处理")
            else:
                self.logger.debug(f"未处理的私有消息: {data}")
                
        except Exception as e:
            self.logger.error(f"分发私有频道消息失败: {e}")
    
    async def _handle_orderbook_update(self, data: Dict[str, Any]):
        """处理WebSocket盘口数据更新并缓存"""
        try:
            inst_id = data['arg'].get('instId')
            if not inst_id:
                return
            
            book_data = data.get('data', [])
            if not book_data:
                return
            
            # 获取第一个盘口数据（通常只有一个）
            orderbook = book_data[0]
            
            # 提取买卖盘数据
            bids = orderbook.get('bids', [])
            asks = orderbook.get('asks', [])
            timestamp = orderbook.get('ts', str(int(time.time() * 1000)))
            
            if bids and asks:
                # 缓存最新盘口数据
                async with self.orderbook_lock:
                    self.orderbook_cache[inst_id] = {
                        'bids': bids,
                        'asks': asks, 
                        'timestamp': timestamp,
                        'best_bid': float(bids[0][0]) if bids[0] else 0,
                        'best_ask': float(asks[0][0]) if asks[0] else 0,
                        'bid_size': float(bids[0][1]) if bids[0] else 0,
                        'ask_size': float(asks[0][1]) if asks[0] else 0
                    }
                
                self.logger.debug(f"📈 更新盘口缓存 {inst_id}: bid={bids[0][0]}, ask={asks[0][0]}")
                
        except Exception as e:
            self.logger.error(f"处理盘口数据更新失败: {e}")

    async def _trigger_callbacks(self, event_type: str, data: Dict[str, Any]):
        """触发事件回调"""
        if event_type in self.event_callbacks:
            callbacks = self.event_callbacks[event_type]
            if callbacks:
                # 并发执行所有回调
                tasks = []
                for callback in callbacks:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            tasks.append(asyncio.create_task(callback(data)))
                        else:
                            # 对于同步函数，在线程池中执行
                            loop = asyncio.get_event_loop()
                            tasks.append(loop.run_in_executor(None, callback, data))
                    except Exception as e:
                        self.logger.error(f"创建{event_type}回调任务失败: {e}")
                
                if tasks:
                    # 等待所有回调完成，但不传播异常
                    results = await asyncio.gather(*tasks, return_exceptions=True)
                    for i, result in enumerate(results):
                        if isinstance(result, Exception):
                            self.logger.error(f"{event_type}回调执行失败: {result}")
    
    async def health_check(self) -> bool:
        """双通道健康检查"""
        try:
            # 检查公共频道
            public_healthy = (
                self.ws_public and 
                not getattr(self.ws_public, 'closed', True) and 
                self.is_public_connected
            )
            
            # 检查私有频道
            private_healthy = (
                self.ws_private and 
                not getattr(self.ws_private, 'closed', True) and 
                self.is_private_connected and 
                self.is_private_authenticated
            )
            
            # 检查HTTP会话
            http_healthy = self.http_session and not self.http_session.closed
            
            overall_health = public_healthy and private_healthy and http_healthy
            
            self.logger.debug(
                f"健康状态: 公共={public_healthy}, "
                f"私有={private_healthy}, HTTP={http_healthy}, "
                f"总体={overall_health}"
            )
            
            return overall_health
            
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            return False
    
    def get_connection_status(self) -> Dict[str, Any]:
        """获取连接状态详情"""
        return {
            "public_channel": {
                "connected": self.is_public_connected,
                "websocket_open": self.ws_public and not getattr(self.ws_public, 'closed', True),
                "reconnect_attempts": self.reconnect_attempts.get('public', 0)
            },
            "private_channel": {
                "connected": self.is_private_connected,
                "authenticated": self.is_private_authenticated,
                "websocket_open": self.ws_private and not getattr(self.ws_private, 'closed', True),
                "reconnect_attempts": self.reconnect_attempts.get('private', 0)
            },
            "http_session": {
                "active": self.http_session and not self.http_session.closed
            },
            "event_callbacks": {
                event_type: len(callbacks) 
                for event_type, callbacks in self.event_callbacks.items()
            }
        }
    
    async def initialize_instrument_specs(self) -> bool:
        """初始化所有配置的交易对的合约规格"""
        if self.instrument_specs_initialized:
            return True
            
        try:
            self.logger.info("🔍 正在获取交易对合约规格...")
            
            # 从配置中获取所有启用的交易对
            from config import get_enabled_trading_pairs
            enabled_pairs = get_enabled_trading_pairs()
            
            # 获取现货和期货的所有symbol
            spot_symbols = []
            futures_symbols = []
            for pair in enabled_pairs:
                spot_symbols.append(pair["spot_id"])
                futures_symbols.append(pair["futures_id"])
            
            # 查询现货规格
            spot_specs = await self._get_instrument_specs("SPOT", spot_symbols)
            # 查询期货规格  
            futures_specs = await self._get_instrument_specs("SWAP", futures_symbols)
            
            # 合并规格数据
            self.instrument_specs.update(spot_specs)
            self.instrument_specs.update(futures_specs)
            
            # 记录获取到的规格信息
            self.logger.info(f"✅ 成功获取 {len(self.instrument_specs)} 个交易对的合约规格")
            for inst_id, spec in self.instrument_specs.items():
                self.logger.debug(f"📋 [{inst_id}] 规格: 合约面值={spec.get('ctVal', 'N/A')}, "
                                f"最小变动={spec.get('tickSz', 'N/A')}, "
                                f"最小数量={spec.get('lotSz', 'N/A')}")
            
            self.instrument_specs_initialized = True
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 获取合约规格失败: {e}")
            return False
    
    async def _get_instrument_specs(self, inst_type: str, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """查询指定类型和交易对的合约规格"""
        if not self.http_session:
            raise RuntimeError("HTTP会话未初始化")
            
        specs = {}
        
        try:
            # OKX API: /api/v5/public/instruments
            request_path = f"/api/v5/public/instruments?instType={inst_type}"
            
            # 公共API不需要签名
            async with self.http_session.get(
                f"{self.base_url}{request_path}",
                proxy=self.proxy
            ) as response:
                result = await response.json()
                
            if result.get('code') == '0':
                instruments = result.get('data', [])
                
                # 过滤出配置中的交易对
                for instrument in instruments:
                    inst_id = instrument.get('instId')
                    if inst_id in symbols:
                        specs[inst_id] = {
                            'instId': inst_id,
                            'instType': inst_type,
                            'ctVal': instrument.get('ctVal', '1'),  # 合约面值
                            'ctMult': instrument.get('ctMult', '1'),  # 合约乘数
                            'tickSz': instrument.get('tickSz', '0.01'),  # 最小变动价位
                            'lotSz': instrument.get('lotSz', '1'),  # 最小交易数量
                            'minSz': instrument.get('minSz', '1'),  # 最小订单数量
                            'maxMktSz': instrument.get('maxMktSz', '999999'),  # 市价单最大数量
                            'maxLmtSz': instrument.get('maxLmtSz', '999999'),  # 限价单最大数量
                            'baseCcy': instrument.get('baseCcy', ''),  # 基础货币
                            'quoteCcy': instrument.get('quoteCcy', ''),  # 计价货币
                            'settleCcy': instrument.get('settleCcy', ''),  # 结算货币
                        }
                        
                self.logger.info(f"🔍 获取 {inst_type} 规格: {len(specs)}/{len(symbols)} 个交易对")
            else:
                self.logger.error(f"❌ 查询 {inst_type} 规格失败: {result.get('msg', 'Unknown error')}")
                
        except Exception as e:
            self.logger.error(f"❌ 获取 {inst_type} 合约规格异常: {e}")
            
        return specs
    
    def get_contract_value(self, inst_id: str) -> float:
        """获取合约面值（用于期货数量计算）"""
        if not self.instrument_specs_initialized:
            self.logger.warning(f"⚠️ 合约规格未初始化，使用默认值")
            return 1.0
            
        spec = self.instrument_specs.get(inst_id)
        if spec:
            return float(spec.get('ctVal', '1'))
        else:
            self.logger.warning(f"⚠️ 未找到 {inst_id} 的合约规格，使用默认值")
            return 1.0
    
    def get_tick_size(self, inst_id: str) -> float:
        """获取最小变动价位"""
        if not self.instrument_specs_initialized:
            self.logger.warning(f"⚠️ 合约规格未初始化，使用默认值")
            return 0.01
            
        spec = self.instrument_specs.get(inst_id)
        if spec:
            return float(spec.get('tickSz', '0.01'))
        else:
            self.logger.warning(f"⚠️ 未找到 {inst_id} 的价格精度，使用默认值")
            return 0.01
    
    def get_lot_size(self, inst_id: str) -> float:
        """获取最小交易数量"""
        if not self.instrument_specs_initialized:
            self.logger.warning(f"⚠️ 合约规格未初始化，使用默认值")
            return 1.0
            
        spec = self.instrument_specs.get(inst_id)
        if spec:
            return float(spec.get('lotSz', '1'))
        else:
            self.logger.warning(f"⚠️ 未找到 {inst_id} 的数量精度，使用默认值")
            return 1.0
    
    def calculate_futures_quantity(self, spot_quantity: float, spot_price: float, futures_inst_id: str) -> float:
        """
        根据现货数量和价格计算对应的期货合约数量
        
        Args:
            spot_quantity: 现货数量
            spot_price: 现货价格
            futures_inst_id: 期货合约ID
            
        Returns:
            期货合约数量
        """
        # 计算现货名义价值
        spot_notional = spot_quantity * spot_price
        
        # 获取期货合约面值
        contract_value = self.get_contract_value(futures_inst_id)
        
        # 计算期货合约数量
        # OKX期货合约: 1张合约 = ctVal * 币种数量
        # 例如: ETH-USDT-SWAP, ctVal=0.1, 表示1张合约 = 0.1 ETH
        # 所以: 合约数量 = 现货数量 / ctVal
        futures_quantity = spot_quantity / contract_value
        
        # 调整到最小交易单位
        lot_size = self.get_lot_size(futures_inst_id)
        futures_quantity = round(futures_quantity / lot_size) * lot_size
        
        self.logger.debug(f"💱 数量转换: 现货{spot_quantity} * {spot_price} = {spot_notional:.2f} USDT")
        self.logger.debug(f"💱 合约面值: {contract_value}, 期货数量: {futures_quantity}")
        
        return futures_quantity
    
    async def _align_order_sizes(self, spot_params: Dict, futures_params: Dict) -> tuple:
        """
        预先对齐现货和期货的交易数量，确保严格匹配
        
        根据OKX交易规格，统一调整到两个交易对都能支持的最大匹配数量
        
        Returns:
            (aligned_spot_params, aligned_futures_params)
        """
        try:
            # 获取交易对精度信息
            spot_precision = await self._get_instrument_precision(spot_params["inst_id"])
            futures_precision = await self._get_instrument_precision(futures_params["inst_id"])
            
            # 获取原始数量
            original_spot_size = float(spot_params["sz"])
            original_futures_size = float(futures_params["sz"])
            
            # 获取精度参数
            spot_lot_sz = float(spot_precision.get('lotSz', '0.0001'))
            futures_lot_sz = float(futures_precision.get('lotSz', '0.001'))
            spot_min_sz = float(spot_precision.get('minSz', '0.0001'))
            futures_min_sz = float(futures_precision.get('minSz', '0.001'))
            
            self.logger.debug(f"📏 原始数量: 现货={original_spot_size}, 期货={original_futures_size}")
            self.logger.debug(f"📏 精度规格: 现货lotSz={spot_lot_sz}/minSz={spot_min_sz}, 期货lotSz={futures_lot_sz}/minSz={futures_min_sz}")
            
            # 对于期现套利，现货和期货应该代表相同的标的数量
            # 需要根据合约面值进行换算
            if "SWAP" in futures_params["inst_id"] or "FUTURES" in futures_params["inst_id"]:
                # 期货合约：需要考虑合约面值(ctVal)
                contract_value = self.get_contract_value(futures_params["inst_id"])
                
                # 计算基于期货精度的标的数量
                # 期货张数 * 合约面值 = 标的数量
                futures_underlying_size = original_futures_size * contract_value
                
                # 调整现货数量以匹配期货对应的标的数量
                # 确保现货数量符合其精度要求
                aligned_spot_size = round(futures_underlying_size / spot_lot_sz) * spot_lot_sz
                aligned_spot_size = max(aligned_spot_size, spot_min_sz)
                
                # 反算期货数量，确保严格对应
                target_underlying_size = aligned_spot_size
                aligned_futures_size = round((target_underlying_size / contract_value) / futures_lot_sz) * futures_lot_sz
                aligned_futures_size = max(aligned_futures_size, futures_min_sz)
                
                # 最终验证：重新计算对应的标的数量确保匹配
                final_spot_underlying = aligned_spot_size
                final_futures_underlying = aligned_futures_size * contract_value
                
                self.logger.debug(f"💱 合约面值调整: ctVal={contract_value}")
                self.logger.debug(f"💱 标的数量: 现货={final_spot_underlying}, 期货={final_futures_underlying}")
                
                # 如果差异超过最小精度单位，进行微调
                underlying_diff = abs(final_spot_underlying - final_futures_underlying)
                if underlying_diff > max(spot_lot_sz, futures_lot_sz * contract_value):
                    self.logger.warning(f"⚠️ 标的数量差异较大: {underlying_diff:.8f}, 进行微调")
                    
                    # 以更严格的精度为准（通常是期货）
                    if futures_lot_sz * contract_value > spot_lot_sz:
                        # 期货精度更粗，以期货为准
                        target_underlying = aligned_futures_size * contract_value
                        aligned_spot_size = round(target_underlying / spot_lot_sz) * spot_lot_sz
                    else:
                        # 现货精度更粗，以现货为准
                        target_underlying = aligned_spot_size
                        aligned_futures_size = round((target_underlying / contract_value) / futures_lot_sz) * futures_lot_sz
            
            else:
                # 非合约类型（如币币现货对），直接数量匹配
                # 选择较粗的精度作为统一标准
                common_lot_size = max(spot_lot_sz, futures_lot_sz)
                common_min_size = max(spot_min_sz, futures_min_sz)
                
                # 以较小的原始数量为基准，向下舍入
                target_size = min(original_spot_size, original_futures_size)
                aligned_size = round(target_size / common_lot_size) * common_lot_size
                aligned_size = max(aligned_size, common_min_size)
                
                aligned_spot_size = aligned_size
                aligned_futures_size = aligned_size
            
            # 创建对齐后的参数
            aligned_spot_params = spot_params.copy()
            aligned_futures_params = futures_params.copy()
            
            aligned_spot_params["sz"] = f"{aligned_spot_size:.8f}".rstrip('0').rstrip('.')
            aligned_futures_params["sz"] = f"{aligned_futures_size:.8f}".rstrip('0').rstrip('.')
            
            self.logger.info(f"✅ 数量对齐完成: 现货 {original_spot_size} → {aligned_spot_size}, 期货 {original_futures_size} → {aligned_futures_size}")
            
            return (aligned_spot_params, aligned_futures_params)
            
        except Exception as e:
            self.logger.error(f"❌ 数量对齐失败: {e}")
            # 失败时返回原始参数
            return (spot_params, futures_params)
    
    async def _execute_atomic_post_only(self, spot_params: Dict, futures_params: Dict) -> tuple:
        """原子性执行Post-Only订单对"""
        try:
            # 获取盘口价格
            spot_prices = await self.get_best_prices(spot_params["inst_id"])
            futures_prices = await self.get_best_prices(futures_params["inst_id"])
            
            if not spot_prices or not futures_prices:
                return (
                    {"success": False, "error": "无法获取盘口价格", "filled_size": 0, "avg_price": 0},
                    {"success": False, "error": "无法获取盘口价格", "filled_size": 0, "avg_price": 0}
                )
            
            # 计算Post-Only价格（确保不会立即成交）
            spot_price = spot_prices['bid'] if spot_params["side"] == "sell" else spot_prices['ask']
            futures_price = futures_prices['bid'] if futures_params["side"] == "sell" else futures_prices['ask']
            
            # 价格精度调整
            spot_price = round(spot_price / self.get_tick_size(spot_params["inst_id"])) * self.get_tick_size(spot_params["inst_id"])
            futures_price = round(futures_price / self.get_tick_size(futures_params["inst_id"])) * self.get_tick_size(futures_params["inst_id"])
            
            # 并发下单
            spot_task = asyncio.create_task(self.place_order(
                inst_id=spot_params["inst_id"],
                trade_mode=spot_params["trade_mode"],
                side=spot_params["side"],
                ord_type="post_only",
                sz=spot_params["sz"],
                px=str(spot_price)
            ))
            
            futures_task = asyncio.create_task(self.place_order(
                inst_id=futures_params["inst_id"],
                trade_mode=futures_params["trade_mode"],
                side=futures_params["side"],
                ord_type="post_only",
                sz=futures_params["sz"],
                px=str(futures_price)
            ))
            
            # 等待两个订单结果
            spot_order_result, futures_order_result = await asyncio.gather(
                spot_task, futures_task, return_exceptions=True
            )
            
            # 检查订单提交是否成功
            spot_order_success = (not isinstance(spot_order_result, Exception) and 
                                spot_order_result.get('code') == '0')
            futures_order_success = (not isinstance(futures_order_result, Exception) and 
                                   futures_order_result.get('code') == '0')
            
            if not spot_order_success or not futures_order_success:
                # 如果有一边下单失败，取消另一边的订单
                await self._cancel_failed_atomic_orders(spot_order_result, futures_order_result, 
                                                       spot_params, futures_params)
                return (
                    {"success": False, "error": "Post-Only下单失败", "filled_size": 0, "avg_price": 0},
                    {"success": False, "error": "Post-Only下单失败", "filled_size": 0, "avg_price": 0}
                )
            
            # 提取订单ID
            spot_order_id = spot_order_result['data'][0]['ordId']
            futures_order_id = futures_order_result['data'][0]['ordId']
            
            # 等待订单成交（短时间）
            post_only_timeout = self.config.ORDER_EXECUTION["SMART_EXECUTION"]["POST_ONLY_TIMEOUT_MS"]
            spot_fill_task = asyncio.create_task(
                self._wait_for_order_fill(spot_order_id, spot_params["inst_id"], post_only_timeout // 1000)
            )
            futures_fill_task = asyncio.create_task(
                self._wait_for_order_fill(futures_order_id, futures_params["inst_id"], post_only_timeout // 1000)
            )
            
            spot_fill_result, futures_fill_result = await asyncio.gather(
                spot_fill_task, futures_fill_task
            )
            
            # 检查是否双边都成交
            both_filled = spot_fill_result['success'] and futures_fill_result['success']
            
            if both_filled:
                return (
                    {
                        "success": True,
                        "filled_size": spot_fill_result['filled_size'],
                        "avg_price": spot_fill_result['avg_price'],
                        "order_ids": [spot_order_id]
                    },
                    {
                        "success": True,
                        "filled_size": futures_fill_result['filled_size'],
                        "avg_price": futures_fill_result['avg_price'],
                        "order_ids": [futures_order_id]
                    }
                )
            else:
                # 取消未成交的订单
                if not spot_fill_result['success']:
                    await self.cancel_order(spot_params["inst_id"], spot_order_id)
                if not futures_fill_result['success']:
                    await self.cancel_order(futures_params["inst_id"], futures_order_id)
                
                return (
                    {"success": False, "error": "Post-Only超时未成交", "filled_size": 0, "avg_price": 0},
                    {"success": False, "error": "Post-Only超时未成交", "filled_size": 0, "avg_price": 0}
                )
                
        except Exception as e:
            self.logger.error(f"Post-Only原子执行失败: {e}")
            return (
                {"success": False, "error": str(e), "filled_size": 0, "avg_price": 0},
                {"success": False, "error": str(e), "filled_size": 0, "avg_price": 0}
            )
    
    async def _execute_atomic_ioc(self, spot_params: Dict, futures_params: Dict) -> tuple:
        """原子性执行IOC订单对"""
        try:
            # 获取盘口价格
            spot_prices = await self.get_best_prices(spot_params["inst_id"])
            futures_prices = await self.get_best_prices(futures_params["inst_id"])
            
            if not spot_prices or not futures_prices:
                return (
                    {"success": False, "error": "无法获取盘口价格", "filled_size": 0, "avg_price": 0},
                    {"success": False, "error": "无法获取盘口价格", "filled_size": 0, "avg_price": 0}
                )
            
            # 计算IOC价格（略微穿越盘口确保成交）
            taker_offset = self.config.ORDER_EXECUTION["SMART_EXECUTION"]["TAKER_PRICE_OFFSET"]
            
            if spot_params["side"] == "buy":
                spot_price = spot_prices['ask'] + taker_offset
            else:
                spot_price = spot_prices['bid'] - taker_offset
                
            if futures_params["side"] == "buy":
                futures_price = futures_prices['ask'] + taker_offset
            else:
                futures_price = futures_prices['bid'] - taker_offset
            
            # 价格精度调整
            spot_price = round(spot_price / self.get_tick_size(spot_params["inst_id"])) * self.get_tick_size(spot_params["inst_id"])
            futures_price = round(futures_price / self.get_tick_size(futures_params["inst_id"])) * self.get_tick_size(futures_params["inst_id"])
            
            # 并发执行IOC订单
            spot_task = asyncio.create_task(self.place_order(
                inst_id=spot_params["inst_id"],
                trade_mode=spot_params["trade_mode"],
                side=spot_params["side"],
                ord_type="ioc",
                sz=spot_params["sz"],
                px=str(spot_price)
            ))
            
            futures_task = asyncio.create_task(self.place_order(
                inst_id=futures_params["inst_id"],
                trade_mode=futures_params["trade_mode"],
                side=futures_params["side"],
                ord_type="ioc",
                sz=futures_params["sz"],
                px=str(futures_price)
            ))
            
            # 等待订单结果
            spot_result, futures_result = await asyncio.gather(
                spot_task, futures_task, return_exceptions=True
            )
            
            # 检查订单结果并验证成交
            return await self._validate_atomic_results(spot_result, futures_result, spot_params, futures_params)
            
        except Exception as e:
            self.logger.error(f"IOC原子执行失败: {e}")
            return (
                {"success": False, "error": str(e), "filled_size": 0, "avg_price": 0},
                {"success": False, "error": str(e), "filled_size": 0, "avg_price": 0}
            )
    
    async def _execute_atomic_market(self, spot_params: Dict, futures_params: Dict) -> tuple:
        """原子性执行市价单对（最后手段）"""
        try:
            # 并发执行市价单
            spot_task = asyncio.create_task(self.place_order(
                inst_id=spot_params["inst_id"],
                trade_mode=spot_params["trade_mode"],
                side=spot_params["side"],
                ord_type="market",
                sz=spot_params["sz"]
            ))
            
            futures_task = asyncio.create_task(self.place_order(
                inst_id=futures_params["inst_id"],
                trade_mode=futures_params["trade_mode"],
                side=futures_params["side"],
                ord_type="market",
                sz=futures_params["sz"]
            ))
            
            # 等待订单结果
            spot_result, futures_result = await asyncio.gather(
                spot_task, futures_task, return_exceptions=True
            )
            
            # 检查订单结果并验证成交
            return await self._validate_atomic_results(spot_result, futures_result, spot_params, futures_params)
            
        except Exception as e:
            self.logger.error(f"市价单原子执行失败: {e}")
            return (
                {"success": False, "error": str(e), "filled_size": 0, "avg_price": 0},
                {"success": False, "error": str(e), "filled_size": 0, "avg_price": 0}
            )
    
    async def _validate_atomic_results(self, spot_result, futures_result, spot_params: Dict, futures_params: Dict) -> tuple:
        """增强版原子性执行结果验证 - 严格防止单边交易"""
        # 🔧 步骤1: 异常检查
        if isinstance(spot_result, Exception) or isinstance(futures_result, Exception):
            exception_msg = f"现货异常: {spot_result}" if isinstance(spot_result, Exception) else f"期货异常: {futures_result}"
            self.logger.error(f"❌ 订单执行异常: {exception_msg}")
            return (
                {"success": False, "error": "订单执行异常", "filled_size": 0, "avg_price": 0},
                {"success": False, "error": "订单执行异常", "filled_size": 0, "avg_price": 0}
            )
        
        # 🔧 步骤2: 订单提交状态验证
        spot_success = spot_result.get('code') == '0'
        futures_success = futures_result.get('code') == '0'
        
        # 如果任意一边提交失败，立即返回双边失败
        if not spot_success or not futures_success:
            spot_error = spot_result.get('msg', 'Unknown error') if not spot_success else "配对失败"
            futures_error = futures_result.get('msg', 'Unknown error') if not futures_success else "配对失败"
            
            self.logger.error(f"❌ 原子性订单提交失败 - 现货: {spot_error}, 期货: {futures_error}")
            
            # 🚨 关键：双边同时失败，绝不允许单边成功
            return (
                {"success": False, "error": spot_error, "filled_size": 0, "avg_price": 0},
                {"success": False, "error": futures_error, "filled_size": 0, "avg_price": 0}
            )
        
        # 提取订单ID并等待成交确认
        spot_order_id = spot_result['data'][0]['ordId']
        futures_order_id = futures_result['data'][0]['ordId']
        
        # 🔧 步骤3: 等待双边成交确认（严格模式）
        ioc_timeout = self.config.ORDER_EXECUTION["SMART_EXECUTION"]["IOC_TIMEOUT_MS"] // 1000
        
        self.logger.info(f"⏱️ 等待双边成交确认，超时时间: {ioc_timeout}秒")
        
        spot_fill_task = asyncio.create_task(
            self._wait_for_order_fill(spot_order_id, spot_params["inst_id"], ioc_timeout)
        )
        futures_fill_task = asyncio.create_task(
            self._wait_for_order_fill(futures_order_id, futures_params["inst_id"], ioc_timeout)
        )
        
        spot_fill_result, futures_fill_result = await asyncio.gather(
            spot_fill_task, futures_fill_task
        )
        
        # 🔧 步骤4: 严格的双边成交验证 - 绝不允许单边成交
        spot_filled_success = spot_fill_result['success']
        futures_filled_success = futures_fill_result['success']
        
        # 🚨 关键检查：必须双边都成交，否则取消所有订单
        if not (spot_filled_success and futures_filled_success):
            self.logger.error(f"❌ 双边成交失败 - 现货成交: {spot_filled_success}, 期货成交: {futures_filled_success}")
            
            # 取消任何可能的部分成交订单
            cancel_tasks = []
            if spot_filled_success:
                self.logger.warning("🔥 检测到现货单边成交，立即取消!")
                cancel_tasks.append(asyncio.create_task(self.cancel_order(spot_params["inst_id"], spot_order_id)))
            if futures_filled_success:
                self.logger.warning("🔥 检测到期货单边成交，立即取消!")
                cancel_tasks.append(asyncio.create_task(self.cancel_order(futures_params["inst_id"], futures_order_id)))
            
            # 并发执行取消操作
            if cancel_tasks:
                await asyncio.gather(*cancel_tasks, return_exceptions=True)
            
            # 返回双边失败结果
            return (
                {"success": False, "error": "双边成交失败，已阻止单边交易", "filled_size": 0, "avg_price": 0},
                {"success": False, "error": "双边成交失败，已阻止单边交易", "filled_size": 0, "avg_price": 0}
            )
        
        # 🔧 步骤5: 双边都成交，验证数量匹配
        if spot_filled_success and futures_filled_success:
            spot_filled = spot_fill_result['filled_size']
            futures_filled = futures_fill_result['filled_size']
            
            # 获取交易对精度规格进行精确匹配验证
            spot_precision = await self._get_instrument_precision(spot_params["inst_id"])
            futures_precision = await self._get_instrument_precision(futures_params["inst_id"])
            
            # 🔧 增强精度容差计算：考虑合约面值差异
            spot_min_sz = float(spot_precision.get('minSz', '1e-8'))
            futures_min_sz = float(futures_precision.get('minSz', '1e-8'))
            
            # 对于期现套利，需要考虑合约面值来计算等价容差
            if "SWAP" in futures_params["inst_id"] or "FUTURES" in futures_params["inst_id"]:
                contract_value = self.get_contract_value(futures_params["inst_id"])
                # 期货最小单位对应的标的数量
                futures_underlying_min = futures_min_sz * contract_value
                # 使用较大的最小单位作为容差
                tolerance = max(spot_min_sz, futures_underlying_min)
            else:
                tolerance = max(spot_min_sz, futures_min_sz)
            
            # 确保容差不会太小
            tolerance = max(tolerance, 1e-8)
            
            volume_diff = abs(spot_filled - futures_filled)
            max_volume = max(spot_filled, futures_filled)
            mismatch_ratio = volume_diff / max_volume if max_volume > 0 else 0
            
            self.logger.debug(f"🔍 严格双边成交量验证: 现货={spot_filled:.8f}, 期货={futures_filled:.8f}")
            self.logger.debug(f"📏 精度规格: 现货minSz={spot_min_sz:.8f}, 期货minSz={futures_min_sz:.8f}")
            if "SWAP" in futures_params["inst_id"] or "FUTURES" in futures_params["inst_id"]:
                self.logger.debug(f"💱 合约面值: {contract_value}, 期货等价minSz={futures_underlying_min:.8f}")
            self.logger.debug(f"📊 差异分析: 绝对差异={volume_diff:.8f}, 相对比例={mismatch_ratio:.8f}, 容差={tolerance:.8f}")
            
            # 如果成交量完全匹配，直接返回成功
            if mismatch_ratio <= tolerance:
                self.logger.info(f"✅ 双边成交量完全匹配: {spot_filled}")
                return (
                    {
                        "success": True,
                        "filled_size": spot_filled,
                        "avg_price": spot_fill_result['avg_price'],
                        "order_ids": [spot_order_id]
                    },
                    {
                        "success": True,
                        "filled_size": futures_filled,
                        "avg_price": futures_fill_result['avg_price'],
                        "order_ids": [futures_order_id]
                    }
                )
            
            # 成交量不匹配，需要对冲差额
            else:
                self.logger.warning(f"⚠️ 双边成交量不匹配! 现货: {spot_filled}, 期货: {futures_filled}, 差异: {volume_diff}")
                
                # 确定哪一边成交更多，需要对冲的方向和数量
                if spot_filled > futures_filled:
                    # 现货成交多了，需要卖出多余的现货头寸
                    hedge_direction = "sell_spot"
                    hedge_amount = volume_diff
                    matched_size = futures_filled
                else:
                    # 期货成交多了，需要平仓多余的期货头寸
                    hedge_direction = "close_futures"
                    hedge_amount = volume_diff
                    matched_size = spot_filled
                
                # 尝试对冲差额 - 增强精度和重试逻辑
                hedge_result = await self._hedge_position_mismatch(
                    hedge_direction, hedge_amount, spot_params, futures_params,
                    spot_precision, futures_precision
                )
                
                if hedge_result['success']:
                    self.logger.info(f"✅ 头寸差额对冲成功: {hedge_direction} {hedge_amount}")
                    # 对冲成功，返回匹配的成交量
                    return (
                        {
                            "success": True,
                            "filled_size": matched_size,
                            "avg_price": spot_fill_result['avg_price'],
                            "order_ids": [spot_order_id],
                            "hedge_info": hedge_result
                        },
                        {
                            "success": True,
                            "filled_size": matched_size,
                            "avg_price": futures_fill_result['avg_price'],
                            "order_ids": [futures_order_id],
                            "hedge_info": hedge_result
                        }
                    )
                else:
                    # 🚨 对冲失败 - 严格模式下绝不允许不匹配的头寸
                    self.logger.error(f"❌ 头寸差额对冲失败: {hedge_result.get('error', 'Unknown error')}")
                    self.logger.error(f"🚨 严重风险: 存在 {volume_diff} 的净头寸敞口!")
                    
                    # 🔧 严格模式：立即执行紧急平仓，消除风险敞口
                    emergency_hedge_result = await self._emergency_hedge_position(
                        spot_filled, futures_filled, spot_params, futures_params
                    )
                    
                    if emergency_hedge_result['success']:
                        self.logger.info("✅ 紧急对冲成功，风险敞口已消除")
                        return (
                            {
                                "success": True,
                                "filled_size": emergency_hedge_result['final_spot_size'],
                                "avg_price": spot_fill_result['avg_price'],
                                "order_ids": [spot_order_id],
                                "emergency_hedge": True
                            },
                            {
                                "success": True,
                                "filled_size": emergency_hedge_result['final_futures_size'],
                                "avg_price": futures_fill_result['avg_price'],
                                "order_ids": [futures_order_id],
                                "emergency_hedge": True
                            }
                        )
                    else:
                        # 🚨 最严重情况：紧急对冲也失败，返回交易失败
                        self.logger.critical(f"🔥 紧急对冲失败! 系统将拒绝此次交易以防止风险敞口")
                        return (
                            {"success": False, "error": f"无法消除{volume_diff}的风险敞口", "filled_size": 0, "avg_price": 0},
                            {"success": False, "error": f"无法消除{volume_diff}的风险敞口", "filled_size": 0, "avg_price": 0}
                        )
    
    async def _emergency_hedge_position(self, spot_filled: float, futures_filled: float, 
                                      spot_params: Dict, futures_params: Dict) -> Dict[str, Any]:
        """
        紧急对冲功能 - 在常规对冲失败时的最后防线
        
        通过市价单快速消除不匹配的头寸，确保无风险敞口
        """
        try:
            self.logger.info(f"🚨 启动紧急对冲: 现货={spot_filled}, 期货={futures_filled}")
            
            # 计算需要对冲的方向和数量
            volume_diff = abs(spot_filled - futures_filled) 
            
            if spot_filled > futures_filled:
                # 现货多了，需要市价卖出多余的现货
                hedge_side = "sell"
                hedge_inst_id = spot_params["inst_id"]
                hedge_trade_mode = spot_params["trade_mode"]
                self.logger.info(f"🔄 紧急卖出多余现货: {volume_diff}")
                
            else:
                # 期货多了，需要市价平仓多余的期货
                hedge_side = "buy" if spot_params["side"] == "sell" else "sell"  # 反向平仓
                hedge_inst_id = futures_params["inst_id"]
                hedge_trade_mode = futures_params["trade_mode"]
                self.logger.info(f"🔄 紧急平仓多余期货: {volume_diff}, 方向: {hedge_side}")
            
            # 执行紧急市价对冲订单
            hedge_result = await self.place_order(
                inst_id=hedge_inst_id,
                trade_mode=hedge_trade_mode,
                side=hedge_side,
                ord_type="market",
                sz=f"{volume_diff:.8f}".rstrip('0').rstrip('.')
            )
            
            if hedge_result.get('code') == '0':
                order_id = hedge_result['data'][0]['ordId']
                
                # 等待对冲订单成交
                hedge_fill = await self._wait_for_order_fill(order_id, hedge_inst_id, 3)
                
                if hedge_fill['success']:
                    # 计算最终匹配的数量
                    final_matched_size = min(spot_filled, futures_filled)
                    
                    self.logger.info(f"✅ 紧急对冲成功: 对冲量={hedge_fill['filled_size']}, 最终匹配量={final_matched_size}")
                    
                    return {
                        'success': True,
                        'hedge_direction': hedge_side,
                        'hedge_amount': hedge_fill['filled_size'],
                        'final_spot_size': final_matched_size,
                        'final_futures_size': final_matched_size,
                        'hedge_order_id': order_id
                    }
                else:
                    return {
                        'success': False,
                        'error': f"紧急对冲订单未成交: {hedge_fill.get('error')}"
                    }
            else:
                return {
                    'success': False,
                    'error': f"紧急对冲订单提交失败: {hedge_result.get('msg')}"
                }
                
        except Exception as e:
            self.logger.error(f"紧急对冲执行异常: {e}")
            return {
                'success': False,
                'error': f"紧急对冲异常: {str(e)}"
            }
    
    async def _hedge_position_mismatch(self, hedge_direction: str, hedge_amount: float, 
                                     spot_params: Dict, futures_params: Dict,
                                     spot_precision: Dict = None, futures_precision: Dict = None) -> Dict[str, Any]:
        """对冲头寸不匹配的差额 - 增强精度处理和重试机制"""
        try:
            # 获取精度信息（如果没有提供）
            if spot_precision is None:
                spot_precision = await self._get_instrument_precision(spot_params["inst_id"])
            if futures_precision is None:
                futures_precision = await self._get_instrument_precision(futures_params["inst_id"])
            
            self.logger.info(f"🔄 开始对冲头寸差额: {hedge_direction} {hedge_amount:.8f}")
            self.logger.debug(f"📏 对冲精度控制: 现货minSz={spot_precision.get('minSz')}, 期货minSz={futures_precision.get('minSz')}")
            
            if hedge_direction == "sell_spot":
                # 现货成交多了，需要市价卖出多余的现货
                # 按交易对精度调整对冲数量
                min_sz = spot_precision.get('minSz', 1e-8)
                adjusted_amount = max(hedge_amount, min_sz)
                
                # 确保数量符合交易对规格 - 修复精度判断逻辑
                if hedge_amount < min_sz:
                    self.logger.warning(f"对冲数量{hedge_amount}小于最小交易单位{min_sz}，跳过对冲")
                    return {
                        'success': False,
                        'error': f"对冲数量太小: {hedge_amount} < {min_sz}",
                        'skipped': True
                    }
                
                hedge_params = {
                    "instId": spot_params["inst_id"],
                    "tdMode": "cash",  # 现货模式
                    "side": "sell",
                    "ordType": "market",
                    "sz": f"{adjusted_amount:.8f}".rstrip('0').rstrip('.')
                }
                
                self.logger.debug(f"📤 现货对冲参数: {hedge_params}")
                
                result = await self.place_order(
                    hedge_params["instId"],
                    hedge_params["tdMode"], 
                    hedge_params["side"],
                    hedge_params["ordType"],
                    hedge_params["sz"],
                    px=""  # 市价单不需要价格
                )
                
                if result.get('code') == '0':
                    order_id = result['data'][0]['ordId']
                    # 等待对冲订单成交
                    hedge_fill = await self._wait_for_order_fill(
                        order_id, spot_params["inst_id"], timeout=5
                    )
                    
                    if hedge_fill['success']:
                        return {
                            'success': True,
                            'direction': hedge_direction,
                            'amount': hedge_amount,
                            'filled_amount': hedge_fill['filled_size'],
                            'avg_price': hedge_fill['avg_price'],
                            'order_id': order_id
                        }
                    else:
                        return {
                            'success': False,
                            'error': f"对冲订单未成交: {hedge_fill.get('error', 'timeout')}",
                            'order_id': order_id
                        }
                else:
                    return {
                        'success': False,
                        'error': f"对冲下单失败: {result.get('msg', 'unknown error')}"
                    }
                    
            elif hedge_direction == "close_futures":
                # 期货成交多了，需要平仓多余的期货头寸
                # 按交易对精度调整对冲数量
                min_sz = futures_precision.get('minSz', 1e-8)
                adjusted_amount = max(hedge_amount, min_sz)
                
                # 确保数量符合交易对规格 - 修复精度判断逻辑
                if hedge_amount < min_sz:
                    self.logger.warning(f"期货对冲数量{hedge_amount}小于最小交易单位{min_sz}，跳过对冲")
                    return {
                        'success': False,
                        'error': f"对冲数量太小: {hedge_amount} < {min_sz}",
                        'skipped': True
                    }
                
                # 获取原始期货订单的方向来确定平仓方向
                original_side = futures_params.get("side", "buy")
                close_side = "sell" if original_side == "buy" else "buy"
                
                hedge_params = {
                    "instId": futures_params["inst_id"],
                    "tdMode": "cross",  # 全仓模式
                    "side": close_side,
                    "ordType": "market",
                    "sz": f"{adjusted_amount:.8f}".rstrip('0').rstrip('.'),
                    "reduceOnly": True  # 只减仓，确保是平仓
                }
                
                self.logger.debug(f"📤 期货对冲参数: {hedge_params}")
                
                result = await self.place_order(
                    hedge_params["instId"],
                    hedge_params["tdMode"], 
                    hedge_params["side"],
                    hedge_params["ordType"],
                    hedge_params["sz"],
                    px=""  # 市价单不需要价格
                )
                
                if result.get('code') == '0':
                    order_id = result['data'][0]['ordId']
                    # 等待对冲订单成交
                    hedge_fill = await self._wait_for_order_fill(
                        order_id, futures_params["inst_id"], timeout=5
                    )
                    
                    if hedge_fill['success']:
                        return {
                            'success': True,
                            'direction': hedge_direction,
                            'amount': hedge_amount,
                            'filled_amount': hedge_fill['filled_size'],
                            'avg_price': hedge_fill['avg_price'],
                            'order_id': order_id
                        }
                    else:
                        return {
                            'success': False,
                            'error': f"对冲订单未成交: {hedge_fill.get('error', 'timeout')}",
                            'order_id': order_id
                        }
                else:
                    return {
                        'success': False,
                        'error': f"对冲下单失败: {result.get('msg', 'unknown error')}"
                    }
            else:
                return {
                    'success': False,
                    'error': f"不支持的对冲方向: {hedge_direction}"
                }
                
        except Exception as e:
            self.logger.error(f"头寸对冲过程中出错: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def _cancel_failed_atomic_orders(self, spot_result, futures_result, spot_params: Dict, futures_params: Dict):
        """取消失败的原子性订单"""
        try:
            # 如果现货下单成功但期货失败，取消现货订单
            if (not isinstance(spot_result, Exception) and spot_result.get('code') == '0' and
                (isinstance(futures_result, Exception) or futures_result.get('code') != '0')):
                
                spot_order_id = spot_result['data'][0]['ordId']
                await self.cancel_order(spot_params["inst_id"], spot_order_id)
                self.logger.info(f"已取消现货订单: {spot_order_id}")
            
            # 如果期货下单成功但现货失败，取消期货订单
            if (not isinstance(futures_result, Exception) and futures_result.get('code') == '0' and
                (isinstance(spot_result, Exception) or spot_result.get('code') != '0')):
                
                futures_order_id = futures_result['data'][0]['ordId']
                await self.cancel_order(futures_params["inst_id"], futures_order_id)
                self.logger.info(f"已取消期货订单: {futures_order_id}")
                
        except Exception as e:
            self.logger.error(f"取消失败订单时出错: {e}")
    
    async def _enhanced_atomic_rollback(self, spot_fill_result: Dict, futures_fill_result: Dict,
                                      spot_order_id: str, futures_order_id: str,
                                      spot_params: Dict, futures_params: Dict) -> Dict[str, Any]:
        """增强的原子性交易回滚机制 - 处理部分成交情况"""
        try:
            rollback_actions = []
            all_success = True

            # 处理现货部分成交回滚
            if spot_fill_result['success'] and spot_fill_result.get('filled_size', 0) > 0:
                # 现货有部分成交，需要对冲
                filled_size = spot_fill_result['filled_size']
                self.logger.info(f"🔄 现货部分成交回滚: {filled_size}")

                # 执行反向市价单对冲
                opposite_side = "sell" if spot_params.get("side") == "buy" else "buy"
                rollback_result = await self.place_order(
                    spot_params["inst_id"],
                    "cash",
                    opposite_side,
                    "market",
                    str(filled_size),
                    px=""  # 市价单不需要价格
                )

                if rollback_result.get('code') == '0':
                    rollback_order_id = rollback_result['data'][0]['ordId']
                    # 等待回滚成交
                    rollback_fill = await self._wait_for_order_fill(
                        rollback_order_id, spot_params["inst_id"], timeout=10
                    )

                    if rollback_fill['success']:
                        rollback_actions.append(f"现货回滚成功: {opposite_side} {rollback_fill['filled_size']}")
                        self.logger.info(f"✅ 现货回滚成交: {rollback_fill['filled_size']}")
                    else:
                        all_success = False
                        rollback_actions.append(f"现货回滚失败: {rollback_fill.get('error', 'timeout')}")
                        self.logger.error(f"❌ 现货回滚失败: {rollback_fill.get('error', 'timeout')}")
                else:
                    all_success = False
                    rollback_actions.append(f"现货回滚下单失败: {rollback_result.get('msg', 'unknown')}")
                    self.logger.error(f"❌ 现货回滚下单失败: {rollback_result.get('msg', 'unknown')}")

            # 处理期货部分成交回滚
            if futures_fill_result['success'] and futures_fill_result.get('filled_size', 0) > 0:
                # 期货有部分成交，需要平仓
                filled_size = futures_fill_result['filled_size']
                self.logger.info(f"🔄 期货部分成交回滚: {filled_size}")

                # 执行反向平仓
                original_side = futures_params.get("side", "buy")
                close_side = "sell" if original_side == "buy" else "buy"

                rollback_result = await self.place_order(
                    futures_params["inst_id"],
                    "cross",
                    close_side,
                    "market",
                    str(filled_size),
                    px=""  # 市价单不需要价格
                )

                if rollback_result.get('code') == '0':
                    rollback_order_id = rollback_result['data'][0]['ordId']
                    # 等待回滚成交
                    rollback_fill = await self._wait_for_order_fill(
                        rollback_order_id, futures_params["inst_id"], timeout=10
                    )

                    if rollback_fill['success']:
                        rollback_actions.append(f"期货回滚成功: {close_side} {rollback_fill['filled_size']}")
                        self.logger.info(f"✅ 期货回滚成交: {rollback_fill['filled_size']}")
                    else:
                        all_success = False
                        rollback_actions.append(f"期货回滚失败: {rollback_fill.get('error', 'timeout')}")
                        self.logger.error(f"❌ 期货回滚失败: {rollback_fill.get('error', 'timeout')}")
                else:
                    all_success = False
                    rollback_actions.append(f"期货回滚下单失败: {rollback_result.get('msg', 'unknown')}")
                    self.logger.error(f"❌ 期货回滚下单失败: {rollback_result.get('msg', 'unknown')}")

            # 取消未成交的订单
            if not spot_fill_result['success']:
                try:
                    await self.cancel_order(spot_params["inst_id"], spot_order_id)
                    rollback_actions.append(f"取消现货订单: {spot_order_id}")
                    self.logger.info(f"✅ 已取消现货订单: {spot_order_id}")
                except Exception as e:
                    rollback_actions.append(f"取消现货订单失败: {str(e)}")
                    self.logger.warning(f"⚠️ 取消现货订单失败: {e}")

            if not futures_fill_result['success']:
                try:
                    await self.cancel_order(futures_params["inst_id"], futures_order_id)
                    rollback_actions.append(f"取消期货订单: {futures_order_id}")
                    self.logger.info(f"✅ 已取消期货订单: {futures_order_id}")
                except Exception as e:
                    rollback_actions.append(f"取消期货订单失败: {str(e)}")
                    self.logger.warning(f"⚠️ 取消期货订单失败: {e}")

            return {
                'success': all_success,
                'actions': rollback_actions,
                'total_actions': len(rollback_actions)
            }

        except Exception as e:
            self.logger.error(f"增强回滚机制异常: {e}")
            return {
                'success': False,
                'error': str(e),
                'actions': []
            }

    async def get_historical_candlesticks(self, inst_id: str, bar: str = "1m",
                                        limit: int = 100, after: str = None,
                                        before: str = None) -> Dict[str, Any]:
        """
        获取历史K线数据 - 用于策略数据预热

        Args:
            inst_id: 产品ID，如 "BTC-USDT"
            bar: K线周期，支持 "1m", "3m", "5m", "15m", "30m", "1H", "2H", "4H", "6H", "12H", "1D", "1W", "1M", "3M", "6M", "1Y"
            limit: 返回结果的数量，最大值为300，默认值为100
            after: 请求此时间戳之后（更旧的数据）的分页内容，传的值为对应接口的ts
            before: 请求此时间戳之前（更新的数据）的分页内容，传的值为对应接口的ts

        Returns:
            Dict包含:
            - success: bool, 是否成功
            - data: List[List], K线数据数组，每个元素为 [ts, o, h, l, c, vol, volCcy, volCcyQuote, confirm]
            - error: str, 错误信息（如果失败）
        """
        try:
            # 应用速率限制 - 查询API
            if not await self.query_rate_limiter.wait_for_tokens(1, timeout=5.0):
                raise RuntimeError("查询速率限制超时 - API请求过于频繁")

            # 构建请求路径和参数
            request_path = "/api/v5/market/candles"
            params = {
                "instId": inst_id,
                "bar": bar,
                "limit": str(limit)
            }

            if after:
                params["after"] = after
            if before:
                params["before"] = before

            # 构建完整的请求URL
            query_string = "&".join([f"{k}={v}" for k, v in params.items()])
            full_path = f"{request_path}?{query_string}"

            self.logger.debug(f"📊 获取历史K线数据: {inst_id}, 周期: {bar}, 数量: {limit}")

            # 发送GET请求
            headers = self._get_headers("GET", full_path, "")

            async with self.http_session.get(
                f"{self.base_url}{full_path}",
                headers=headers,
                proxy=self.proxy
            ) as response:
                result = await response.json()

                if result.get('code') == '0':
                    candlestick_data = result.get('data', [])
                    self.logger.debug(f"✅ 成功获取 {len(candlestick_data)} 条K线数据")

                    return {
                        'success': True,
                        'data': candlestick_data,
                        'inst_id': inst_id,
                        'bar': bar,
                        'count': len(candlestick_data)
                    }
                else:
                    error_msg = result.get('msg', 'Unknown error')
                    error_code = result.get('code', 'Unknown')
                    self.logger.error(f"❌ 获取K线数据失败: 错误码={error_code}, 消息={error_msg}")

                    return {
                        'success': False,
                        'error': f"{error_code}: {error_msg}",
                        'inst_id': inst_id,
                        'bar': bar
                    }

        except Exception as e:
            self.logger.error(f"获取历史K线数据异常: {e}")
            return {
                'success': False,
                'error': str(e),
                'inst_id': inst_id,
                'bar': bar
            }

    async def get_recent_candlesticks(self, inst_id: str, minutes: int = 30,
                                    bar: str = "1m") -> Dict[str, Any]:
        """
        获取最近N分钟的K线数据 - 专用于策略预热

        Args:
            inst_id: 产品ID，如 "BTC-USDT"
            minutes: 获取最近多少分钟的数据，默认30分钟
            bar: K线周期，默认1分钟

        Returns:
            Dict包含历史K线数据和统计信息
        """
        try:
            # 计算需要获取的K线数量
            if bar == "1m":
                limit = min(minutes, 300)  # API最大限制300条
            elif bar == "5m":
                limit = min(minutes // 5, 300)
            elif bar == "15m":
                limit = min(minutes // 15, 300)
            elif bar == "30m":
                limit = min(minutes // 30, 300)
            elif bar == "1H":
                limit = min(minutes // 60, 300)
            else:
                limit = 100  # 默认值

            self.logger.info(f"📊 获取 {inst_id} 最近 {minutes} 分钟的 {bar} K线数据 (约 {limit} 条)")

            # 调用通用K线获取方法
            result = await self.get_historical_candlesticks(
                inst_id=inst_id,
                bar=bar,
                limit=limit
            )

            if result['success']:
                candlestick_data = result['data']

                # 添加时间范围统计
                if candlestick_data:
                    # K线数据格式: [ts, o, h, l, c, vol, volCcy, volCcyQuote, confirm]
                    latest_ts = int(candlestick_data[0][0])  # 最新时间戳
                    oldest_ts = int(candlestick_data[-1][0])  # 最旧时间戳

                    latest_time = datetime.fromtimestamp(latest_ts / 1000, timezone.utc)
                    oldest_time = datetime.fromtimestamp(oldest_ts / 1000, timezone.utc)

                    result.update({
                        'time_range': {
                            'latest_time': latest_time.isoformat(),
                            'oldest_time': oldest_time.isoformat(),
                            'latest_ts': latest_ts,
                            'oldest_ts': oldest_ts,
                            'span_minutes': (latest_ts - oldest_ts) / (1000 * 60)
                        },
                        'requested_minutes': minutes,
                        'actual_count': len(candlestick_data)
                    })

                    self.logger.info(f"✅ 成功获取 {len(candlestick_data)} 条K线数据，时间范围: {oldest_time.strftime('%H:%M:%S')} - {latest_time.strftime('%H:%M:%S')}")
                else:
                    self.logger.warning(f"⚠️ 未获取到任何K线数据: {inst_id}")

            return result

        except Exception as e:
            self.logger.error(f"获取最近K线数据异常: {e}")
            return {
                'success': False,
                'error': str(e),
                'inst_id': inst_id,
                'requested_minutes': minutes,
                'bar': bar
            }

    async def get_multiple_historical_candlesticks(self, inst_ids: List[str],
                                                 minutes: int = 30,
                                                 bar: str = "1m") -> Dict[str, Any]:
        """
        批量获取多个交易对的历史K线数据 - 用于策略预热

        Args:
            inst_ids: 产品ID列表，如 ["BTC-USDT", "ETH-USDT", "DOGE-USDT"]
            minutes: 获取最近多少分钟的数据，默认30分钟
            bar: K线周期，默认1分钟

        Returns:
            Dict包含:
            - success: bool, 整体是否成功
            - data: Dict[str, List], 每个交易对的K线数据
            - summary: Dict, 汇总统计信息
            - errors: Dict[str, str], 失败的交易对及错误信息
        """
        try:
            self.logger.info(f"📊 批量获取 {len(inst_ids)} 个交易对的历史K线数据")

            results = {}
            errors = {}
            successful_count = 0
            total_candlesticks = 0

            # 并发获取所有交易对的数据，但控制并发数量避免触发限速
            semaphore = asyncio.Semaphore(3)  # 最多同时3个请求

            async def fetch_single_pair(inst_id: str):
                async with semaphore:
                    try:
                        # 在请求之间添加小延迟，避免触发限速
                        await asyncio.sleep(0.1)

                        result = await self.get_recent_candlesticks(
                            inst_id=inst_id,
                            minutes=minutes,
                            bar=bar
                        )

                        if result['success']:
                            results[inst_id] = result['data']
                            return inst_id, result, None
                        else:
                            return inst_id, None, result.get('error', 'Unknown error')

                    except Exception as e:
                        return inst_id, None, str(e)

            # 并发执行所有请求
            tasks = [fetch_single_pair(inst_id) for inst_id in inst_ids]
            completed_tasks = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            for task_result in completed_tasks:
                if isinstance(task_result, Exception):
                    self.logger.error(f"批量获取任务异常: {task_result}")
                    continue

                inst_id, data, error = task_result

                if error:
                    errors[inst_id] = error
                    self.logger.warning(f"⚠️ {inst_id} 获取失败: {error}")
                else:
                    successful_count += 1
                    total_candlesticks += len(data) if data else 0
                    self.logger.debug(f"✅ {inst_id} 获取成功: {len(data) if data else 0} 条K线")

            # 生成汇总统计
            summary = {
                'total_pairs': len(inst_ids),
                'successful_pairs': successful_count,
                'failed_pairs': len(errors),
                'total_candlesticks': total_candlesticks,
                'success_rate': (successful_count / len(inst_ids)) * 100 if inst_ids else 0,
                'requested_minutes': minutes,
                'bar_period': bar
            }

            overall_success = successful_count > 0 and len(errors) == 0

            self.logger.info(f"📊 批量获取完成: {successful_count}/{len(inst_ids)} 成功, 共 {total_candlesticks} 条K线数据")

            if errors:
                self.logger.warning(f"⚠️ 失败的交易对: {list(errors.keys())}")

            return {
                'success': overall_success,
                'data': results,
                'summary': summary,
                'errors': errors,
                'inst_ids': inst_ids
            }

        except Exception as e:
            self.logger.error(f"批量获取历史K线数据异常: {e}")
            return {
                'success': False,
                'error': str(e),
                'data': {},
                'summary': {
                    'total_pairs': len(inst_ids),
                    'successful_pairs': 0,
                    'failed_pairs': len(inst_ids),
                    'total_candlesticks': 0,
                    'success_rate': 0,
                    'requested_minutes': minutes,
                    'bar_period': bar
                },
                'errors': {inst_id: str(e) for inst_id in inst_ids},
                'inst_ids': inst_ids
            }


    def __repr__(self):
        return (
            f"OKXConnector("
            f"public={self.is_public_connected}, "
            f"private={self.is_private_connected}, "
            f"authenticated={self.is_private_authenticated}, "
            f"sandbox={self.config.USE_SANDBOX}"
            f")"
        )