# 交易仪表盘Redis数据源优化报告

## 修改概述

已成功确保 `trading_dashboard.py` 中的所有数据都来源于Redis，消除了自主计算和数据冲突的问题。

## 主要修改内容

### 1. 数据来源原则确立
- 在文件顶部添加了明确的数据来源原则说明
- 所有数据必须来源于Redis消息，不进行任何自主计算或API调用
- 仅进行必要的单位转换用于显示

### 2. 函数重构

#### 2.1 入场信息获取函数
- **原函数**: `calculate_entry_info()` - 计算入场成本和基差率
- **新函数**: `get_entry_info()` - 从Redis获取预计算的入场成本和基差率
- **修改**: 移除了现货+期货成本的计算逻辑，直接从Redis获取 `entry_cost` 字段

#### 2.2 止盈目标获取函数  
- **原函数**: `calculate_profit_target()` - 计算目标止盈基差率和距离
- **新函数**: `get_profit_target()` - 从Redis获取预设的止盈目标
- **修改**: 移除了基于配置文件的计算逻辑，直接从Redis获取 `profit_target_basis` 和 `profit_distance_bps` 字段

#### 2.3 PNL格式化函数
- **原函数**: `format_unrealized_pnl()` - 计算基差变化的bps
- **新函数**: `format_unrealized_pnl()` - 从Redis获取预计算的PNL数据
- **修改**: 移除了基差变化计算逻辑，直接从Redis获取 `basis_change_bps` 字段

#### 2.4 风险等级获取函数
- **原函数**: `calculate_risk_level()` - 计算距离止损线的距离
- **新函数**: `get_risk_level()` - 从Redis获取预计算的风险指标
- **修改**: 移除了止损距离计算逻辑，直接从Redis获取 `risk_level` 和 `stop_loss_distance_bps` 字段

#### 2.5 持仓时长获取函数
- **原函数**: `calculate_position_duration()` - 计算持仓时长
- **新函数**: `get_position_duration()` - 优先从Redis获取预计算的时长
- **修改**: 优先使用Redis中的 `position_duration` 字段，仅在缺失时才进行时间计算作为备用

### 3. 微观结构数据优化

#### 3.1 压力指标
- **原逻辑**: 基于OBI和买卖比计算压力指标
- **新逻辑**: 直接从Redis获取 `buy_pressure`、`sell_pressure`、`volume_pressure` 字段

#### 3.2 订单簿状态
- **原逻辑**: 模拟价差和订单簿深度
- **新逻辑**: 从Redis获取真实的 `spread`、`spread_bps`、`orderbook_depth` 数据

#### 3.3 交易流分析
- **原逻辑**: 模拟交易强度和硬编码大单数据
- **新逻辑**: 从Redis获取 `trade_intensity` 和 `recent_large_orders` 数据

### 4. 市场状态数据优化

#### 4.1 距离上轨计算
- **原逻辑**: 实时计算基差距离布林带上轨的距离
- **新逻辑**: 从Redis获取预计算的 `distance_to_upper_bps` 字段

### 5. 数据转换说明
保留的数据转换仅用于显示格式化：
- 基差转换为百分比 (`basis * 100`)
- 基差转换为基点 (`basis * 10000`) 
- 资金费率转换为百分比 (`funding_rate * 100`)
- 布林带数据转换为百分比

所有转换都添加了明确注释说明其仅用于显示目的。

## 需要交易机器人提供的Redis字段

### 仓位数据字段
```python
position_data = {
    'entry_cost': float,                    # 预计算的入场成本
    'profit_target_basis': float,           # 止盈目标基差
    'profit_distance_bps': float,           # 距离止盈的基点数
    'basis_change_bps': float,              # 基差变化基点数
    'risk_level': str,                      # 风险等级 ('safe', 'warning', 'danger')
    'stop_loss_distance_bps': float,        # 距离止损线的基点数
    'position_duration': str,               # 持仓时长显示字符串
    # ... 其他现有字段
}
```

### 微观结构数据字段
```python
micro_data = {
    'buy_pressure': float,                  # 买压 (0-100)
    'sell_pressure': float,                 # 卖压 (0-100)  
    'volume_pressure': float,               # 成交量压力 (0-100)
    'spread': float,                        # 价差
    'spread_bps': float,                    # 价差基点数
    'orderbook_depth': list,                # 订单簿深度显示列表
    'trade_intensity': int,                 # 交易强度 (ticks/min)
    'recent_large_orders': list,            # 近期大单列表
    # ... 其他现有字段
}
```

### 市场状态数据字段
```python
market_state = {
    'distance_to_upper_bps': float,         # 距离布林带上轨的基点数
    # ... 其他现有字段
}
```

## 验证结果

✅ **无API调用**: 已移除所有OKX API相关代码  
✅ **无自主计算**: 所有业务逻辑计算都改为从Redis获取  
✅ **无数据冲突**: 消除了重复计算导致的数据不一致问题  
✅ **仅显示转换**: 保留的数学运算仅用于单位转换和格式化显示  
✅ **Redis依赖**: 所有数据都依赖Redis消息，确保数据一致性  

## 建议

1. **交易机器人端**: 需要在相应的Redis消息中添加上述预计算字段
2. **监控**: 建议添加Redis数据完整性监控，确保关键字段不缺失
3. **备用机制**: 对于关键显示数据，建议保留"N/A"显示作为数据缺失时的备用方案
