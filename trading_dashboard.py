"""
实时交易仪表盘 - 基于Redis消息队列的高性能架构
支持低延迟实时数据更新和丰富的可视化功能

重要原则：
1. 所有数据都来源于Redis消息，不进行任何自主计算或API调用
2. 仅进行必要的单位转换用于显示（如基差转百分比、bps等）
3. 不重复计算任何业务逻辑，避免数据冲突
4. 所有计算密集型数据（如PNL、风险指标等）都应由交易机器人预计算并通过Redis传递
"""
import asyncio
import json
import logging
from collections import deque
from datetime import datetime
from typing import Dict, Any, Optional

import redis.asyncio as redis
from rich.console import Console
from rich.layout import Layout
from rich.live import Live
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from rich.progress import SpinnerColumn, TextColumn, Progress

# 注意：仪表盘不再直接调用OKX API，所有数据都来自Redis消息
# from okx_connector import OKXConnector  # 已移除

# --- 配置项 ---
REDIS_HOST = 'localhost'
REDIS_PORT = 6379
REDIS_PASSWORD = None
REDIS_DB = 0
MAX_LOG_LINES = 15
MAX_BASIS_HISTORY = 50  # 用于基差历史图的最大历史点数
REFRESH_RATE = 1.0  # UI刷新频率（秒）

# Redis频道配置
REDIS_CHANNELS = {
    'system': 'trading_bot:system',
    'positions': 'trading_bot:positions', 
    'market': 'trading_bot:market',
    'microstructure': 'trading_bot:microstructure',
    'alerts': 'trading_bot:alerts',
    'strategy': 'trading_bot:strategy'  # 添加策略数据频道订阅
}

# --- 全局状态管理 ---
console = Console()
layout = Layout()

# 注意：不再使用OKX连接器，所有数据来自Redis
# okx_connector = None  # 已移除

# 数据存储结构
system_status = {
    "status": "Initializing...",
    "last_update": "N/A",
    "ws_connection": "Disconnected",
    "start_time": None,
    "trade_pair": "Unknown",
    "subscribed_instruments": 0,
    "redis_connection": "Disconnected"
}

active_positions = {}

# 策略数据存储
strategy_data = {
    'pairs': {},
    'timestamp': None,
    'is_running': False
}

market_states = {
    # 'BTC-USDT': {
    #     'basis': 0.0,
    #     'basis_history': deque(maxlen=MAX_BASIS_HISTORY),
    #     'spot_price': 0.0,
    #     'futures_price': 0.0,
    #     'bb_middle': 0.0,
    #     'bb_upper': 0.0,
    #     'bb_lower': 0.0,
    #     'last_update': '',
    #     'is_realtime': False
    # }
}

microstructure_states = {
    # 'BTC-USDT': {
    #     'obi': 0.0,
    #     'buy_sell_ratio': 1.0,
    #     'micro_signal': 'neutral',
    #     'micro_confidence': 0.0
    # }
}

api_limiter_status = {
    'order_rate_limiter': {
        'tokens': 0,
        'capacity': 1,
        'refill_rate': 0.1
    }
}

last_trade_events = deque(maxlen=MAX_LOG_LINES)
last_alerts = deque(maxlen=MAX_LOG_LINES)

# 连接状态
connection_stats = {
    'redis_connected': False,
    'messages_received': 0,
    'last_message_time': None,
    'connection_errors': 0
}


class DashboardApp:
    """仪表盘主应用类 - 管理Redis连接和事件分发"""
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self.pubsub = None
        self.data_queue = asyncio.Queue()
        self.is_running = False
        self.logger = logging.getLogger(__name__)
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    async def connect_redis(self) -> bool:
        """连接到Redis服务器"""
        try:
            self.redis_client = redis.Redis(
                host=REDIS_HOST,
                port=REDIS_PORT,
                password=REDIS_PASSWORD,
                db=REDIS_DB,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True
            )
            
            # 测试连接
            await self.redis_client.ping()
            connection_stats['redis_connected'] = True
            system_status['redis_connection'] = "Connected"
            
            self.logger.info(f"✅ Redis连接成功: {REDIS_HOST}:{REDIS_PORT}")
            return True
            
        except Exception as e:
            connection_stats['redis_connected'] = False
            connection_stats['connection_errors'] += 1
            system_status['redis_connection'] = f"Failed: {str(e)[:30]}"
            
            self.logger.error(f"❌ Redis连接失败: {e}")
            return False
    
    async def _message_handler(self, message):
        """处理Redis消息并放入内部队列"""
        try:
            if message['type'] == 'message':
                data = json.loads(message['data'])
                await self.data_queue.put(data)
                
                # 更新统计信息
                connection_stats['messages_received'] += 1
                connection_stats['last_message_time'] = datetime.now()
                
        except (json.JSONDecodeError, KeyError) as e:
            self.logger.warning(f"解析Redis消息失败: {e}")
        except Exception as e:
            self.logger.error(f"处理Redis消息失败: {e}")
    
    async def _subscribe_to_redis(self):
        """订阅Redis频道"""
        if not self.redis_client:
            return

        try:
            self.pubsub = self.redis_client.pubsub()

            # 订阅所有频道
            channels_to_subscribe = list(REDIS_CHANNELS.values())
            await self.pubsub.subscribe(*channels_to_subscribe)

            self.logger.info(f"📡 已订阅Redis频道: {list(REDIS_CHANNELS.keys())}")
            self.logger.info(f"📡 订阅的具体频道: {channels_to_subscribe}")

            # 持续监听消息
            while self.is_running:
                try:
                    # 使用超时避免无限阻塞
                    message = await asyncio.wait_for(self.pubsub.get_message(), timeout=1.0)
                    if message and message['type'] == 'message':
                        channel = message['channel']
                        self.logger.debug(f"📨 收到Redis消息: {channel}")
                        await self._message_handler(message)
                except asyncio.TimeoutError:
                    # 超时是正常的，继续循环
                    continue
                except Exception as e:
                    self.logger.error(f"处理Redis消息时出错: {e}")
                    await asyncio.sleep(0.1)

        except Exception as e:
            self.logger.error(f"❌ Redis订阅失败: {e}")
            connection_stats['redis_connected'] = False
            system_status['redis_connection'] = f"Subscription failed: {str(e)[:30]}"
    
    async def _dispatch_events(self):
        """从内部队列取出数据并分发给状态更新函数"""
        while self.is_running:
            try:
                # 设置超时避免阻塞
                data = await asyncio.wait_for(self.data_queue.get(), timeout=1.0)
                
                event_type = data.get('event_type')
                payload = data.get('payload', {})
                
                # 根据事件类型分发处理
                if event_type == 'system_status':
                    self.update_system_status(payload)
                elif event_type == 'strategy_update':
                    self.update_strategy_data(payload)
                elif event_type in ['position_opened', 'position_closed', 'position_recovered']:
                    self.update_positions(event_type, payload)
                elif event_type == 'market_state':
                    self.update_market_state(payload)
                elif event_type == 'signal_generated':
                    self.add_signal_event(payload)
                elif event_type == 'microstructure_update':
                    self.update_microstructure(payload)
                elif event_type == 'api_limiter_status':
                    self.update_api_limiter(payload)
                elif event_type == 'alert':
                    self.add_alert(payload)
                else:
                    # 其他交易事件作为trade events处理
                    self.add_trade_event(event_type, payload)
                
                self.data_queue.task_done()
                
            except asyncio.TimeoutError:
                continue  # 超时继续循环
            except Exception as e:
                self.logger.error(f"事件分发失败: {e}")
    
    def update_system_status(self, payload: Dict[str, Any]):
        """更新系统状态"""
        details = payload.get('details', {})
        system_status.update({
            'status': payload.get('status', 'Unknown'),
            'last_update': payload.get('timestamp', datetime.now().isoformat()),
            'start_time': details.get('start_time'),
            'trade_pair': details.get('trade_pair', system_status.get('trade_pair')),
            'subscribed_instruments': details.get('subscribed_instruments', 0)
        })
        
        # WebSocket连接状态（从Redis消息推断）
        if payload.get('status') == 'Running':
            system_status['ws_connection'] = "Connected"

    def update_strategy_data(self, payload: Dict[str, Any]):
        """处理策略数据更新 - 核心数据源"""
        try:
            global strategy_data
            
            # 更新系统状态
            system_status.update({
                'status': 'Running' if payload.get('is_running', False) else 'Stopped',
                'last_update': payload.get('timestamp', datetime.now().isoformat()),
                'uptime_seconds': payload.get('uptime_seconds', 0),
                'loop_count': payload.get('loop_count', 0)
            })

            # 🔥 关键修复：更新全局strategy_data变量
            strategy_data.update({
                'pairs': payload.get('pairs', {}),
                'timestamp': payload.get('timestamp'),
                'is_running': payload.get('is_running', False),
                'uptime_seconds': payload.get('uptime_seconds', 0),
                'loop_count': payload.get('loop_count', 0),
                'manager': payload.get('manager', {}),
                'data_flow_monitoring': payload.get('data_flow_monitoring', {}),
                'heartbeat': payload.get('heartbeat', {})
            })

            # 处理交易对数据
            pairs_data = payload.get('pairs', {})
            for pair_name, pair_data in pairs_data.items():
                self._update_pair_market_state(pair_name, pair_data)
                self._update_pair_position_data(pair_name, pair_data)
            
            # 调试日志：确认数据更新
            pairs_count = len(pairs_data)
            tech_indicators_count = 0
            for pair_data in pairs_data.values():
                if pair_data.get('technical_indicators'):
                    tech_indicators_count += 1
            
            self.logger.debug(f"📈 策略数据更新: {pairs_count}个交易对, {tech_indicators_count}个包含技术指标")

        except Exception as e:
            self.logger.error(f"处理策略数据失败: {e}")
            import traceback
            self.logger.error(f"错误详情: {traceback.format_exc()}")

    def _update_pair_market_state(self, pair_name: str, pair_data: Dict[str, Any]):
        """更新交易对市场状态"""
        try:
            # 初始化市场状态
            if pair_name not in market_states:
                market_states[pair_name] = {
                    'basis_history': deque(maxlen=MAX_BASIS_HISTORY)
                }

            # 获取价格和基差数据
            prices = pair_data.get('prices', {})
            basis_data = pair_data.get('basis', {})
            indicators = pair_data.get('indicators', {})

            # 更新基差历史
            current_basis = basis_data.get('current_basis', 0.0)
            if current_basis is not None:
                market_states[pair_name]['basis_history'].append(current_basis * 100)  # 转换为基点显示

            # 更新市场状态数据
            bollinger_bands = indicators.get('bollinger_bands', {})
            market_states[pair_name].update({
                'spot_price': prices.get('spot_price', 0.0),
                'futures_price': prices.get('futures_price', 0.0),
                'basis': current_basis,
                'funding_rate': basis_data.get('funding_rate', 0.0),
                'bb_middle': bollinger_bands.get('strategic_middle', 0.0),
                'bb_upper': bollinger_bands.get('strategic_upper', 0.0),
                'bb_lower': bollinger_bands.get('strategic_lower', 0.0),
                'last_update': pair_data.get('last_update', ''),
                'is_realtime': True,
                'update_source': 'strategy_engine'
            })

        except Exception as e:
            self.logger.error(f"更新{pair_name}市场状态失败: {e}")

    def _update_pair_position_data(self, pair_name: str, pair_data: Dict[str, Any]):
        """更新交易对仓位数据"""
        try:
            position_data = pair_data.get('position', {})

            # 如果有活跃仓位，更新仓位信息
            if position_data.get('active', False):
                active_positions[pair_name] = {
                    'position_id': f"{pair_name}_active",
                    'type': position_data.get('type', 'unknown'),
                    'entry_price': position_data.get('entry_price', 0.0),
                    'current_pnl': position_data.get('current_pnl', 0.0),
                    'status': 'open',
                    'last_update': datetime.now().isoformat()
                }
            else:
                # 移除非活跃仓位
                if pair_name in active_positions:
                    del active_positions[pair_name]

        except Exception as e:
            self.logger.error(f"更新{pair_name}仓位数据失败: {e}")
    
    def update_positions(self, event_type: str, payload: Dict[str, Any]):
        """更新仓位信息"""
        pair_name = payload.get('pair_name', 'Unknown')
        position_data = payload.get('position_data', {})
        
        if event_type in ['position_opened', 'position_recovered']:
            active_positions[pair_name] = {
                'position_id': position_data.get('position_id', 'Unknown'),
                'type': position_data.get('position_type', 'Unknown'),
                'size': position_data.get('final_position_size', 0.0),
                'entry_basis': position_data.get('entry_basis', 0.0),
                'entry_spot_price': position_data.get('entry_spot_price', 0.0),
                'entry_futures_price': position_data.get('entry_futures_price', 0.0),
                'unrealized_pnl': position_data.get('unrealized_pnl', 0.0),
                'open_time': position_data.get('open_time')
            }
        elif event_type == 'position_closed':
            if pair_name in active_positions:
                del active_positions[pair_name]
    
    def update_market_state(self, payload: Dict[str, Any]):
        """更新市场状态"""
        pair_name = payload.get('pair_name', 'Unknown')
        market_data = payload.get('market_state', {})
        
        if pair_name not in market_states:
            market_states[pair_name] = {
                'basis_history': deque(maxlen=MAX_BASIS_HISTORY)
            }
        
        # 更新基差历史（用于sparkline图表）
        # 注意：基差数据来自Redis，这里只做单位转换用于显示
        basis = market_data.get('basis', 0.0)
        if basis is not None:
            market_states[pair_name]['basis_history'].append(basis * 100)  # 仅转换单位为基点用于显示
        else:
            market_states[pair_name]['basis_history'].append(0.0)
        
        # 更新其他市场状态
        market_states[pair_name].update({
            'basis': basis,
            'spot_price': market_data.get('spot_price', 0.0),
            'futures_price': market_data.get('futures_price', 0.0),
            'funding_rate': market_data.get('funding_rate', 0.0),
            'bb_middle': market_data.get('bb_middle', 0.0),
            'bb_upper': market_data.get('bb_upper', 0.0),
            'bb_lower': market_data.get('bb_lower', 0.0),
            'last_update': payload.get('timestamp', ''),
            'is_realtime': market_data.get('is_realtime', False),
            'update_source': market_data.get('update_source', 'unknown')
        })
    
    def add_signal_event(self, payload: Dict[str, Any]):
        """添加交易信号事件"""
        pair_name = payload.get('pair_name', 'Unknown')
        signal_data = payload.get('signal_data', {})
        timestamp = payload.get('timestamp', datetime.now().isoformat())
        
        # 格式化信号消息
        signal_type = signal_data.get('signal_type', 'Unknown')
        confidence = signal_data.get('confidence', 0.0)
        risk_grade = signal_data.get('risk_grade', 'Unknown')
        
        message = (f"[{timestamp[-8:]}] [bold green]信号[/]: {pair_name} - {signal_type} | "
                  f"评级: {risk_grade} | 置信度: {confidence:.2f}")
        
        last_trade_events.append(message)
    
    def update_microstructure(self, payload: Dict[str, Any]):
        """更新微观结构数据"""
        pair_name = payload.get('pair_name', 'Unknown')
        micro_data = payload.get('micro_data', {})
        
        microstructure_states[pair_name] = {
            'obi': micro_data.get('obi', 0.0),
            'buy_sell_ratio': micro_data.get('buy_sell_ratio', 1.0),
            'micro_signal': micro_data.get('micro_signal', 'neutral'),
            'micro_confidence': micro_data.get('micro_confidence', 0.0),
            'obi_signal': micro_data.get('obi_signal', 'neutral'),
            'flow_signal': micro_data.get('flow_signal', 'neutral'),
            'last_update': datetime.now().isoformat()
        }
    
    def update_api_limiter(self, payload: Dict[str, Any]):
        """更新API限制器状态"""
        limiter_data = payload.get('limiter_data', {})
        api_limiter_status.update(limiter_data)
    
    def add_alert(self, payload: Dict[str, Any]):
        """添加警报"""
        level = payload.get('level', 'INFO').upper()
        message = payload.get('message', '')
        timestamp = payload.get('timestamp', datetime.now().isoformat())
        
        # 警报图标和颜色映射
        icon_map = {
            "INFO": "ℹ️",
            "WARNING": "⚠️", 
            "ERROR": "❌",
            "CRITICAL": "🔥"
        }
        color_map = {
            "INFO": "white",
            "WARNING": "yellow",
            "ERROR": "red", 
            "CRITICAL": "bold red on white"
        }
        
        icon = icon_map.get(level, "ℹ️")
        color = color_map.get(level, "white")
        
        formatted_message = f"[{timestamp[-8:]}] [{color}]{icon} {message}[/]"
        last_alerts.append(formatted_message)
    
    def add_trade_event(self, event_type: str, payload: Dict[str, Any]):
        """添加交易事件"""
        pair_name = payload.get('pair_name', 'Unknown')
        timestamp = payload.get('timestamp', datetime.now().isoformat())
        
        if event_type == 'position_opened':
            trade_data = payload.get('trade_data', {})
            entry_basis = trade_data.get('entry_basis', 0)
            # 注意：基差数据来自Redis，这里只做单位转换用于显示
            entry_basis_pct = (entry_basis * 100) if entry_basis is not None else 0.0
            message = (f"[{timestamp[-8:]}] [bold cyan]开仓[/]: {pair_name} | "
                      f"基差: {entry_basis_pct:.4f}%")
        elif event_type == 'position_closed':
            trade_data = payload.get('trade_data', {})
            pnl = trade_data.get('realized_pnl', 0.0)
            pnl_style = "green" if pnl >= 0 else "red"
            message = (f"[{timestamp[-8:]}] [bold magenta]平仓[/]: {pair_name} | "
                      f"盈亏: [{pnl_style}]{pnl:.4f}[/]")
        else:
            message = f"[{timestamp[-8:]}] {event_type}: {pair_name}"
        
        last_trade_events.append(message)
    
    async def start(self):
        """启动仪表盘应用"""
        self.is_running = True
        
        # 连接Redis
        if not await self.connect_redis():
            console.print("[bold red]Redis连接失败，仪表盘将无法接收实时数据[/bold red]")
            return False
        
        # 启动异步任务
        redis_task = asyncio.create_task(self._subscribe_to_redis())
        dispatcher_task = asyncio.create_task(self._dispatch_events())
        
        try:
            # 启动后台任务，不等待完成
            await asyncio.gather(redis_task, dispatcher_task, return_exceptions=True)
        except Exception as e:
            self.logger.error(f"后台任务异常: {e}")
        finally:
            self.is_running = False
            if self.pubsub:
                await self.pubsub.unsubscribe()
            if self.redis_client:
                await self.redis_client.aclose()
    
    def stop(self):
        """停止仪表盘应用"""
        self.is_running = False


# --- UI面板生成函数 ---

def make_header() -> Panel:
    """生成仪表盘头部"""
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    redis_status = "🟢" if connection_stats['redis_connected'] else "🔴"
    
    header_text = Text.assemble(
        ("高频套利机器人 - 实时监控仪表盘", "bold magenta"),
        ("  |  ", "white"),
        (f"Redis {redis_status}", "white"),
        ("  |  ", "white"), 
        (current_time, "cyan")
    )
    return Panel(header_text, border_style="green")


def make_system_status_panel() -> Panel:
    """生成系统状态面板"""
    status_table = Table.grid(expand=True)
    status_table.add_column(style="cyan")
    status_table.add_column(style="white")
    
    # 机器人状态
    status_color = "green" if "Running" in system_status['status'] else "yellow"
    status_table.add_row("机器人状态:", f"[bold {status_color}]{system_status['status']}[/]")
    
    # 最后心跳
    last_update = system_status['last_update']
    if last_update != "N/A":
        try:
            update_time = datetime.fromisoformat(last_update.replace('Z', '+00:00'))
            display_time = update_time.strftime("%H:%M:%S")
        except:
            display_time = last_update[-8:] if len(last_update) >= 8 else last_update
    else:
        display_time = "N/A"
    
    status_table.add_row("最后心跳:", display_time)
    
    # WebSocket连接
    ws_style = "green" if system_status['ws_connection'] == "Connected" else "red"
    status_table.add_row("WebSocket:", f"[{ws_style}]{system_status['ws_connection']}[/]")
    
    # Redis连接
    redis_style = "green" if connection_stats['redis_connected'] else "red"
    redis_status = "Connected" if connection_stats['redis_connected'] else system_status.get('redis_connection', 'Disconnected')
    status_table.add_row("Redis连接:", f"[{redis_style}]{redis_status}[/]")
    
    # 交易对
    status_table.add_row("交易对:", system_status.get('trade_pair', 'Unknown'))
    
    # 订阅的标的数量
    status_table.add_row("订阅标的:", str(system_status.get('subscribed_instruments', 0)))
    
    # API限制器状态
    limiter = api_limiter_status.get('order_rate_limiter', {})
    tokens = limiter.get('tokens', 0)
    capacity = limiter.get('capacity', 1)
    fill_pct = (tokens / capacity) * 100 if capacity > 0 else 0
    
    token_color = "green" if fill_pct > 50 else "yellow" if fill_pct > 20 else "red"
    status_table.add_row("API令牌:", f"[{token_color}]{tokens:.0f}/{capacity} ({fill_pct:.1f}%)[/]")
    
    # 消息统计
    msg_count = connection_stats['messages_received']
    status_table.add_row("收到消息:", str(msg_count))
    
    return Panel(status_table, title="[bold cyan]系统状态[/bold cyan]", border_style="cyan")


def make_positions_panel() -> Panel:
    """生成持仓信息面板"""
    position_table = Table(title="[bold]当前持仓[/bold]", border_style="yellow")
    position_table.add_column("交易对", justify="left", style="cyan")
    position_table.add_column("持仓状态", justify="center", style="green")
    position_table.add_column("方向", justify="center", style="white")
    position_table.add_column("大小", justify="right", style="magenta")
    position_table.add_column("入场成本", justify="right", style="white")
    position_table.add_column("入场基差率", justify="right", style="green")
    position_table.add_column("目标止盈", justify="right", style="blue")
    position_table.add_column("止盈距离", justify="right", style="blue")
    position_table.add_column("持仓时长", justify="center", style="cyan")
    position_table.add_column("未实现PNL", justify="right", style="red")
    position_table.add_column("风险度", justify="center", style="red")

    if not active_positions:
        position_table.add_row("[grey70]无活跃仓位...[/grey70]", "", "", "", "", "", "", "", "", "", "")
    else:
        for pair, pos in active_positions.items():
            # 获取当前基差
            current_basis = market_states.get(pair, {}).get('basis', 0.0)

            # 持仓状态
            pos_status = get_position_status(pos)

            # 方向显示
            pos_type = pos.get('type', 'N/A')
            direction_style = "cyan" if "short_futures_long_spot" in pos_type else "white"
            direction_display = "套利" if "short_futures_long_spot" in pos_type else pos_type

            # 入场成本和基差率
            entry_cost, entry_basis_rate = get_entry_info(pair, pos)

            # 目标止盈基差率和距离
            target_profit, profit_distance = get_profit_target(pair, pos, current_basis)

            # 持仓时长
            duration = get_position_duration(pos)

            # 未实现PNL（bps和USDT双显示）
            upl_display = format_unrealized_pnl(pos, current_basis)

            # 风险度
            risk_level = get_risk_level(pos, current_basis)

            position_table.add_row(
                pair,
                pos_status,
                Text(direction_display, style=direction_style),
                f"{pos.get('size', 0):.4f}",
                entry_cost,
                entry_basis_rate,
                target_profit,
                profit_distance,
                duration,
                upl_display,
                risk_level
            )

    return Panel(position_table, title="[bold yellow]持仓管理[/bold yellow]", border_style="yellow")


def get_position_status(pos: Dict) -> str:
    """获取持仓状态"""
    status = pos.get('status', 'unknown')
    open_time = pos.get('open_time')
    close_time = pos.get('close_time')

    if close_time:
        return "[green]已平仓[/green]"
    elif open_time:
        return "[yellow]持仓中[/yellow]"
    else:
        return "[blue]开仓中[/blue]"


def get_entry_info(pair: str, pos: Dict) -> tuple:
    """获取入场成本和基差率（从Redis数据中获取，不进行计算）"""
    # 直接从仓位数据中获取，这些数据应该在开仓时由交易机器人计算并存储
    # 注意：pair参数保留用于未来扩展，当前从pos中获取所有数据
    entry_cost = pos.get('entry_cost', 0.0)  # 从Redis获取预计算的入场成本
    entry_basis = pos.get('entry_basis', 0.0)  # 从Redis获取入场基差

    # 格式化显示，不进行计算
    if entry_cost > 0:
        cost_display = f"${entry_cost:.5f}"
    else:
        cost_display = "N/A"

    if entry_basis is not None:
        basis_pct = entry_basis * 100  # 仅做单位转换显示
        basis_display = f"{basis_pct:.5f}%"
    else:
        basis_display = "N/A"

    return cost_display, basis_display


def get_profit_target(pair: str, pos: Dict, current_basis: float) -> tuple:
    """获取目标止盈基差率和距离（从Redis数据中获取，避免重复计算）"""
    # 从仓位数据中获取预设的止盈目标，这些应该在开仓时由交易机器人设置
    # 注意：pair和current_basis参数保留用于未来扩展，当前从pos中获取所有数据
    target_basis = pos.get('profit_target_basis')  # 从Redis获取止盈目标基差
    profit_distance_bps = pos.get('profit_distance_bps')  # 从Redis获取预计算的距离

    # 如果Redis中没有这些数据，则显示N/A，不进行计算
    if target_basis is not None:
        target_basis_pct = target_basis * 100
        target_display = f"{target_basis_pct:.5f}%"
    else:
        target_display = "N/A"

    if profit_distance_bps is not None:
        distance_display = f"{profit_distance_bps:.0f} bps"
    else:
        distance_display = "N/A"

    return target_display, distance_display


def get_position_duration(pos: Dict) -> str:
    """获取持仓时长（从Redis数据中获取，避免重复计算）"""
    # 优先从Redis获取预计算的持仓时长
    duration_display = pos.get('position_duration')
    if duration_display:
        return duration_display

    # 如果Redis中没有预计算的时长，则从开仓时间计算（仅作为备用）
    open_time = pos.get('open_time')
    if not open_time:
        return "N/A"

    try:
        from datetime import datetime
        if isinstance(open_time, str):
            open_dt = datetime.fromisoformat(open_time.replace('Z', '+00:00'))
        else:
            open_dt = datetime.fromtimestamp(open_time)

        duration = datetime.now() - open_dt.replace(tzinfo=None)

        if duration.days > 0:
            return f"{duration.days}天"
        elif duration.seconds > 3600:
            hours = duration.seconds // 3600
            return f"{hours}小时"
        else:
            minutes = duration.seconds // 60
            return f"{minutes}分钟"
    except:
        return "N/A"


def format_unrealized_pnl(pos: Dict, current_basis: float) -> str:
    """格式化未实现PNL（从Redis数据中获取，不进行计算）"""
    # 直接从仓位数据中获取预计算的PNL数据
    # 注意：current_basis参数保留用于未来扩展，当前从pos中获取所有数据
    basis_change_bps = pos.get('basis_change_bps', 0.0)  # 从Redis获取基差变化bps
    pnl_usdt = pos.get('unrealized_pnl', 0.0)  # 从Redis获取未实现PNL

    # 颜色编码
    if basis_change_bps > 0 and pnl_usdt > 0:
        style = "green"
        sign = "+"
    elif basis_change_bps < 0 and pnl_usdt < 0:
        style = "red"
        sign = ""
    else:
        style = "yellow"
        sign = "+" if basis_change_bps >= 0 else ""

    return f"[{style}]{sign}{basis_change_bps:.1f} bps (${pnl_usdt:.2f})[/{style}]"


def get_risk_level(pos: Dict, current_basis: float) -> str:
    """获取风险度（从Redis数据中获取，不进行计算）"""
    # 直接从仓位数据中获取预计算的风险指标
    # 注意：current_basis参数保留用于未来扩展，当前从pos中获取所有数据
    risk_level = pos.get('risk_level')  # 从Redis获取风险等级
    stop_loss_distance_bps = pos.get('stop_loss_distance_bps')  # 从Redis获取距离止损线的距离

    # 如果Redis中有预计算的风险数据，直接使用
    if risk_level and stop_loss_distance_bps is not None:
        if risk_level == 'safe':
            return f"[green]安全 ({stop_loss_distance_bps:.0f}bps)[/green]"
        elif risk_level == 'warning':
            return f"[yellow]注意 ({stop_loss_distance_bps:.0f}bps)[/yellow]"
        elif risk_level == 'danger':
            return f"[red]危险 ({stop_loss_distance_bps:.0f}bps)[/red]"

    # 如果Redis中没有风险数据，显示N/A
    return "N/A"


def make_market_state_panel() -> Panel:
    """生成市场状态面板"""
    market_table = Table(title="[bold]实时市场状态[/bold]", border_style="blue")
    market_table.add_column("交易对", style="cyan")
    market_table.add_column("现货价格", justify="right", style="green")
    market_table.add_column("期货价格", justify="right", style="yellow")
    market_table.add_column("基差(%)", justify="right", style="white")
    market_table.add_column("距离上轨", justify="right", style="bright_yellow")
    market_table.add_column("资金费率", justify="right", style="blue")
    market_table.add_column("中轨(%)", justify="right", style="blue")
    market_table.add_column("上轨(%)", justify="right", style="blue")
    market_table.add_column("趋势", justify="center", style="magenta")
    market_table.add_column("数据源", justify="center", style="yellow")
    market_table.add_column("更新时间", justify="center", style="green")
    
    if not market_states:
        market_table.add_row("[grey70]等待市场数据...[/grey70]", "", "", "", "", "", "", "", "", "", "")
    else:
        for pair, state in market_states.items():
            # 截断长的交易对名称
            display_pair = pair[:10] if len(pair) > 10 else pair

            # 现货和期货价格
            spot_price = state.get('spot_price', 0)
            futures_price = state.get('futures_price', 0)
            spot_display = f"${spot_price:.5f}" if spot_price else "N/A"
            futures_display = f"${futures_price:.5f}" if futures_price else "N/A"

            # 基差颜色编码
            # 注意：基差数据来自Redis，这里只做单位转换和颜色编码用于显示
            basis = state.get('basis', 0)
            basis_pct = (basis * 100) if basis is not None else 0.0  # 仅转换单位为百分比用于显示
            if abs(basis_pct) > 2.0:  # 大于2%
                basis_style = "bold red"
            elif abs(basis_pct) > 1.0:  # 大于1%
                basis_style = "bold yellow"
            else:
                basis_style = "green"

            # 资金费率
            # 注意：资金费率数据来自Redis，这里只做单位转换用于显示
            funding_rate = state.get('funding_rate', 0)
            funding_rate_pct = (funding_rate * 100) if funding_rate is not None else 0.0  # 仅转换单位为百分比用于显示
            if funding_rate_pct > 0.01:  # 大于0.01%
                funding_style = "red"
            elif funding_rate_pct < -0.01:  # 小于-0.01%
                funding_style = "green"
            else:
                funding_style = "white"
            funding_display = f"[{funding_style}]{funding_rate_pct:.5f}%[/{funding_style}]"

            # 基差历史趋势（简化版sparkline）
            basis_history = state.get('basis_history', [])
            if len(basis_history) >= 2:
                recent_trend = basis_history[-1] - basis_history[-2]
                if recent_trend > 0.01:
                    trend_indicator = "↗"
                elif recent_trend < -0.01:
                    trend_indicator = "↘"
                else:
                    trend_indicator = "→"
                sparkline_display = f"{trend_indicator}"
            else:
                sparkline_display = "—"

            # 数据源显示
            is_realtime = state.get('is_realtime', False)
            source_display = "[bold green]实时[/]" if is_realtime else "[yellow]定时[/]"

            # 更新时间显示
            last_update = state.get('last_update', '')
            if last_update:
                try:
                    update_time = datetime.fromisoformat(last_update.replace('Z', '+00:00'))
                    time_display = update_time.strftime("%H:%M")
                except:
                    time_display = last_update[-5:] if len(last_update) >= 5 else last_update
            else:
                time_display = "--"

            # 从Redis数据中获取距离上轨的距离，避免重复计算
            distance_to_upper_bps = state.get('distance_to_upper_bps')  # 从Redis获取预计算的距离

            if distance_to_upper_bps is not None:
                # 根据距离给颜色编码
                if distance_to_upper_bps > -10:  # 距离上轨小于10个基点
                    distance_style = "green"
                    distance_display = f"[{distance_style}]{distance_to_upper_bps:+.1f}bp[/]"
                elif distance_to_upper_bps > -50:  # 距离上轨10-50个基点
                    distance_style = "yellow"
                    distance_display = f"[{distance_style}]{distance_to_upper_bps:+.1f}bp[/]"
                else:  # 距离上轨超过50个基点
                    distance_style = "red"
                    distance_display = f"[{distance_style}]{distance_to_upper_bps:+.1f}bp[/]"
            else:
                distance_display = "N/A"

            # 安全处理可能为None的值
            # 注意：布林带数据来自Redis，这里只做单位转换用于显示
            bb_middle = state.get('bb_middle', 0)
            bb_upper = state.get('bb_upper', 0)
            bb_middle_pct = (bb_middle * 100) if bb_middle is not None else 0.0  # 仅转换单位为百分比用于显示
            bb_upper_pct = (bb_upper * 100) if bb_upper is not None else 0.0  # 仅转换单位为百分比用于显示

            market_table.add_row(
                display_pair,
                spot_display,
                futures_display,
                f"[{basis_style}]{basis_pct:.5f}[/]",
                distance_display,
                funding_display,
                f"{bb_middle_pct:.5f}",
                f"{bb_upper_pct:.5f}",
                sparkline_display,
                source_display,
                time_display
            )
    
    return Panel(market_table, title="[bold blue]市场监控[/bold blue]", border_style="blue")


def make_microstructure_panel() -> Panel:
    """生成微观结构面板 - 三层架构：核心压力指标 → 订单簿状态 → 交易流分析"""
    from rich.columns import Columns
    from rich.console import Group
    from rich.align import Align

    # 获取所有配置的交易对
    try:
        from config import get_enabled_trading_pairs
        trading_pairs = get_enabled_trading_pairs()
        configured_pairs = [f"{pair['spot_id']}-{pair['futures_id']}" for pair in trading_pairs]
    except:
        configured_pairs = ["BTC-USDT", "ETH-USDT", "DOGE-USDT"]  # 默认交易对

    panels = []

    for pair_key in configured_pairs:
        # 获取该交易对的微观结构数据
        state = microstructure_states.get(pair_key, {})

        # === 1. 核心压力指标 (Core Pressure Indicators) ===
        obi = state.get('obi', 0.0)
        buy_sell_ratio = state.get('buy_sell_ratio', 1.0)
        micro_confidence = state.get('micro_confidence', 0.0)

        # 从Redis数据中获取预计算的压力指标，避免重复计算
        buy_pressure = state.get('buy_pressure', 50.0)  # 从Redis获取买压
        sell_pressure = state.get('sell_pressure', 50.0)  # 从Redis获取卖压
        volume_pressure = state.get('volume_pressure', 50.0)  # 从Redis获取成交量压力

        # 压力条显示
        buy_bar = "█" * int(buy_pressure / 10) + "░" * (10 - int(buy_pressure / 10))
        sell_bar = "█" * int(sell_pressure / 10) + "░" * (10 - int(sell_pressure / 10))

        pressure_display = f"""[bold cyan]{pair_key}[/bold cyan]
[green]买压: {buy_bar} {buy_pressure:.1f}%[/green]
[red]卖压: {sell_bar} {sell_pressure:.1f}%[/red]
[yellow]OBI: {obi:+.3f}[/yellow] | [blue]成交量压力: {volume_pressure:.1f}%[/blue]"""

        # === 2. 订单簿状态 (Order Book State) ===
        # 从Redis数据中获取订单簿信息，避免模拟计算
        spread = state.get('spread', 0.0)  # 从Redis获取价差
        spread_bps = state.get('spread_bps', 0.0)  # 从Redis获取价差bps
        orderbook_depth = state.get('orderbook_depth', [])  # 从Redis获取订单簿深度

        # 如果有订单簿深度数据，使用真实数据；否则显示等待数据
        if orderbook_depth:
            depth_levels = orderbook_depth
        else:
            depth_levels = [
                "等待订单簿数据...",
                "卖5 ░░░░░░░░",
                "卖4 ░░░░░░░░",
                "卖3 ░░░░░░░░",
                "卖2 ░░░░░░░░",
                "卖1 ░░░░░░░░",
                "-" * 15,
                "买1 ░░░░░░░░",
                "买2 ░░░░░░░░",
                "买3 ░░░░░░░░",
                "买4 ░░░░░░░░",
                "买5 ░░░░░░░░"
            ]

        orderbook_display = f"""[bold white]订单簿状态[/bold white]
价差: {spread:.5f} ({spread_bps:.1f} bps)
{chr(10).join(depth_levels)}"""

        # === 3. 交易流分析 (Trade Flow Analysis) ===
        micro_signal = state.get('micro_signal', 'neutral')
        obi_signal = state.get('obi_signal', 'neutral')
        flow_signal = state.get('flow_signal', 'neutral')

        # 从Redis数据中获取交易强度，避免模拟计算
        trade_intensity = state.get('trade_intensity', 0)  # 从Redis获取交易强度

        # 信号颜色映射
        signal_colors = {
            'bullish': 'green', 'buy': 'green', 'inflow': 'green',
            'bearish': 'red', 'sell': 'red', 'outflow': 'red',
            'neutral': 'white'
        }

        micro_color = signal_colors.get(micro_signal, 'white')
        obi_color = signal_colors.get(obi_signal, 'white')
        flow_color = signal_colors.get(flow_signal, 'white')

        # 从Redis数据中获取近期大单，避免硬编码
        recent_large_orders = state.get('recent_large_orders', [])
        if recent_large_orders:
            large_orders_text = "\n".join(recent_large_orders)
        else:
            large_orders_text = "等待大单数据..."

        flow_display = f"""[bold white]交易流分析[/bold white]
交易强度: {trade_intensity} ticks/min
微观信号: [{micro_color}]{micro_signal.upper()}[/{micro_color}] ({micro_confidence:.2f})
OBI信号: [{obi_color}]{obi_signal.upper()}[/{obi_color}]
资金流: [{flow_color}]{flow_signal.upper()}[/{flow_color}]

[bold yellow]近期大单[/bold yellow]
{large_orders_text}"""

        # 组合三个部分
        pair_content = Group(
            Align.left(pressure_display),
            "",
            Align.left(orderbook_display),
            "",
            Align.left(flow_display)
        )

        panels.append(Panel(pair_content, title=f"[bold green]{pair_key}[/bold green]", border_style="green", width=35))

    # 如果没有数据，显示等待信息
    if not panels:
        waiting_content = Group(
            Align.center("[bold yellow]等待微观结构数据...[/bold yellow]"),
            "",
            Align.center("[grey70]配置的交易对:[/grey70]"),
            Align.center(f"[cyan]{', '.join(configured_pairs)}[/cyan]")
        )
        panels.append(Panel(waiting_content, title="[bold green]微观结构[/bold green]", border_style="green"))

    # 使用列布局显示多个交易对
    if len(panels) == 1:
        return panels[0]
    else:
        return Panel(Columns(panels, equal=True, expand=True),
                    title="[bold green]微观结构洞察[/bold green]", border_style="green")


def make_technical_indicators_panel() -> Panel:
    """生成技术指标面板 - 显示策略引擎计算的技术指标"""
    from rich.table import Table

    # 获取所有配置的交易对
    try:
        from config import get_enabled_trading_pairs
        trading_pairs = get_enabled_trading_pairs()
        configured_pairs = [f"{pair['spot_id']}-{pair['futures_id']}" for pair in trading_pairs]
    except:
        configured_pairs = ["BTC-USDT", "ETH-USDT", "DOGE-USDT"]  # 默认交易对

    # 创建技术指标表格
    tech_table = Table(show_header=True, header_style="bold cyan")
    tech_table.add_column("交易对", style="cyan", width=12)
    tech_table.add_column("ATR", style="white", width=8)
    tech_table.add_column("ADX", style="white", width=8)
    tech_table.add_column("+DI", style="green", width=8)
    tech_table.add_column("-DI", style="red", width=8)
    tech_table.add_column("BB位置", style="yellow", width=10)
    tech_table.add_column("Z分数", style="white", width=8)
    tech_table.add_column("信号强度", style="white", width=10)

    for pair_key in configured_pairs:
        # 从全局状态获取策略数据
        global strategy_data
        pair_data = strategy_data.get('pairs', {})
        
        # 🔥 关键修复：处理交易对名称格式转换
        # 仪表盘使用横杠格式 (BTC-USDT)，策略管理器使用下划线格式 (BTC_USDT)
        strategy_pair_key = pair_key.replace('-', '_')  # BTC-USDT -> BTC_USDT
        
        strategy_data_for_pair = pair_data.get(strategy_pair_key, {})
        tech_indicators = strategy_data_for_pair.get('technical_indicators', {})
        indicators_data = strategy_data_for_pair.get('indicators', {})
        
        # 🔍 增强数据验证和调试（全面调试信息）
        if pair_key == configured_pairs[0]:  # 只在第一个交易对时输出调试信息
            available_pairs = list(pair_data.keys()) if pair_data else []
            has_tech_indicators = bool(tech_indicators)
            incremental_indicators = tech_indicators.get('incremental_indicators', {})
            dashboard_info = tech_indicators.get('dashboard_info', {})
            
            console.print(f"[dim]📊 仪表盘数据接收调试:")
            console.print(f"  可用交易对: {available_pairs}")
            console.print(f"  技术指标存在: {has_tech_indicators}")
            console.print(f"  ATR值: {tech_indicators.get('atr')}")
            console.print(f"  ADX值: {tech_indicators.get('adx')}")
            console.print(f"  +DI值: {incremental_indicators.get('plus_di')}")
            console.print(f"  -DI值: {incremental_indicators.get('minus_di')}")
            console.print(f"  指标就绪: {dashboard_info.get('all_ready', False)}")
            console.print(f"  传输计数: {dashboard_info.get('transmission_count', 0)}")
            console.print(f"  更新时间: {strategy_data.get('timestamp', 'N/A')}")
            console.print(f"[/dim]")

        # 显示交易对名称
        display_pair = pair_key.replace('-', '/')

        # ATR指标
        atr = tech_indicators.get('atr')
        atr_display = f"{atr:.6f}" if atr is not None else "N/A"

        # ADX指标
        adx = tech_indicators.get('adx')
        adx_display = f"{adx:.1f}" if adx is not None else "N/A"

        # 方向性指标
        incremental_indicators = tech_indicators.get('incremental_indicators', {})
        plus_di = incremental_indicators.get('plus_di')
        minus_di = incremental_indicators.get('minus_di')
        plus_di_display = f"{plus_di:.1f}" if plus_di is not None else "N/A"
        minus_di_display = f"{minus_di:.1f}" if minus_di is not None else "N/A"

        # 布林带位置
        bb_position = tech_indicators.get('bb_position', 'unknown')
        bb_position_colors = {
            'upper': 'red',
            'middle': 'yellow',
            'lower': 'green',
            'above_upper': 'bright_red',
            'below_lower': 'bright_green',
            'unknown': 'grey70'
        }
        bb_color = bb_position_colors.get(bb_position, 'white')
        bb_display = f"[{bb_color}]{bb_position}[/{bb_color}]"

        # Z分数
        z_score = indicators_data.get('z_score')
        z_score_display = f"{z_score:.2f}" if z_score is not None else "N/A"
        if z_score is not None:
            if abs(z_score) > 2:
                z_score_display = f"[red]{z_score_display}[/red]"
            elif abs(z_score) > 1:
                z_score_display = f"[yellow]{z_score_display}[/yellow]"
            else:
                z_score_display = f"[green]{z_score_display}[/green]"

        # 信号强度
        signal_strength = indicators_data.get('signal_strength')
        signal_strength_display = f"{signal_strength:.2f}" if signal_strength is not None else "N/A"

        tech_table.add_row(
            display_pair,
            atr_display,
            adx_display,
            plus_di_display,
            minus_di_display,
            bb_display,
            z_score_display,
            signal_strength_display
        )

    return Panel(tech_table, title="[bold cyan]技术指标监控[/bold cyan]", border_style="cyan")


def make_alerts_panel() -> Panel:
    """生成警报面板"""
    content = "\n".join(last_alerts) if last_alerts else "[grey70]暂无警报...[/grey70]"
    return Panel(Text(content, style="white"), title="[bold yellow]系统警报[/bold yellow]", border_style="yellow")


def make_trade_events_panel() -> Panel:
    """生成交易事件面板"""
    content = "\n".join(last_trade_events) if last_trade_events else "[grey70]等待交易事件...[/grey70]"
    return Panel(Text(content, style="white"), title="[bold white]关键交易事件[/bold white]", border_style="white")


def update_dashboard():
    """更新整个仪表盘布局"""
    layout["header"].update(make_header())
    
    # 左侧面板
    layout["left"].split_column(
        make_system_status_panel(),
        make_positions_panel(), 
        make_market_state_panel()
    )
    
    # 右侧面板
    layout["right"].split_column(
        make_microstructure_panel(),
        make_technical_indicators_panel(),
        make_data_flow_monitoring_panel(),
        make_alerts_panel(),
        make_trade_events_panel()
    )


def initialize_microstructure_data():
    """初始化所有配置交易对的微观结构数据"""
    global microstructure_states

    try:
        from config import get_enabled_trading_pairs
        trading_pairs = get_enabled_trading_pairs()

        for pair_config in trading_pairs:
            pair_key = f"{pair_config['spot_id']}-{pair_config['futures_id']}"

            if pair_key not in microstructure_states:
                microstructure_states[pair_key] = {
                    'obi': 0.0,
                    'buy_sell_ratio': 1.0,
                    'micro_signal': 'neutral',
                    'micro_confidence': 0.0,
                    'obi_signal': 'neutral',
                    'flow_signal': 'neutral',
                    'last_update': datetime.now().isoformat()
                }
    except Exception as e:
        logging.error(f"初始化微观结构数据失败: {e}")


# 注意：已移除 update_market_data 和 market_data_updater 函数
# 所有市场数据现在都通过Redis消息获取，不再直接调用OKX API


async def main():
    """主程序入口"""
    # 初始化数据结构
    initialize_microstructure_data()

    # 注意：不再初始化OKX连接器，所有数据来自Redis消息
    logging.info("仪表盘启动 - 所有数据来源于Redis消息")

    # 设置基础布局
    layout.split_column(
        Layout(name="header", size=3),
        Layout(ratio=1, name="main")
    )
    layout["main"].split_row(
        Layout(name="left", ratio=2),
        Layout(name="right", ratio=1)
    )
    
    # 创建仪表盘应用
    app = DashboardApp()
    
    # 显示连接进度
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("正在连接Redis服务器...", total=None)

        # 连接Redis
        if not await app.connect_redis():
            console.print("[bold red]Redis连接失败，仪表盘将无法接收实时数据[/bold red]")
            return

        progress.update(task, description="Redis连接已建立，启动仪表盘...")
        await asyncio.sleep(1)

    # 启动后台任务
    app.is_running = True
    redis_task = asyncio.create_task(app._subscribe_to_redis())
    dispatcher_task = asyncio.create_task(app._dispatch_events())
    # 注意：不再启动market_data_updater任务，所有数据来自Redis

    # 启动实时UI
    with Live(layout, screen=True, redirect_stderr=False, refresh_per_second=1/REFRESH_RATE) as live:
        console.print("\n[bold green]🚀 仪表盘启动成功！[/bold green]")
        console.print("[cyan]按 Ctrl+C 退出仪表盘[/cyan]\n")

        try:
            while app.is_running:
                update_dashboard()
                await asyncio.sleep(REFRESH_RATE)

        except KeyboardInterrupt:
            console.print("\n[yellow]正在关闭仪表盘...[/yellow]")
            app.stop()
        except Exception as e:
            console.print(f"\n[bold red]仪表盘发生错误: {e}[/bold red]")
        finally:
            # 清理后台任务
            if not redis_task.done():
                redis_task.cancel()
            if not dispatcher_task.done():
                dispatcher_task.cancel()
            try:
                await asyncio.gather(redis_task, dispatcher_task, return_exceptions=True)
            except Exception:
                pass

            # 清理Redis连接
            if app.pubsub:
                await app.pubsub.unsubscribe()
            if app.redis_client:
                await app.redis_client.aclose()

            # 注意：不再需要清理OKX连接器


def make_data_flow_monitoring_panel() -> Panel:
    """生成数据流监控面板"""
    global strategy_data
    try:
        if not strategy_data or 'data_flow_monitoring' not in strategy_data:
            return Panel(
                "[yellow]等待数据流监控信息...[/yellow]",
                title="🔍 数据流监控",
                border_style="yellow"
            )

        monitoring_data = strategy_data['data_flow_monitoring']
        status_data = monitoring_data.get('status', {})
        metrics_data = monitoring_data.get('metrics', {})

        content_lines = []

        # 监控状态
        status = status_data.get('status', 'unknown')
        status_colors = {
            'healthy': 'green',
            'warning': 'yellow',
            'error': 'red',
            'critical': 'bright_red'
        }
        status_color = status_colors.get(status, 'white')

        content_lines.append(f"[bold]状态: [{status_color}]{status.upper()}[/{status_color}][/bold]")

        # 数据统计
        redis_count = status_data.get('redis_data_count', 0)
        content_lines.append(f"Redis数据项: {redis_count}")

        # 延迟信息
        latency = status_data.get('latency', 0)
        latency_color = 'green' if latency < 5 else 'yellow' if latency < 15 else 'red'
        content_lines.append(f"延迟: [{latency_color}]{latency:.2f}秒[/{latency_color}]")

        # 问题统计
        missing_pairs = status_data.get('missing_pairs', [])
        data_mismatches = status_data.get('data_mismatches', 0)
        format_violations = status_data.get('format_violations', 0)

        if missing_pairs:
            content_lines.append(f"[red]缺失交易对: {len(missing_pairs)}[/red]")
        if data_mismatches > 0:
            content_lines.append(f"[yellow]数据不匹配: {data_mismatches}[/yellow]")
        if format_violations > 0:
            content_lines.append(f"[yellow]格式违规: {format_violations}[/yellow]")

        # 指标摘要
        if metrics_data:
            avg_latency = metrics_data.get('average_latency', 0)
            error_rate = metrics_data.get('error_rate', 0)
            content_lines.append("")
            content_lines.append(f"平均延迟: {avg_latency:.2f}秒")
            content_lines.append(f"错误率: {error_rate:.1%}")

        # 问题列表
        issues = status_data.get('issues', [])
        if issues:
            content_lines.append("")
            content_lines.append("[bold red]当前问题:[/bold red]")
            for issue in issues[:3]:  # 只显示前3个问题
                content_lines.append(f"  • {issue}")
            if len(issues) > 3:
                content_lines.append(f"  ... 还有{len(issues)-3}个问题")

        return Panel(
            "\n".join(content_lines),
            title="🔍 数据流监控",
            border_style=status_color
        )

    except Exception as e:
        return Panel(
            f"[red]数据流监控面板错误: {str(e)}[/red]",
            title="🔍 数据流监控",
            border_style="red"
        )


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n仪表盘已关闭。")
    except Exception as e:
        print(f"启动仪表盘失败: {e}")