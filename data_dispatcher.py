"""
数据分发器 - 统一的数据分发管理

这个模块确保策略计算是唯一的数据源，Redis和日志系统作为两个并行的、独立的数据消费者。
所有显示数据都源自同一次策略计算结果，保证数据一致性。
"""
import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from dataclasses import dataclass

@dataclass
class DistributionResult:
    """分发结果"""
    redis_success: bool = False
    log_executed: bool = False
    errors: list = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []

class DataDispatcher:
    """
    数据分发器
    
    核心职责：
    1. 接收策略计算的完整结果数据包
    2. 并行分发给Redis发布器和日志系统
    3. 确保两个消费者获得完全相同的数据源
    4. 提供统一的错误处理和监控
    """
    
    def __init__(self, redis_publisher=None, logger=None):
        self.redis_publisher = redis_publisher
        self.logger = logger or logging.getLogger(__name__)
        
        # 分发统计
        self.stats = {
            'total_dispatches': 0,
            'redis_successes': 0,
            'redis_failures': 0,
            'logs_executed': 0,
            'last_dispatch_time': None
        }
    
    async def dispatch_strategy_data(self, strategy_data: Dict[str, Any]) -> DistributionResult:
        """
        分发策略数据到所有消费者
        
        Args:
            strategy_data: 来自策略管理器的完整数据包
            
        Returns:
            DistributionResult: 分发结果统计
        """
        result = DistributionResult()
        
        try:
            self.stats['total_dispatches'] += 1
            self.stats['last_dispatch_time'] = datetime.now()
            
            # 并行分发到Redis和日志系统
            redis_task = self._dispatch_to_redis(strategy_data)
            log_task = self._dispatch_to_log(strategy_data)
            
            # 等待两个任务完成
            redis_success, log_executed = await asyncio.gather(
                redis_task, 
                log_task, 
                return_exceptions=True
            )
            
            # 处理Redis分发结果
            if isinstance(redis_success, Exception):
                result.errors.append(f"Redis分发异常: {redis_success}")
                self.stats['redis_failures'] += 1
            else:
                result.redis_success = redis_success
                if redis_success:
                    self.stats['redis_successes'] += 1
                else:
                    self.stats['redis_failures'] += 1
            
            # 处理日志分发结果
            if isinstance(log_executed, Exception):
                result.errors.append(f"日志分发异常: {log_executed}")
            else:
                result.log_executed = log_executed
                if log_executed:
                    self.stats['logs_executed'] += 1
            
        except Exception as e:
            error_msg = f"数据分发器内部错误: {e}"
            result.errors.append(error_msg)
            self.logger.error(error_msg)
        
        return result
    
    async def _dispatch_to_redis(self, strategy_data: Dict[str, Any]) -> bool:
        """
        分发数据到Redis发布器
        
        Args:
            strategy_data: 完整的策略数据包
            
        Returns:
            bool: 发布是否成功
        """
        if not self.redis_publisher:
            return False
        
        try:
            # 直接发布原始策略数据，保持数据完整性
            # 仪表盘的update_strategy_data方法期望完整的策略数据格式
            await self.redis_publisher.publish_strategy_data(strategy_data)
            return True
            
        except Exception as e:
            # 只在调试模式记录Redis发布失败
            self.logger.debug(f"Redis发布失败: {e}")
            return False
    
    async def _dispatch_to_log(self, strategy_data: Dict[str, Any]) -> bool:
        """
        分发数据到日志系统
        
        Args:
            strategy_data: 完整的策略数据包
            
        Returns:
            bool: 是否执行了日志记录
        """
        try:
            heartbeat_data = strategy_data.get("heartbeat", {})
            
            # 检查是否需要记录心跳日志
            if heartbeat_data.get("should_log", False):
                log_message = heartbeat_data.get("message", "心跳数据缺失")
                
                # 根据是否有错误选择日志级别
                if heartbeat_data.get("error", False):
                    self.logger.error(log_message)
                else:
                    self.logger.info(log_message)
                
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"日志分发失败: {e}")
            return False
    
    def extract_log_data(self, strategy_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        从策略数据中提取适合日志的数据
        
        Args:
            strategy_data: 完整的策略数据包
            
        Returns:
            Dict: 格式化的日志数据
        """
        try:
            heartbeat = strategy_data.get("heartbeat", {})
            
            return {
                "timestamp": strategy_data.get("timestamp"),
                "loop_count": strategy_data.get("loop_count", 0),
                "uptime_minutes": strategy_data.get("uptime_minutes", 0),
                "active_pairs": heartbeat.get("active_pairs", 0),
                "active_positions": heartbeat.get("active_positions", 0),
                "should_log": heartbeat.get("should_log", False),
                "message": heartbeat.get("message", ""),
                "is_error": heartbeat.get("error", False)
            }
            
        except Exception as e:
            self.logger.error(f"提取日志数据失败: {e}")
            return {
                "timestamp": datetime.now().isoformat(),
                "message": f"日志数据提取失败: {e}",
                "should_log": True,
                "is_error": True
            }
    
    def extract_redis_data(self, strategy_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        从策略数据中提取适合Redis的数据
        
        Args:
            strategy_data: 完整的策略数据包
            
        Returns:
            Dict: 格式化的Redis数据
        """
        try:
            return {
                "timestamp": strategy_data.get("timestamp"),
                "loop_count": strategy_data.get("loop_count", 0),
                "pairs_data": strategy_data.get("pairs", {}),
                "manager_status": strategy_data.get("manager", {}),
                "system_health": {
                    "is_running": strategy_data.get("is_running", False),
                    "uptime_seconds": strategy_data.get("uptime_seconds", 0),
                    "uptime_minutes": strategy_data.get("uptime_minutes", 0)
                }
            }
            
        except Exception as e:
            self.logger.error(f"提取Redis数据失败: {e}")
            return {
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
    
    def get_dispatch_stats(self) -> Dict[str, Any]:
        """获取分发统计信息"""
        total = self.stats['total_dispatches']
        
        return {
            'total_dispatches': total,
            'redis_success_rate': (self.stats['redis_successes'] / total) if total > 0 else 0,
            'redis_failure_rate': (self.stats['redis_failures'] / total) if total > 0 else 0,
            'log_execution_rate': (self.stats['logs_executed'] / total) if total > 0 else 0,
            'last_dispatch_time': self.stats['last_dispatch_time'].isoformat() if self.stats['last_dispatch_time'] else None,
            'raw_stats': self.stats
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_dispatches': 0,
            'redis_successes': 0,
            'redis_failures': 0,
            'logs_executed': 0,
            'last_dispatch_time': None
        }
        
        self.logger.info("数据分发器统计信息已重置")
    
    def __repr__(self):
        return (
            f"DataDispatcher("
            f"redis_enabled={self.redis_publisher is not None}, "
            f"total_dispatches={self.stats['total_dispatches']}"
            f")"
        )