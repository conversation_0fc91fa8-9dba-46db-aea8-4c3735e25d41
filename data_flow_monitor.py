"""
数据流监控和验证模块
确保策略引擎->Redis->仪表盘数据链路的完整性和一致性
"""
import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from enum import Enum
import redis.asyncio as redis
from data_standards import DATA_VALIDATOR, DATA_CONVERTER, StandardPositionData
from log_utils import get_structured_logger


class DataFlowStatus(Enum):
    """数据流状态"""
    HEALTHY = "healthy"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class DataFlowMetrics:
    """数据流指标"""
    timestamp: datetime = field(default_factory=datetime.now)
    
    # 数据完整性指标
    strategy_data_count: int = 0
    redis_data_count: int = 0
    missing_pairs: Set[str] = field(default_factory=set)
    
    # 数据一致性指标
    data_mismatches: List[Dict[str, Any]] = field(default_factory=list)
    format_violations: List[Dict[str, Any]] = field(default_factory=list)
    
    # 性能指标
    strategy_to_redis_latency: float = 0.0
    redis_to_dashboard_latency: float = 0.0
    
    # 健康状态
    overall_status: DataFlowStatus = DataFlowStatus.HEALTHY
    issues: List[str] = field(default_factory=list)


class DataFlowMonitor:
    """数据流监控器"""
    
    def __init__(self, redis_host: str = "localhost", redis_port: int = 6379):
        self.logger = get_structured_logger(__name__)
        self.redis_host = redis_host
        self.redis_port = redis_port
        self.redis_client = None
        
        # 监控配置
        self.monitoring_interval = 30  # 30秒检查一次
        self.data_timeout_threshold = 60  # 60秒无数据更新视为异常
        self.latency_warning_threshold = 5.0  # 5秒延迟警告
        self.latency_critical_threshold = 15.0  # 15秒延迟严重
        
        # 历史数据
        self.metrics_history: List[DataFlowMetrics] = []
        self.max_history_size = 100
        
        # 监控状态
        self.is_monitoring = False
        self.last_strategy_data: Dict[str, Any] = {}
        self.last_redis_data: Dict[str, Any] = {}
        
    async def start_monitoring(self):
        """开始监控"""
        try:
            self.redis_client = redis.Redis(
                host=self.redis_host,
                port=self.redis_port,
                decode_responses=True
            )
            await self.redis_client.ping()
            
            self.is_monitoring = True
            self.logger.info("🔍 数据流监控器启动成功")
            
            # 启动监控循环
            asyncio.create_task(self._monitoring_loop())
            
        except Exception as e:
            self.logger.error(f"启动数据流监控器失败: {e}")
            raise
    
    async def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.redis_client:
            await self.redis_client.close()
        self.logger.info("🔍 数据流监控器已停止")
    
    async def _monitoring_loop(self):
        """监控主循环"""
        while self.is_monitoring:
            try:
                metrics = await self._collect_metrics()
                self._analyze_metrics(metrics)
                self._store_metrics(metrics)
                self._report_status(metrics)
                
                await asyncio.sleep(self.monitoring_interval)
                
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                await asyncio.sleep(self.monitoring_interval)
    
    async def _collect_metrics(self) -> DataFlowMetrics:
        """收集数据流指标"""
        metrics = DataFlowMetrics()
        
        try:
            # 获取Redis中的策略数据
            redis_data = await self._get_redis_strategy_data()
            metrics.redis_data_count = len(redis_data.get('pairs', {}))
            
            # 检查数据完整性
            await self._check_data_completeness(metrics, redis_data)
            
            # 检查数据一致性
            await self._check_data_consistency(metrics, redis_data)
            
            # 检查数据格式
            await self._check_data_format(metrics, redis_data)
            
            # 计算延迟
            await self._calculate_latencies(metrics, redis_data)
            
        except Exception as e:
            metrics.issues.append(f"指标收集失败: {e}")
            metrics.overall_status = DataFlowStatus.ERROR
            
        return metrics
    
    async def _get_redis_strategy_data(self) -> Dict[str, Any]:
        """从Redis获取策略数据"""
        try:
            data = await self.redis_client.get("strategy_update")
            if data:
                import json
                return json.loads(data)
            return {}
        except Exception as e:
            self.logger.error(f"获取Redis策略数据失败: {e}")
            return {}
    
    async def _check_data_completeness(self, metrics: DataFlowMetrics, redis_data: Dict[str, Any]):
        """检查数据完整性"""
        try:
            pairs_data = redis_data.get('pairs', {})
            
            # 检查必要的数据字段
            required_fields = ['prices', 'basis', 'indicators', 'position']
            
            for pair_name, pair_data in pairs_data.items():
                for field in required_fields:
                    if field not in pair_data:
                        metrics.missing_pairs.add(pair_name)
                        metrics.issues.append(f"{pair_name} 缺少字段: {field}")
            
            # 检查系统数据
            if 'timestamp' not in redis_data:
                metrics.issues.append("缺少系统时间戳")
            
            if 'manager' not in redis_data:
                metrics.issues.append("缺少管理器状态数据")
                
        except Exception as e:
            metrics.issues.append(f"完整性检查失败: {e}")
    
    async def _check_data_consistency(self, metrics: DataFlowMetrics, redis_data: Dict[str, Any]):
        """检查数据一致性"""
        try:
            pairs_data = redis_data.get('pairs', {})
            
            for pair_name, pair_data in pairs_data.items():
                # 检查价格数据一致性
                prices = pair_data.get('prices', {})
                basis_data = pair_data.get('basis', {})
                
                if prices.get('spot_price') and prices.get('futures_price'):
                    calculated_basis = prices['spot_price'] - prices['futures_price']
                    reported_basis = basis_data.get('current_basis', 0)
                    
                    if abs(calculated_basis - reported_basis) > 0.001:  # 允许小误差
                        metrics.data_mismatches.append({
                            'pair': pair_name,
                            'type': 'basis_mismatch',
                            'calculated': calculated_basis,
                            'reported': reported_basis,
                            'difference': abs(calculated_basis - reported_basis)
                        })
                
                # 检查仓位数据一致性
                position = pair_data.get('position', {})
                if position.get('active') and position.get('manager_data'):
                    manager_data = position['manager_data']
                    if position.get('entry_price') != manager_data.get('entry_spot_price'):
                        metrics.data_mismatches.append({
                            'pair': pair_name,
                            'type': 'position_price_mismatch',
                            'pair_status': position.get('entry_price'),
                            'manager': manager_data.get('entry_spot_price')
                        })
                        
        except Exception as e:
            metrics.issues.append(f"一致性检查失败: {e}")
    
    async def _check_data_format(self, metrics: DataFlowMetrics, redis_data: Dict[str, Any]):
        """检查数据格式"""
        try:
            pairs_data = redis_data.get('pairs', {})
            
            for pair_name, pair_data in pairs_data.items():
                # 验证数据格式
                validation_result = DATA_VALIDATOR.validate_market_data(pair_data)
                if not validation_result['valid']:
                    metrics.format_violations.append({
                        'pair': pair_name,
                        'violations': validation_result['errors']
                    })
                    
        except Exception as e:
            metrics.issues.append(f"格式检查失败: {e}")
    
    async def _calculate_latencies(self, metrics: DataFlowMetrics, redis_data: Dict[str, Any]):
        """计算延迟"""
        try:
            current_time = datetime.now()
            
            # 计算策略到Redis的延迟
            if 'timestamp' in redis_data:
                redis_timestamp = DATA_CONVERTER.parse_timestamp(redis_data['timestamp'])
                if redis_timestamp:
                    metrics.strategy_to_redis_latency = (current_time - redis_timestamp).total_seconds()
            
            # 检查延迟阈值
            if metrics.strategy_to_redis_latency > self.latency_critical_threshold:
                metrics.overall_status = DataFlowStatus.CRITICAL
                metrics.issues.append(f"严重延迟: {metrics.strategy_to_redis_latency:.2f}秒")
            elif metrics.strategy_to_redis_latency > self.latency_warning_threshold:
                if metrics.overall_status == DataFlowStatus.HEALTHY:
                    metrics.overall_status = DataFlowStatus.WARNING
                metrics.issues.append(f"延迟警告: {metrics.strategy_to_redis_latency:.2f}秒")
                
        except Exception as e:
            metrics.issues.append(f"延迟计算失败: {e}")
    
    def _analyze_metrics(self, metrics: DataFlowMetrics):
        """分析指标并确定整体状态"""
        if metrics.overall_status == DataFlowStatus.HEALTHY:
            if metrics.missing_pairs or metrics.data_mismatches or metrics.format_violations:
                if len(metrics.missing_pairs) > 2 or len(metrics.data_mismatches) > 5:
                    metrics.overall_status = DataFlowStatus.ERROR
                else:
                    metrics.overall_status = DataFlowStatus.WARNING
    
    def _store_metrics(self, metrics: DataFlowMetrics):
        """存储指标历史"""
        self.metrics_history.append(metrics)
        if len(self.metrics_history) > self.max_history_size:
            self.metrics_history.pop(0)
    
    def _report_status(self, metrics: DataFlowMetrics):
        """报告监控状态"""
        if metrics.overall_status == DataFlowStatus.HEALTHY:
            if len(self.metrics_history) % 10 == 0:  # 每10次报告一次正常状态
                self.logger.info(f"✅ 数据流健康: {metrics.redis_data_count}个交易对, 延迟{metrics.strategy_to_redis_latency:.2f}秒")
        else:
            status_emoji = {
                DataFlowStatus.WARNING: "⚠️",
                DataFlowStatus.ERROR: "❌", 
                DataFlowStatus.CRITICAL: "🚨"
            }
            
            self.logger.warning(
                f"{status_emoji[metrics.overall_status]} 数据流状态: {metrics.overall_status.value}\n"
                f"问题: {'; '.join(metrics.issues)}\n"
                f"缺失交易对: {metrics.missing_pairs}\n"
                f"数据不匹配: {len(metrics.data_mismatches)}个\n"
                f"格式违规: {len(metrics.format_violations)}个"
            )
    
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前监控状态"""
        if not self.metrics_history:
            return {"status": "no_data", "message": "暂无监控数据"}
        
        latest_metrics = self.metrics_history[-1]
        return {
            "status": latest_metrics.overall_status.value,
            "timestamp": latest_metrics.timestamp.isoformat(),
            "redis_data_count": latest_metrics.redis_data_count,
            "missing_pairs": list(latest_metrics.missing_pairs),
            "data_mismatches": len(latest_metrics.data_mismatches),
            "format_violations": len(latest_metrics.format_violations),
            "latency": latest_metrics.strategy_to_redis_latency,
            "issues": latest_metrics.issues
        }
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        if not self.metrics_history:
            return {}
        
        recent_metrics = self.metrics_history[-10:]  # 最近10次
        
        avg_latency = sum(m.strategy_to_redis_latency for m in recent_metrics) / len(recent_metrics)
        error_rate = sum(1 for m in recent_metrics if m.overall_status in [DataFlowStatus.ERROR, DataFlowStatus.CRITICAL]) / len(recent_metrics)
        
        return {
            "monitoring_duration": len(self.metrics_history) * self.monitoring_interval,
            "average_latency": avg_latency,
            "error_rate": error_rate,
            "total_issues": sum(len(m.issues) for m in recent_metrics),
            "health_trend": [m.overall_status.value for m in recent_metrics]
        }
