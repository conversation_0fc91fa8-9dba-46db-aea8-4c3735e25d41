#!/usr/bin/env python3
"""
端到端技术指标数据传输链路验证脚本

验证策略引擎→多交易对管理器→Redis发布→仪表盘接收的完整数据传输链路
"""

import asyncio
import json
import redis.asyncio as redis
from datetime import datetime, timedelta
from typing import Dict, Any, List
import logging
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("end_to_end_validation")

class EndToEndValidator:
    """端到端数据传输链路验证器"""
    
    def __init__(self):
        self.redis_client = None
        self.validation_results = {
            'total_messages': 0,
            'messages_with_indicators': 0,
            'pairs_analyzed': {},
            'transmission_timeline': [],
            'performance_metrics': {},
            'issues_found': []
        }
        
    async def connect_redis(self):
        """连接Redis"""
        try:
            self.redis_client = redis.Redis(
                host='localhost',
                port=6379,
                decode_responses=True
            )
            await self.redis_client.ping()
            logger.info("✅ Redis连接成功")
            return True
        except Exception as e:
            logger.error(f"❌ Redis连接失败: {e}")
            return False
    
    async def run_validation(self, duration_minutes: int = 3):
        """运行端到端验证"""
        if not self.redis_client:
            logger.error("Redis未连接")
            return False
        
        logger.info(f"🚀 开始端到端验证，持续 {duration_minutes} 分钟...")
        
        # 订阅策略数据频道
        pubsub = self.redis_client.pubsub()
        await pubsub.subscribe('trading_bot:strategy')
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        try:
            async for message in pubsub.listen():
                if message['type'] == 'message':
                    await self._process_validation_message(message)
                    
                    # 检查是否超时
                    if datetime.now() >= end_time:
                        break
                        
        except KeyboardInterrupt:
            logger.info("用户中断验证")
        finally:
            await pubsub.unsubscribe('trading_bot:strategy')
            await pubsub.close()
            
        # 生成验证报告
        await self._generate_validation_report()
        return True
    
    async def _process_validation_message(self, message: Dict):
        """处理验证消息"""
        try:
            data = json.loads(message['data'])
            timestamp = data.get('timestamp', 'N/A')
            event_type = data.get('event_type', 'unknown')
            
            self.validation_results['total_messages'] += 1
            
            if event_type == 'strategy_update':
                payload = data.get('payload', {})
                pairs_data = payload.get('pairs', {})
                
                message_has_indicators = False
                
                # 分析每个交易对的技术指标
                for pair_name, pair_data in pairs_data.items():
                    await self._analyze_pair_indicators(pair_name, pair_data, timestamp)
                    
                    tech_indicators = pair_data.get('technical_indicators', {})
                    if tech_indicators.get('atr') is not None or tech_indicators.get('adx') is not None:
                        message_has_indicators = True
                
                if message_has_indicators:
                    self.validation_results['messages_with_indicators'] += 1
                
                # 记录传输时间线
                self.validation_results['transmission_timeline'].append({
                    'message_count': self.validation_results['total_messages'],
                    'timestamp': timestamp,
                    'has_indicators': message_has_indicators,
                    'pairs_count': len(pairs_data)
                })
                
        except Exception as e:
            self.validation_results['issues_found'].append(f"消息处理错误: {e}")
    
    async def _analyze_pair_indicators(self, pair_name: str, pair_data: Dict, timestamp: str):
        """分析交易对的技术指标"""
        if pair_name not in self.validation_results['pairs_analyzed']:
            self.validation_results['pairs_analyzed'][pair_name] = {
                'total_updates': 0,
                'atr_values': [],
                'adx_values': [],
                'first_atr_time': None,
                'first_adx_time': None,
                'indicators_ready_time': None,
                'transmission_counts': [],
                'dashboard_info_history': []
            }
        
        pair_stats = self.validation_results['pairs_analyzed'][pair_name]
        pair_stats['total_updates'] += 1
        
        tech_indicators = pair_data.get('technical_indicators', {})
        dashboard_info = tech_indicators.get('dashboard_info', {})
        
        # 记录ATR值
        atr = tech_indicators.get('atr')
        if atr is not None:
            pair_stats['atr_values'].append(atr)
            if pair_stats['first_atr_time'] is None:
                pair_stats['first_atr_time'] = timestamp
        
        # 记录ADX值
        adx = tech_indicators.get('adx')
        if adx is not None:
            pair_stats['adx_values'].append(adx)
            if pair_stats['first_adx_time'] is None:
                pair_stats['first_adx_time'] = timestamp
        
        # 记录指标就绪时间
        if (dashboard_info.get('all_ready', False) and 
            pair_stats['indicators_ready_time'] is None):
            pair_stats['indicators_ready_time'] = timestamp
        
        # 记录传输计数
        transmission_count = dashboard_info.get('transmission_count', 0)
        if transmission_count > 0:
            pair_stats['transmission_counts'].append(transmission_count)
        
        # 记录仪表盘信息历史
        pair_stats['dashboard_info_history'].append({
            'timestamp': timestamp,
            'all_ready': dashboard_info.get('all_ready', False),
            'atr_progress': dashboard_info.get('atr_progress', 0),
            'adx_progress': dashboard_info.get('adx_progress', 0),
            'status_message': dashboard_info.get('status_message', ''),
            'total_updates': dashboard_info.get('total_updates', 0)
        })
    
    async def _generate_validation_report(self):
        """生成验证报告"""
        logger.info("\n" + "="*80)
        logger.info("📋 端到端技术指标数据传输链路验证报告")
        logger.info("="*80)
        
        # 总体统计
        total_messages = self.validation_results['total_messages']
        messages_with_indicators = self.validation_results['messages_with_indicators']
        
        logger.info(f"\n📊 总体统计:")
        logger.info(f"  总消息数: {total_messages}")
        logger.info(f"  包含技术指标的消息: {messages_with_indicators}")
        logger.info(f"  技术指标覆盖率: {(messages_with_indicators/total_messages*100):.1f}%" if total_messages > 0 else "  技术指标覆盖率: 0%")
        
        # 交易对分析
        logger.info(f"\n🔍 交易对详细分析:")
        for pair_name, stats in self.validation_results['pairs_analyzed'].items():
            logger.info(f"\n  交易对: {pair_name}")
            logger.info(f"    总更新次数: {stats['total_updates']}")
            logger.info(f"    ATR数据点: {len(stats['atr_values'])}")
            logger.info(f"    ADX数据点: {len(stats['adx_values'])}")
            logger.info(f"    首次ATR时间: {stats['first_atr_time'] or '未获得'}")
            logger.info(f"    首次ADX时间: {stats['first_adx_time'] or '未获得'}")
            logger.info(f"    指标就绪时间: {stats['indicators_ready_time'] or '未就绪'}")
            
            if stats['atr_values']:
                atr_latest = stats['atr_values'][-1]
                atr_range = f"{min(stats['atr_values']):.6f} - {max(stats['atr_values']):.6f}"
                logger.info(f"    ATR最新值: {atr_latest:.6f} (范围: {atr_range})")
            
            if stats['adx_values']:
                adx_latest = stats['adx_values'][-1]
                adx_range = f"{min(stats['adx_values']):.2f} - {max(stats['adx_values']):.2f}"
                logger.info(f"    ADX最新值: {adx_latest:.2f} (范围: {adx_range})")
            
            if stats['transmission_counts']:
                latest_transmission = max(stats['transmission_counts'])
                logger.info(f"    最新传输计数: {latest_transmission}")
        
        # 性能分析
        logger.info(f"\n⚡ 性能分析:")
        timeline = self.validation_results['transmission_timeline']
        if len(timeline) > 1:
            # 计算消息间隔
            intervals = []
            for i in range(1, len(timeline)):
                try:
                    prev_time = datetime.fromisoformat(timeline[i-1]['timestamp'].replace('Z', '+00:00'))
                    curr_time = datetime.fromisoformat(timeline[i]['timestamp'].replace('Z', '+00:00'))
                    interval = (curr_time - prev_time).total_seconds()
                    intervals.append(interval)
                except:
                    continue
            
            if intervals:
                avg_interval = sum(intervals) / len(intervals)
                min_interval = min(intervals)
                max_interval = max(intervals)
                logger.info(f"  平均消息间隔: {avg_interval:.2f}秒")
                logger.info(f"  最小消息间隔: {min_interval:.2f}秒")
                logger.info(f"  最大消息间隔: {max_interval:.2f}秒")
        
        # 问题报告
        if self.validation_results['issues_found']:
            logger.info(f"\n⚠️  发现的问题:")
            for issue in self.validation_results['issues_found']:
                logger.info(f"  - {issue}")
        else:
            logger.info(f"\n✅ 未发现明显问题")
        
        # 建议
        logger.info(f"\n💡 建议:")
        if messages_with_indicators == 0:
            logger.info("  - ❌ 未接收到任何技术指标数据，请检查策略引擎是否正常运行")
        elif messages_with_indicators < total_messages * 0.8:
            logger.info("  - ⚠️  技术指标数据覆盖率较低，可能存在计算延迟")
        else:
            logger.info("  - ✅ 技术指标数据传输正常")
        
        # 检查是否有交易对的指标已就绪
        ready_pairs = [name for name, stats in self.validation_results['pairs_analyzed'].items() 
                      if stats['indicators_ready_time'] is not None]
        
        if ready_pairs:
            logger.info(f"  - ✅ 以下交易对的技术指标已就绪: {', '.join(ready_pairs)}")
        else:
            logger.info("  - ⚠️  暂无交易对的技术指标完全就绪，可能需要更多时间初始化")
        
        logger.info("\n" + "="*80)

async def main():
    """主函数"""
    validator = EndToEndValidator()
    
    # 连接Redis
    if not await validator.connect_redis():
        return
    
    # 运行验证
    try:
        await validator.run_validation(duration_minutes=3)  # 验证3分钟
    except Exception as e:
        logger.error(f"验证过程中发生错误: {e}")
    finally:
        if validator.redis_client:
            await validator.redis_client.close()

if __name__ == "__main__":
    asyncio.run(main())
