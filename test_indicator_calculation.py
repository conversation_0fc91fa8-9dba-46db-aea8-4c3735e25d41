#!/usr/bin/env python3
"""
技术指标计算逻辑测试脚本

用于验证ATR和ADX指标的增量计算是否正确工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from incremental_indicators import IndicatorManager
import random
import time

def test_indicator_calculation():
    """测试技术指标计算"""
    print("🧪 开始测试技术指标计算逻辑...")
    
    # 创建指标管理器
    manager = IndicatorManager(atr_period=14, adx_period=14)
    
    # 模拟价格数据
    base_price = 50000.0
    prices = []
    
    print("\n📊 模拟价格数据并计算技术指标:")
    print("更新次数 | 高价     | 低价     | 收盘价   | ATR      | ADX    | +DI    | -DI    | 状态")
    print("-" * 90)
    
    for i in range(30):  # 测试30个数据点
        # 生成模拟价格数据
        volatility = 100 + random.uniform(-50, 50)
        high = base_price + random.uniform(0, volatility)
        low = base_price - random.uniform(0, volatility)
        close = base_price + random.uniform(-volatility/2, volatility/2)
        
        # 更新基础价格（模拟趋势）
        base_price += random.uniform(-20, 20)
        
        prices.append((high, low, close))
        
        # 更新指标
        result = manager.update_all(high, low, close)
        
        # 获取仪表盘就绪值
        dashboard_values = manager.get_dashboard_ready_values()
        
        # 格式化输出
        atr_str = f"{result['atr']:.4f}" if result['atr'] else "None"
        adx_str = f"{result['adx']:.2f}" if result['adx'] else "None"
        plus_di_str = f"{result['plus_di']:.2f}" if result['plus_di'] else "None"
        minus_di_str = f"{result['minus_di']:.2f}" if result['minus_di'] else "None"
        
        status = "就绪" if manager.is_all_ready() else dashboard_values['status_message']
        
        print(f"{i+1:8d} | {high:8.2f} | {low:8.2f} | {close:8.2f} | {atr_str:8s} | {adx_str:6s} | {plus_di_str:6s} | {minus_di_str:6s} | {status}")
        
        # 检查状态变化
        debug_info = result.get('debug_info', {})
        if debug_info.get('state_changed', {}).get('atr_became_ready'):
            print(f"    🎉 ATR指标在第{i+1}次更新时变为就绪！")
        if debug_info.get('state_changed', {}).get('adx_became_ready'):
            print(f"    🎉 ADX指标在第{i+1}次更新时变为就绪！")
        
        # 短暂延迟以模拟实时更新
        time.sleep(0.1)
    
    print("\n" + "="*90)
    print("📋 测试总结:")
    
    final_values = manager.get_current_values()
    dashboard_values = manager.get_dashboard_ready_values()
    
    print(f"总更新次数: {manager.update_count}")
    print(f"ATR就绪: {final_values['atr_ready']}")
    print(f"ADX就绪: {final_values['adx_ready']}")
    print(f"所有指标就绪: {manager.is_all_ready()}")
    
    if final_values['atr_ready']:
        print(f"最终ATR值: {final_values['atr']:.6f}")
    if final_values['adx_ready']:
        print(f"最终ADX值: {final_values['adx']:.2f}")
        plus_di, minus_di = manager.adx_calculator.get_directional_indicators()
        if plus_di and minus_di:
            print(f"最终+DI值: {plus_di:.2f}")
            print(f"最终-DI值: {minus_di:.2f}")
    
    print(f"ATR数据计数: {manager.atr_calculator.data_count}")
    print(f"ADX数据计数: {manager.adx_calculator.data_count}")
    print(f"状态消息: {dashboard_values['status_message']}")
    
    return manager.is_all_ready()

def test_data_structure():
    """测试数据结构的完整性"""
    print("\n🔍 测试数据结构完整性...")
    
    manager = IndicatorManager()
    
    # 添加一些数据
    for i in range(5):
        high = 50000 + i * 10
        low = 49990 + i * 10
        close = 49995 + i * 10
        result = manager.update_all(high, low, close)
    
    # 检查返回的数据结构
    current_values = manager.get_current_values()
    dashboard_values = manager.get_dashboard_ready_values()
    
    print("✅ get_current_values() 返回的键:")
    for key in sorted(current_values.keys()):
        print(f"  - {key}: {type(current_values[key])}")
    
    print("\n✅ get_dashboard_ready_values() 返回的键:")
    for key in sorted(dashboard_values.keys()):
        print(f"  - {key}: {type(dashboard_values[key])}")
    
    # 验证关键字段存在
    required_fields = ['atr', 'adx', 'plus_di', 'minus_di', 'atr_ready', 'adx_ready']
    missing_fields = [field for field in required_fields if field not in current_values]
    
    if missing_fields:
        print(f"❌ 缺少必需字段: {missing_fields}")
        return False
    else:
        print("✅ 所有必需字段都存在")
        return True

if __name__ == "__main__":
    print("🚀 技术指标计算逻辑测试")
    print("="*50)
    
    # 测试数据结构
    structure_ok = test_data_structure()
    
    if structure_ok:
        # 测试计算逻辑
        calculation_ok = test_indicator_calculation()
        
        if calculation_ok:
            print("\n✅ 所有测试通过！技术指标计算逻辑正常工作。")
        else:
            print("\n⚠️  指标未能在预期时间内就绪，可能需要更多数据点。")
    else:
        print("\n❌ 数据结构测试失败！")
    
    print("\n🔚 测试完成")
