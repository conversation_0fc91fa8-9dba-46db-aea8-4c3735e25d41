"""
异步套利机器人引擎 - 事件驱动的高性能交易系统
"""
import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Set, Tuple
import json

from okx_connector import OKXConnector
from multi_pair_strategy_manager import MultiPairStrategyManager
from position_manager import PositionManager, PositionType, OrderInfo
from okx_microstructure_adapter import OKXMicrostructureAdapter
from position_monitor import PositionMonitor
from alert_system import AlertSystem
from emergency_handler import EmergencyHandler
from data_dispatcher import DataDispatcher

from log_utils import get_structured_logger, trace_manager, KPICalculator
from config import get_enabled_trading_pairs, get_all_symbols
from redis_publisher import get_redis_publisher
# 监控系统已移除 - 性能指标通过log_utils.py的结构化日志记录


class ArbitrageBot:
    """事件驱动套利机器人 - 多交易对单边套利架构"""
    
    def __init__(self, config):
        self.config = config
        self.logger = get_structured_logger(__name__)
        
        # 核心组件
        self.connector = OKXConnector(config)
        self.strategy_manager = MultiPairStrategyManager(config)  # 替换单一策略为多交易对管理器
        self.position_manager = PositionManager(config)
        
        # 微观结构分析组件
        self.microstructure_adapter = OKXMicrostructureAdapter()
        self.strategy_manager.set_microstructure_adapters(self.microstructure_adapter)

        # 告警系统 - 对冲回滚安全网的通知组件
        self.alert_system = None  # 初始化后设置
        
        # 持仓监控系统 - 对冲回滚安全网的核心组件
        self.position_monitor = PositionMonitor(self.connector)
        
        # 应急处理系统 - 对冲回滚安全网的最后防线
        self.emergency_handler = None  # 初始化后设置
        
        # 数据分发器 - 统一的数据分发管理
        self.data_dispatcher = None  # 初始化后设置
        
        
        # ======= 多交易对实时状态管理 =======
        # 各交易对价格数据 {symbol: price}
        self.spot_prices: Dict[str, float] = {}
        self.futures_prices: Dict[str, float] = {}
        self.last_price_updates: Dict[str, datetime] = {}
        
        # 各交易对资金费率 {futures_symbol: rate}
        self.funding_rates: Dict[str, float] = {}
        self.last_funding_updates: Dict[str, datetime] = {}
        
        # 持仓状态（由WebSocket推送更新）
        self.current_positions: Dict[str, Dict[str, Any]] = {}
        self.last_position_update_time: Optional[datetime] = None
        
        # 订单状态（由WebSocket推送更新）
        self.open_orders: Dict[str, Dict[str, Any]] = {}
        self.last_order_update_time: Optional[datetime] = None
        
        # 账户状态（由WebSocket推送更新）
        self.account_balance: Dict[str, Any] = {}
        self.last_balance_update_time: Optional[datetime] = None
        
        # ======= 运行状态控制 =======
        self.is_running = False
        self.start_time: Optional[datetime] = None
        
        # 性能监控
        self.message_count = 0
        self.processing_times = []
        
        # 主循环心跳计数器 - 避免高频日志刷屏
        self.loop_counter = 0
        
        # 价格更新日志控制
        self.price_log_counter = 0
        self.price_log_interval = 50
        self.total_updates = 0  # 用于监控实时数据流
        
        # 风险控制
        self.emergency_stop = False
        self.error_count = 0
        self.max_errors = 10
        
        # 并发开仓锁定机制
        self.is_opening_position = False
        
        # 多交易对订阅列表
        spot_symbols, futures_symbols = get_all_symbols()
        self.subscribed_spot_instruments = spot_symbols
        self.subscribed_futures_instruments = futures_symbols
        all_instruments = spot_symbols + futures_symbols
        
        self.logger.info(
            f"多交易对套利机器人初始化完成 - "
            f"现货:{len(spot_symbols)}个, 期货:{len(futures_symbols)}个"
        )

        # Redis发布器（仪表盘通信）
        self.redis_publisher = None

        # 监控系统已移除 - 使用log_utils.py的结构化日志记录
        self.logger.info("📊 使用结构化日志系统进行性能监控")
    
    async def start(self):
        """启动事件驱动机器人"""
        try:
            self.logger.info("🚀 启动事件驱动套利机器人...")
            
            # 第一步：初始化Redis发布器
            self.logger.info("📡 初始化Redis发布器...")
            try:
                self.redis_publisher = await get_redis_publisher()
                # 移除系统状态发布 - 系统状态现在由策略计算引擎统一管理
                self.logger.info("✅ Redis发布器初始化成功")
            except Exception as e:
                self.logger.warning(f"⚠️ Redis发布器初始化失败，仪表盘将不可用: {e}")
                self.redis_publisher = None
            
            # 初始化告警系统
            self.logger.info("🚨 初始化告警系统...")
            self.alert_system = AlertSystem(self.redis_publisher)
            
            # 初始化应急处理系统
            self.logger.info("🆘 初始化应急处理系统...")
            self.emergency_handler = EmergencyHandler(
                self.connector, 
                self.position_manager, 
                self.alert_system
            )
            
            # 初始化数据分发器
            self.logger.info("📡 初始化数据分发器...")
            self.data_dispatcher = DataDispatcher(
                redis_publisher=self.redis_publisher,
                logger=self.logger
            )
            
            # 将相关系统传递给各组件
            self.position_monitor.alert_system = self.alert_system
            self.logger.info("✅ 完整系统架构（策略+分发+安全网）初始化成功")
            
            # 第二步：尝试恢复上次的状态
            self.logger.info("🔄 尝试恢复上次的仓位状态...")
            recovery_success = self.position_manager.load_state_from_json()
            
            if recovery_success:
                self.logger.info("✅ 仓位状态恢复成功")
                if self.position_manager.in_position:
                    position_info = self.position_manager.get_position_info()
                    self.logger.warning(
                        f"⚠️  检测到活跃仓位: {position_info['position_id']}, "
                        f"类型: {position_info['position_type']}, "
                        f"持仓时长: {position_info['holding_duration_seconds']/3600:.1f}小时"
                    )
                    # 发布仓位恢复事件
                    if self.redis_publisher:
                        await self.redis_publisher.publish_position_event(
                            "position_recovered", 
                            position_info['position_id'],
                            position_info
                        )
            else:
                self.logger.info("🆕 使用全新状态启动")
            
            # 启动双通道连接器
            await self.connector.start()
            
            # 初始化交易对规格信息
            self.logger.info("📋 初始化交易对规格信息...")
            specs_initialized = await self.connector.initialize_instrument_specs()
            if not specs_initialized:
                raise RuntimeError("交易对规格初始化失败，无法继续运行")

            # 数据预热 - 获取历史数据填充战略层
            self.logger.info("🔥 开始策略数据预热...")
            await self._perform_data_preheating()

            # 注册所有事件回调函数
            self._register_event_callbacks()

            # 订阅所有需要的数据流
            await self._subscribe_all_channels()
            
            # 设置运行状态
            self.is_running = True
            self.start_time = datetime.now()
            
            # 移除系统状态发布 - 系统状态现在由策略计算引擎统一管理
            # 启动完成，系统状态将通过DataDispatcher定期发布
            
            # 设置手动平仓检测
            self.position_manager.set_okx_connector(self.connector)
            
            # 启动持仓监控系统
            self.logger.info("🔍 启动实时持仓监控系统...")
            trading_pairs = get_enabled_trading_pairs()
            await self.position_monitor.start_monitoring(trading_pairs)
            
            # 启动应急监控系统
            self.logger.info("🆘 启动应急处理监控...")
            await self.emergency_handler.start_monitoring()
            
            self.logger.info("✅ 事件驱动机器人启动成功，等待实时数据推送")
            
            # 启动策略计算主循环任务（并行运行）
            strategy_loop_task = asyncio.create_task(
                self._strategy_calculation_loop(), 
                name="strategy_calculation_loop"
            )
            
            # 保持运行（事件驱动 + 策略主循环）
            try:
                while self.is_running:
                    await asyncio.sleep(1)  # 最小休眠，主要工作由事件回调处理
                    
                    # 定期检测手动平仓
                    manual_close_detected = await self.position_manager.check_manual_close_detection()
                    if manual_close_detected:
                        self.logger.info("🔄 检测到手动平仓，系统状态已自动同步")
                    
                    # 定期健康检查（非轮询业务逻辑）
                    if self.start_time and (datetime.now() - self.start_time).total_seconds() % 300 == 0:
                        await self._perform_health_check()
                        
                    # 检查策略循环任务是否异常退出
                    if strategy_loop_task.done():
                        exception = strategy_loop_task.exception()
                        if exception:
                            self.logger.error(f"策略计算循环异常退出: {exception}")
                            # 重启策略循环任务
                            strategy_loop_task = asyncio.create_task(
                                self._strategy_calculation_loop(), 
                                name="strategy_calculation_loop_restart"
                            )
                        
            except KeyboardInterrupt:
                self.logger.info("收到停止信号")
                
            finally:
                # 停止策略循环任务
                if not strategy_loop_task.done():
                    strategy_loop_task.cancel()
                    try:
                        await strategy_loop_task
                    except asyncio.CancelledError:
                        pass
            
        except Exception as e:
            self.logger.error(f"机器人启动失败: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止事件驱动机器人"""
        try:
            self.logger.info("🛑 正在停止事件驱动机器人...")
            
            self.is_running = False
            
            # 如果有开仓位置，尝试平仓
            if self.position_manager.in_position:
                self.logger.warning("检测到未平仓位，尝试紧急平仓")
                await self._emergency_close_position("系统停止")
            
            # 停止持仓监控系统
            self.logger.info("⏹️ 停止实时持仓监控系统...")
            await self.position_monitor.stop_monitoring()
            
            # 停止应急处理系统
            self.logger.info("🆘 停止应急处理监控...")
            if self.emergency_handler:
                await self.emergency_handler.stop_monitoring()
            
            # 停止双通道连接器
            await self.connector.stop()
            
            # 输出最终统计
            self._log_final_statistics()
            
            self.logger.info("✅ 事件驱动机器人已停止")
            
        except Exception as e:
            self.logger.error(f"停止机器人时发生错误: {e}")
    
    def _register_event_callbacks(self):
        """注册所有事件回调函数"""
        try:
            self.logger.info("📋 注册事件回调函数...")
            
            # 注册公共频道回调
            self.connector.register_event_callback('ticker', self.handle_ticker_update)
            self.connector.register_event_callback('books', self.handle_books_update)
            self.connector.register_event_callback('trades', self.handle_trades_update)  # 微观结构：逐笔成交
            
            # 注册资金费率回调（关键：实时净利润计算）
            self.connector.register_event_callback('funding-rate', self.handle_funding_rate_update)
            
            # 注册私有频道回调
            self.connector.register_event_callback('account', self.handle_account_update)
            self.connector.register_event_callback('orders', self.handle_order_update)
            self.connector.register_event_callback('positions', self.handle_position_update)
            
            self.logger.info("✅ 事件回调函数注册完成")
            
        except Exception as e:
            self.logger.error(f"注册事件回调失败: {e}")
            raise
    
    async def _subscribe_all_channels(self):
        """订阅所有需要的数据流 - 多交易对版本"""
        try:
            self.logger.info("📡 订阅多交易对数据流...")
            
            # 订阅所有交易对的价格数据
            all_instruments = self.subscribed_spot_instruments + self.subscribed_futures_instruments
            await self.connector.subscribe_tickers(all_instruments)
            await self.connector.subscribe_order_books(all_instruments)
            
            # 订阅所有现货的微观结构数据（高频分析）
            await self.connector.subscribe_order_books_5(self.subscribed_spot_instruments)
            await self.connector.subscribe_trades(self.subscribed_spot_instruments)
            
            # 订阅所有期货的资金费率（关键：实时净利润计算）
            await self.connector.subscribe_funding_rates(self.subscribed_futures_instruments)
            
            # 订阅私有频道数据
            await self.connector.subscribe_account_updates()
            await self.connector.subscribe_order_updates("SWAP")
            await self.connector.subscribe_position_updates("SWAP")
            
            self.logger.info(
                f"✅ 多交易对数据流订阅完成 - "
                f"总计{len(all_instruments)}个标的"
            )
            
        except Exception as e:
            self.logger.error(f"订阅数据流失败: {e}")
            raise
    
    # ======= WebSocket 事件回调处理函数 =======

    def _validate_ticker_data(self, data: Dict[str, Any]) -> bool:
        """
        验证 Ticker 数据的完整性

        Args:
            data: WebSocket 接收到的原始数据

        Returns:
            bool: 数据是否完整且有效
        """
        try:
            # 检查基本结构
            if not isinstance(data, dict):
                self.logger.warning("Ticker数据不是字典格式")
                return False

            # 检查必要的顶级字段
            if "arg" not in data or "data" not in data:
                self.logger.warning("Ticker数据缺少必要字段 'arg' 或 'data'")
                return False

            # 检查 arg 字段
            arg = data["arg"]
            if not isinstance(arg, dict) or "instId" not in arg:
                self.logger.warning("Ticker数据 'arg' 字段格式错误或缺少 'instId'")
                return False

            # 检查 data 字段
            data_list = data["data"]
            if not isinstance(data_list, list) or len(data_list) == 0:
                self.logger.warning("Ticker数据 'data' 字段为空或格式错误")
                return False

            # 检查第一个数据项
            ticker_data = data_list[0]
            if not isinstance(ticker_data, dict):
                self.logger.warning("Ticker数据项不是字典格式")
                return False

            # 检查关键价格字段
            if "last" not in ticker_data:
                self.logger.warning(f"Ticker数据缺少 'last' 字段: {arg.get('instId', 'unknown')}")
                return False

            # 验证价格数据格式
            try:
                last_price = float(ticker_data["last"])
                if last_price <= 0:
                    self.logger.warning(f"无效的价格数据: {last_price}")
                    return False
            except (ValueError, TypeError):
                self.logger.warning(f"价格数据无法转换为浮点数: {ticker_data.get('last')}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"验证Ticker数据时发生异常: {e}")
            return False

    async def handle_ticker_update(self, data: Dict[str, Any]):
        """处理价格更新事件 - 多交易对版本 (增加数据时效性检查)"""
        try:
            start_time = time.time()

            # 数据完整性验证
            if not self._validate_ticker_data(data):
                return  # 跳过不完整的数据，等待下一次更新

            # 解析价格数据
            inst_id = data["arg"]["instId"]
            ticker_data = data["data"][0]
            last_price = float(ticker_data["last"])
            current_time = datetime.now()

            # 🔥 数据时效性检查配置
            MAX_PRICE_DELAY_SECONDS = 2.0  # 最大可接受的价格延迟

            # 更新价格状态
            price_updated = False
            
            if inst_id in self.subscribed_spot_instruments:
                old_price = self.spot_prices.get(inst_id)
                self.spot_prices[inst_id] = last_price
                self.last_price_updates[inst_id] = current_time
                price_updated = True

                # 查找对应的期货价格，更新策略
                for pair_status in self.strategy_manager.pair_strategies.values():
                    if pair_status.spot_symbol == inst_id:
                        futures_symbol = pair_status.futures_symbol
                        # 🔥 数据快照一致性：在同一时刻获取期货价格
                        futures_price = self.futures_prices.get(futures_symbol)
                        futures_update_time = self.last_price_updates.get(futures_symbol)

                        if futures_price and futures_update_time:
                            # 🔥 数据时效性检查
                            futures_delay = (current_time - futures_update_time).total_seconds()
                            if futures_delay > MAX_PRICE_DELAY_SECONDS:
                                self.logger.warning(
                                    f"策略更新跳过: {inst_id}/{futures_symbol} 期货数据陈旧 "
                                    f"(延迟: {futures_delay:.2f}s)"
                                )
                                break  # 跳过这个交易对的更新

                            # 创建价格快照，确保所有计算使用相同的数据
                            price_snapshot = {
                                'spot_price': last_price,
                                'futures_price': futures_price,
                                'timestamp': current_time,
                                'spot_symbol': inst_id,
                                'futures_symbol': futures_symbol
                            }

                            # 🔥 增强错误处理：策略更新异常捕获
                            try:
                                update_success = self.strategy_manager.update_pair_prices(
                                    inst_id, futures_symbol,
                                    price_snapshot['spot_price'],
                                    price_snapshot['futures_price']
                                )

                                # 使用快照数据进行基差计算和日志记录（降低频率）
                                if update_success and self.total_updates % 200 == 0:
                                    basis = (price_snapshot['futures_price'] - price_snapshot['spot_price']) / price_snapshot['spot_price'] if price_snapshot['spot_price'] > 0 else 0.0
                                    self.logger.debug(f"📊 现货价格更新触发: {inst_id}=${price_snapshot['spot_price']}, "
                                                    f"期货=${price_snapshot['futures_price']}, 基差={basis:.6f} "
                                                    f"(更新#{self.total_updates})")

                            except Exception as strategy_e:
                                self.logger.error(f"策略价格更新失败 {inst_id}: {strategy_e}")
                                # 继续执行，不中断整个流程

                            # 🔥 增强错误处理：状态记录异常捕获
                            try:
                                await self._log_market_state_update_realtime_with_snapshot(price_snapshot)
                            except Exception as log_e:
                                self.logger.error(f"记录市场状态失败 {inst_id}: {log_e}")
                                # 继续执行，不中断整个流程
                        break
                        
            elif inst_id in self.subscribed_futures_instruments:
                old_price = self.futures_prices.get(inst_id)
                self.futures_prices[inst_id] = last_price
                self.last_price_updates[inst_id] = current_time
                price_updated = True

                # 查找对应的现货价格，更新策略
                for pair_status in self.strategy_manager.pair_strategies.values():
                    if pair_status.futures_symbol == inst_id:
                        spot_symbol = pair_status.spot_symbol
                        # 🔥 数据快照一致性：在同一时刻获取现货价格
                        spot_price = self.spot_prices.get(spot_symbol)
                        spot_update_time = self.last_price_updates.get(spot_symbol)

                        if spot_price and spot_update_time:
                            # 🔥 数据时效性检查
                            spot_delay = (current_time - spot_update_time).total_seconds()
                            if spot_delay > MAX_PRICE_DELAY_SECONDS:
                                self.logger.warning(
                                    f"策略更新跳过: {spot_symbol}/{inst_id} 现货数据陈旧 "
                                    f"(延迟: {spot_delay:.2f}s)"
                                )
                                break  # 跳过这个交易对的更新

                            # 创建价格快照，确保所有计算使用相同的数据
                            price_snapshot = {
                                'spot_price': spot_price,
                                'futures_price': last_price,
                                'timestamp': current_time,
                                'spot_symbol': spot_symbol,
                                'futures_symbol': inst_id
                            }

                            # 🔥 增强错误处理：策略更新异常捕获
                            try:
                                update_success = self.strategy_manager.update_pair_prices(
                                    spot_symbol, inst_id,
                                    price_snapshot['spot_price'],
                                    price_snapshot['futures_price']
                                )

                                # 使用快照数据进行基差计算和日志记录（降低频率）
                                if update_success and self.total_updates % 200 == 0:
                                    basis = (price_snapshot['futures_price'] - price_snapshot['spot_price']) / price_snapshot['spot_price'] if price_snapshot['spot_price'] > 0 else 0.0
                                    self.logger.debug(f"📈 期货价格更新触发: {inst_id}=${price_snapshot['futures_price']}, "
                                                    f"现货=${price_snapshot['spot_price']}, 基差={basis:.6f} "
                                                    f"(更新#{self.total_updates})")

                            except Exception as strategy_e:
                                self.logger.error(f"策略价格更新失败 {inst_id}: {strategy_e}")
                                # 继续执行，不中断整个流程

                            # 🔥 增强错误处理：状态记录异常捕获
                            try:
                                await self._log_market_state_update_realtime_with_snapshot(price_snapshot)
                            except Exception as log_e:
                                self.logger.error(f"记录市场状态失败 {inst_id}: {log_e}")
                                # 继续执行，不中断整个流程
                        break
            
            if price_updated:
                self.last_price_update_time = datetime.now()
                self.message_count += 1
                self.price_log_counter += 1
                self.total_updates += 1  # 递增总更新计数器
                
                # 定期记录详细价格信息 (降级为DEBUG)
                if self.price_log_counter % self.price_log_interval == 0:
                    instrument_type = "spot" if inst_id in self.subscribed_spot_instruments else "futures"
                    self.logger.debug_structured(
                        f"{instrument_type}价格更新",
                        event_type="price_update",
                        metrics={
                            "price": last_price,
                            "price_change_pct": ((last_price-old_price)/old_price*100) if old_price else 0
                        },
                        details={"instrument": instrument_type, "symbol": inst_id}
                    )
                
                # 多交易对策略检查 - 当有足够价格数据时触发
                if self.price_log_counter % 100 == 0:  # 每100次价格更新记录一次
                    self.logger.debug(f"🔄 触发策略检查 (#{self.price_log_counter})")
                await self._check_all_strategies_async()
            
            # 记录处理时间和Prometheus指标
            processing_time = (time.time() - start_time) * 1000
            self.processing_times.append(processing_time)
            if len(self.processing_times) > 1000:
                self.processing_times.pop(0)
            
            # 记录价格更新延迟指标
            # 性能指标通过结构化日志记录
            
            # 记录WebSocket消息
            # WebSocket消息统计通过结构化日志记录
            
        except KeyError as ke:
            self.error_count += 1
            # 🔥 增强错误处理：专门处理 KeyError
            if "'last'" in str(ke):
                self.logger.warning(f"收到不完整的Ticker数据，缺少'last'字段: {data.get('arg', {}).get('instId', 'unknown')}")
            else:
                self.logger.error(f"Ticker数据字段访问错误: {ke}")

            # 记录错误统计但不中断程序运行

        except ValueError as ve:
            self.error_count += 1
            # 🔥 增强错误处理：专门处理数值转换错误
            self.logger.error(f"Ticker数据格式错误: {ve}")

        except Exception as e:
            self.error_count += 1
            # 🔥 增强错误处理：捕获所有其他异常
            self.logger.error(f"处理Ticker更新发生未预期错误: {e}")

            # 记录详细的错误上下文
            try:
                inst_id = data.get("arg", {}).get("instId", "unknown")
                self.logger.error(f"错误上下文 - 交易对: {inst_id}, 数据结构: {type(data)}")
            except:
                self.logger.error("无法获取错误上下文信息")

            # 记录错误指标
            # 错误统计通过结构化日志记录
            # WebSocket消息统计通过结构化日志记录
    
    async def handle_books_update(self, data: Dict[str, Any]):
        """处理订单簿更新事件（含微观结构分析）"""
        try:
            inst_id = data["arg"]["instId"]
            books_data = data["data"][0]
            bids = books_data.get("bids", [])
            asks = books_data.get("asks", [])
            
            if len(bids) > 0 and len(asks) > 0:
                best_bid = float(bids[0][0])
                best_ask = float(asks[0][0])
                spread = (best_ask - best_bid) / best_bid
                
                # 处理微观结构分析（仅针对现货）
                if inst_id == self.config.SPOT_ID:
                    orderbook_snapshot = self.microstructure_adapter.process_orderbook_update(data)
                    
                    if orderbook_snapshot:
                        # 获取微观结构信号用于交易决策增强
                        microstructure_signals = self.microstructure_adapter.get_microstructure_signals(inst_id)
                        
                        # 只在信号变化或异常情况下记录详细日志
                        if microstructure_signals and microstructure_signals.get('micro_signal') != 'neutral':
                            self.logger.debug_structured(
                                f"微观结构信号更新 {inst_id}",
                                event_type="microstructure_signal_update",
                                metrics={
                                    'micro_signal': microstructure_signals.get('micro_signal'),
                                    'micro_confidence': microstructure_signals.get('micro_confidence'),
                                    'obi_signal': microstructure_signals.get('obi_signal'),
                                    'flow_signal': microstructure_signals.get('flow_signal')
                                },
                                details={'symbol': inst_id}
                            )

                            # 发布微观结构数据到Redis（用于仪表盘）
                            if self.redis_publisher:
                                try:
                                    # 查找对应的交易对名称
                                    pair_name = None
                                    for pname, pair_status in self.strategy_manager.pair_strategies.items():
                                        if inst_id in [pair_status.spot_symbol, pair_status.futures_symbol]:
                                            pair_name = pname
                                            break

                                    if pair_name:
                                        micro_data = {
                                            'obi': microstructure_signals.get('obi', 0.0),
                                            'buy_sell_ratio': microstructure_signals.get('buy_sell_ratio', 1.0),
                                            'micro_signal': microstructure_signals.get('micro_signal', 'neutral'),
                                            'micro_confidence': microstructure_signals.get('micro_confidence', 0.0),
                                            'obi_signal': microstructure_signals.get('obi_signal', 'neutral'),
                                            'flow_signal': microstructure_signals.get('flow_signal', 'neutral')
                                        }
                                        # 移除微观结构数据发布 - 现在由策略计算引擎统一管理
                                        pass
                                except Exception as redis_e:
                                    self.logger.debug(f"发布微观结构数据到Redis失败: {redis_e}")
                
                # 记录市场状态用于仪表盘显示
                await self._log_market_state_for_symbol(inst_id)
                    
        except Exception as e:
            # 记录错误指标
            # 错误统计通过结构化日志记录
            
            self.logger.error_structured(
                f"处理订单簿更新失败: {e}",
                event_type="books_update_error",
                details={
                    'error': str(e),
                    'raw_data': str(data)[:500]
                }
            )
            
            # 发布错误警报到Redis
            if self.redis_publisher:
                try:
                    await self.redis_publisher.publish_alert(
                        "ERROR",
                        f"处理订单簿更新失败: {e}",
                        {"component": "books_update", "error": str(e)}
                    )
                except Exception:
                    pass  # Redis发布失败不影响主要功能

    async def _log_market_state_for_symbol(self, symbol: str):
        """为指定交易对记录市场状态（用于仪表盘显示）"""
        try:
            # 获取对应的现货和期货价格
            spot_price = None
            futures_price = None

            if symbol in self.spot_prices:
                spot_price = self.spot_prices[symbol]
                # 查找对应的期货价格
                for pair_status in self.strategy_manager.pair_strategies.values():
                    if pair_status.spot_symbol == symbol:
                        futures_symbol = pair_status.futures_symbol
                        futures_price = self.futures_prices.get(futures_symbol)
                        break
            elif symbol in self.futures_prices:
                futures_price = self.futures_prices[symbol]
                # 查找对应的现货价格
                for pair_status in self.strategy_manager.pair_strategies.values():
                    if pair_status.futures_symbol == symbol:
                        spot_symbol = pair_status.spot_symbol
                        spot_price = self.spot_prices.get(spot_symbol)
                        break

            # 如果没有配对价格，跳过记录
            if not (spot_price and futures_price):
                return

            # 记录实时市场状态更新（用于仪表盘）
            await self._log_market_state_update_realtime(symbol, spot_price, futures_price)

        except Exception as e:
            self.logger.error(f"记录市场状态失败 {symbol}: {e}")



    async def _log_market_state_update_realtime_with_snapshot(self, price_snapshot: Dict[str, Any]):
        """
        使用价格快照记录市场状态更新事件（确保数据一致性）

        Args:
            price_snapshot: 包含一致性价格数据的快照
                {
                    'spot_price': float,
                    'futures_price': float,
                    'timestamp': datetime,
                    'spot_symbol': str,
                    'futures_symbol': str
                }
        """
        try:
            spot_symbol = price_snapshot['spot_symbol']
            futures_symbol = price_snapshot['futures_symbol']
            spot_price = price_snapshot['spot_price']
            futures_price = price_snapshot['futures_price']

            # 查找对应的交易对
            for pair_name, pair_status in self.strategy_manager.pair_strategies.items():
                if (pair_status.spot_symbol == spot_symbol and
                    pair_status.futures_symbol == futures_symbol):

                    # 获取策略状态 - 使用get_current_state()方法获取最新计算结果
                    strategy = pair_status.strategy
                    strategy_state = strategy.get_current_state()

                    # 使用快照数据计算基差（确保一致性）
                    current_basis = (futures_price - spot_price) / spot_price if spot_price > 0 else 0.0

                    # 从策略状态获取分层布林带数据
                    layered_bb = strategy_state.get('layered_bollinger_bands', {})
                    strategic_bb = layered_bb.get('strategic', {})
                    tactical_bb = layered_bb.get('tactical', {})

                    # 分层布林带（战略层）- 主要用于仪表盘显示
                    strategic_bb_middle = strategic_bb.get('bb_middle', None)
                    strategic_bb_upper = strategic_bb.get('bb_upper', None)
                    strategic_bb_lower = strategic_bb.get('bb_lower', None)

                    # 分层布林带（战术层）
                    tactical_bb_middle = tactical_bb.get('bb_middle', None)
                    tactical_bb_upper = tactical_bb.get('bb_upper', None)
                    tactical_bb_lower = tactical_bb.get('bb_lower', None)

                    # 传统布林带（向后兼容）
                    bb_middle = strategic_bb_middle  # 使用战略层作为默认
                    bb_upper = strategic_bb_upper
                    bb_lower = strategic_bb_lower

                    # 记录结构化日志
                    self.logger.info_structured(
                        f"实时市场状态更新 - {pair_name} (快照)",
                        event_type="strategy_state_update_snapshot",
                        metrics={
                            "current_basis": current_basis,
                            # 传统布林带
                            "bb_middle": bb_middle,
                            "bb_upper": bb_upper,
                            "bb_lower": bb_lower,
                            # 分层布林带 - 战略层（主要用于仪表盘显示）
                            "strategic_bb_middle": strategic_bb_middle,
                            "strategic_bb_upper": strategic_bb_upper,
                            "strategic_bb_lower": strategic_bb_lower,
                            # 分层布林带 - 战术层
                            "tactical_bb_middle": tactical_bb_middle,
                            "tactical_bb_upper": tactical_bb_upper,
                            "tactical_bb_lower": tactical_bb_lower,
                        },
                        details={
                            "pair_name": pair_name,
                            "spot_symbol": spot_symbol,
                            "futures_symbol": futures_symbol,
                            "spot_price": spot_price,
                            "futures_price": futures_price,
                            "snapshot_timestamp": price_snapshot['timestamp'].isoformat(),
                            "update_source": "websocket_realtime_snapshot",
                            # 策略状态信息
                            "strategy_state": getattr(strategy, 'strategy_state', 'unknown'),
                            "data_points": len(getattr(strategy, 'basis_history', [])),
                            "strategic_data_points": len(getattr(strategy, 'strategic_basis_history', [])),
                            "tactical_data_points": len(getattr(strategy, 'tactical_basis_history', []))
                        }
                    )
                    break

        except Exception as e:
            self.logger.error(f"记录快照市场状态失败: {e}")

    async def _log_market_state_update_realtime(self, symbol: str, spot_price: float, futures_price: float):
        """实时记录市场状态更新事件（WebSocket触发）- 保留向后兼容"""
        try:
            # 查找对应的交易对
            for pair_name, pair_status in self.strategy_manager.pair_strategies.items():
                if symbol in [pair_status.spot_symbol, pair_status.futures_symbol]:
                    # 获取策略状态 - 使用get_current_state()方法获取最新计算结果
                    strategy = pair_status.strategy
                    strategy_state = strategy.get_current_state()

                    # 使用策略计算的最新基差
                    current_basis = strategy_state.get('current_basis', 0.0)
                    if current_basis is None or current_basis == 0.0:
                        # 如果策略还没有基差数据，使用策略相同的计算公式
                        current_basis = (futures_price - spot_price) / spot_price if spot_price > 0 else 0.0

                    # 从策略状态获取分层布林带数据
                    layered_bb = strategy_state.get('layered_bollinger_bands', {})
                    strategic_bb = layered_bb.get('strategic', {})
                    tactical_bb = layered_bb.get('tactical', {})

                    # 分层布林带（战略层）- 主要用于仪表盘显示
                    strategic_bb_middle = strategic_bb.get('bb_middle', None)
                    strategic_bb_upper = strategic_bb.get('bb_upper', None)
                    strategic_bb_lower = strategic_bb.get('bb_lower', None)

                    # 分层布林带（战术层）
                    tactical_bb_middle = tactical_bb.get('bb_middle', None)
                    tactical_bb_upper = tactical_bb.get('bb_upper', None)
                    tactical_bb_lower = tactical_bb.get('bb_lower', None)

                    # 传统布林带（向后兼容）
                    bb_middle = strategic_bb_middle  # 使用战略层作为默认
                    bb_upper = strategic_bb_upper
                    bb_lower = strategic_bb_lower

                    # 记录结构化日志
                    self.logger.info_structured(
                        f"实时市场状态更新 - {pair_name}",
                        event_type="strategy_state_update",
                        metrics={
                            "current_basis": current_basis,
                            # 传统布林带
                            "bb_middle": bb_middle,
                            "bb_upper": bb_upper,
                            "bb_lower": bb_lower,
                            # 分层布林带 - 战略层（主要用于仪表盘显示）
                            "strategic_bb_middle": strategic_bb_middle,
                            "strategic_bb_upper": strategic_bb_upper,
                            "strategic_bb_lower": strategic_bb_lower,
                            # 分层布林带 - 战术层
                            "tactical_bb_middle": tactical_bb_middle,
                            "tactical_bb_upper": tactical_bb_upper,
                            "tactical_bb_lower": tactical_bb_lower,
                        },
                        details={
                            "pair_name": pair_name,
                            "symbol": symbol,
                            "spot_symbol": pair_status.spot_symbol,
                            "futures_symbol": pair_status.futures_symbol,
                            "spot_price": spot_price,
                            "futures_price": futures_price,
                            "update_source": "websocket_realtime",
                            # 策略状态信息
                            "strategy_state": getattr(strategy, 'strategy_state', 'unknown'),
                            "data_points": len(getattr(strategy, 'basis_history', [])),
                            "strategic_data_points": len(getattr(strategy, 'strategic_basis_history', [])),
                            "tactical_data_points": len(getattr(strategy, 'tactical_basis_history', []))
                        }
                    )
                    
                    # 发布Redis市场状态事件（用于仪表盘）
                    if self.redis_publisher:
                        try:
                            # 获取资金费率 - 使用WebSocket实时数据
                            funding_rate = 0.0
                            futures_symbol = pair_status.futures_symbol

                            # 优先使用WebSocket实时资金费率数据
                            if futures_symbol in self.funding_rates:
                                funding_rate = self.funding_rates[futures_symbol]
                                self.logger.debug(f"使用WebSocket资金费率: {futures_symbol} = {funding_rate:.6f}")
                            else:
                                # 如果WebSocket数据不可用，尝试API获取
                                try:
                                    if hasattr(self, 'connector') and self.connector:
                                        funding_data = await self.connector.get_funding_rate(futures_symbol)
                                        if funding_data:
                                            funding_rate = float(funding_data.get('fundingRate', 0))
                                            self.logger.debug(f"使用API资金费率: {futures_symbol} = {funding_rate:.6f}")
                                except Exception as e:
                                    self.logger.debug(f"获取资金费率失败: {e}")

                            # 计算最低开仓基差 - 使用策略实例的成本效益分析
                            min_entry_basis = None
                            basis_distance_pct = None
                            
                            # 获取策略实例和计算参数
                            if strategy:
                                try:
                                    # 使用与信号生成相同的参数计算position_size
                                    from config import calculate_position_size
                                    position_size = calculate_position_size(spot_price, pair_name)
                                    
                                    # 使用策略实例计算最低开仓基差（确保数据源一致性）
                                    min_entry_basis = strategy.calculate_min_entry_basis(position_size, funding_rate)
                                    
                                    if min_entry_basis is not None and min_entry_basis != 0:
                                        # 计算当前基差距离最低开仓基差的百分比
                                        # 公式：(当前基差 - 最低开仓基差) / |最低开仓基差| * 100
                                        basis_distance_pct = ((current_basis - min_entry_basis) / abs(min_entry_basis)) * 100
                                        
                                except Exception as calc_e:
                                    self.logger.debug(f"计算最低开仓基差失败 {pair_name}: {calc_e}")
                                    min_entry_basis = None
                                    basis_distance_pct = None

                            market_state = {
                                "basis": current_basis,
                                "spot_price": spot_price,
                                "futures_price": futures_price,
                                "funding_rate": funding_rate,
                                "bb_middle": strategic_bb_middle or bb_middle,
                                "bb_upper": strategic_bb_upper or bb_upper,
                                "bb_lower": strategic_bb_lower or bb_lower,
                                "strategic_bb_middle": strategic_bb_middle,
                                "strategic_bb_upper": strategic_bb_upper,
                                "strategic_bb_lower": strategic_bb_lower,
                                "tactical_bb_middle": tactical_bb_middle,
                                "tactical_bb_upper": tactical_bb_upper,
                                "tactical_bb_lower": tactical_bb_lower,
                                # 新增最低开仓基差相关数据
                                "min_entry_basis": min_entry_basis,
                                "basis_distance_pct": basis_distance_pct,
                                "update_source": "websocket_realtime",
                                "is_realtime": True,
                                "strategy_state": strategy_state.get('strategy_state', {}).get('current_state', 'unknown'),
                                "data_points": len(strategy_state.get('basis_history', []))
                            }

                            # 关键调试：记录发布到Redis的完整数据
                            strategic_middle_str = f"{strategic_bb_middle:.6f}" if strategic_bb_middle is not None else "None"
                            strategic_upper_str = f"{strategic_bb_upper:.6f}" if strategic_bb_upper is not None else "None"
                            min_entry_str = f"{min_entry_basis:.6f}" if min_entry_basis is not None else "None"
                            distance_str = f"{basis_distance_pct:.2f}%" if basis_distance_pct is not None else "None"

                            self.logger.debug(f"📡 发布市场状态到Redis - {pair_name}: "
                                             f"基差={current_basis:.6f}, "
                                             f"现货=${spot_price:.5f}, 期货=${futures_price:.5f}, "
                                             f"资金费率={funding_rate:.6f}, "
                                             f"战略中轨={strategic_middle_str}, "
                                             f"战略上轨={strategic_upper_str}, "
                                             f"最低开仓基差={min_entry_str}, "
                                             f"距离开仓阈值={distance_str}")

                            # 移除市场数据发布 - 现在由策略计算引擎统一管理
                            pass
                        except Exception as redis_e:
                            self.logger.debug(f"市场状态处理完成")
                    
                    break

        except Exception as e:
            self.logger.error(f"记录实时市场状态更新失败: {e}")







    async def handle_trades_update(self, data: Dict[str, Any]):
        """处理逐笔成交更新事件（微观结构分析）"""
        try:
            inst_id = data["arg"]["instId"]
            
            # 仅处理现货的逐笔成交数据
            if inst_id == self.config.SPOT_ID:
                trades = self.microstructure_adapter.process_trade_update(data)
                
                if trades:
                    # 统计成交信息
                    total_volume = sum(trade.size for trade in trades)
                    buy_volume = sum(trade.size for trade in trades if trade.side == 'buy')
                    sell_volume = sum(trade.size for trade in trades if trade.side == 'sell')
                    
                    # 高频交易流数据使用DEBUG级别避免日志洪水
                    self.logger.debug_structured(
                        f"逐笔成交数据 {inst_id} 批次处理",
                        event_type="trades_batch_processed",
                        metrics={
                            'trade_count': len(trades),
                            'total_volume': total_volume,
                            'buy_volume': buy_volume,
                            'sell_volume': sell_volume,
                            'buy_ratio': buy_volume / total_volume if total_volume > 0 else 0
                        },
                        details={'symbol': inst_id}
                    )
                    
                    # 获取更新后的交易流指标
                    indicators = self.microstructure_adapter.get_latest_indicators(inst_id)
                    
                    # 更新Prometheus交易压力指标
                    volume_pressure = indicators.get('volume_pressure', 0)
                    # 交易压力通过结构化日志记录
                    
                    # 只在交易压力显著偏向时记录DEBUG级别日志
                    if abs(volume_pressure) > 0.3:  # 压力超过30%时记录
                        pressure_type = '买压' if volume_pressure > 0 else '卖压'
                        self.logger.debug_structured(
                            f"检测到显著{pressure_type} {inst_id}",
                            event_type="significant_trade_pressure",
                            metrics={
                                'volume_pressure': volume_pressure,
                                'buy_sell_ratio': indicators.get('buy_sell_ratio'),
                                'trade_intensity': indicators.get('trade_intensity')
                            },
                            details={'symbol': inst_id}
                        )
                
        except Exception as e:
            # 记录错误指标
            # 错误统计通过结构化日志记录
            
            self.logger.error_structured(
                f"处理逐笔成交更新失败: {e}",
                event_type="trades_update_error",
                details={
                    'error': str(e),
                    'raw_data': str(data)[:500]
                }
            )
    
    async def handle_funding_rate_update(self, data: Dict[str, Any]):
        """处理资金费率更新事件 - 多交易对版本"""
        try:
            funding_data = data.get("data", [])
            
            for funding_info in funding_data:
                inst_id = funding_info.get("instId")
                funding_rate = float(funding_info.get("fundingRate", 0))
                funding_time = funding_info.get("fundingTime")
                
                # 更新对应期货合约的资金费率
                if inst_id in self.subscribed_futures_instruments:
                    old_rate = self.funding_rates.get(inst_id, 0.0)
                    self.funding_rates[inst_id] = funding_rate
                    self.last_funding_updates[inst_id] = datetime.now()
                    
                    # 更新Prometheus资金费率指标
                    # 资金费率通过结构化日志记录
                    
                    self.logger.debug_structured(
                        "资金费率更新",
                        event_type="funding_rate_update",
                        metrics={
                            "funding_rate_bps": KPICalculator.funding_rate_bps(funding_rate),
                            "funding_rate_change_bps": (funding_rate - old_rate) * 10000,
                            "old_funding_rate": old_rate,
                            "new_funding_rate": funding_rate
                        },
                        details={
                            "inst_id": inst_id,
                            "funding_time": funding_time
                        }
                    )
                    
                    # 关键：资金费率更新后触发多交易对策略重新评估
                    await self._check_all_strategies_async()
                    
        except Exception as e:
            # 记录错误指标
            # 错误统计通过结构化日志记录
            
            self.logger.error(f"处理资金费率更新失败: {e}")
    
    async def handle_account_update(self, data: Dict[str, Any]):
        """处理账户更新事件"""
        try:
            # 更新账户余额状态
            account_data = data.get("data", [])
            for account_info in account_data:
                ccy = account_info.get("ccy")
                cash_bal = account_info.get("cashBal")
                if ccy and cash_bal:
                    self.account_balance[ccy] = {
                        "balance": float(cash_bal),
                        "update_time": datetime.now()
                    }
            
            self.last_balance_update_time = datetime.now()
            self.logger.debug(f"💰 账户余额更新: {len(account_data)} 条记录")
            
        except Exception as e:
            # 记录错误指标
            # 错误统计通过结构化日志记录
            
            self.logger.error(f"处理账户更新失赅: {e}")
    
    async def handle_position_update(self, data: Dict[str, Any]):
        """处理持仓更新事件（取代原有的轮询检查）"""
        try:
            position_data = data.get("data", [])
            
            for pos_info in position_data:
                inst_id = pos_info.get("instId")
                pos_size = float(pos_info.get("pos", 0))
                unrealized_pnl = float(pos_info.get("upl", 0))
                margin_ratio = float(pos_info.get("mgnRatio", 0))
                
                # 更新内部状态
                self.current_positions[inst_id] = {
                    "size": pos_size,
                    "unrealized_pnl": unrealized_pnl,
                    "margin_ratio": margin_ratio,
                    "update_time": datetime.now(),
                    "raw_data": pos_info
                }
                
                # 实时更新PositionManager的未实现盈亏（多交易对版本暂时跳过）
                # TODO: 需要根据持仓的具体交易对来更新PnL
                pass
                
                # 实时风险检查（取代轮询）
                if self.position_manager.in_position:
                    await self._check_position_risks_realtime(pos_info)
            
            self.last_position_update_time = datetime.now()
            self.logger.debug(f"🎯 持仓更新: {len(position_data)} 条记录")
            
        except Exception as e:
            # 记录错误指标
            # 错误统计通过结构化日志记录
            
            self.logger.error(f"处理持仓更新失败: {e}")
    
    async def handle_order_update(self, data: Dict[str, Any]):
        """处理订单更新事件（关键：PositionManager状态变更驱动）"""
        try:
            order_data = data.get("data", [])
            
            for order_info in order_data:
                order_id = order_info.get("ordId")
                inst_id = order_info.get("instId")
                state = order_info.get("state")
                side = order_info.get("side")
                filled_size = float(order_info.get("fillSz", 0))
                avg_price = float(order_info.get("avgPx", 0))
                
                # 更新内部订单状态
                self.open_orders[order_id] = {
                    "inst_id": inst_id,
                    "state": state,
                    "side": side,
                    "filled_size": filled_size,
                    "avg_price": avg_price,
                    "update_time": datetime.now(),
                    "raw_data": order_info
                }
                
                self.logger.info(
                    f"📱 订单状态更新: {order_id} ({inst_id}) {side} {state} "
                    f"filled:{filled_size} @ {avg_price}"
                )
                
                # 关键：仅在完全成交时才触发PositionManager状态变更
                if state == "filled":
                    await self._handle_order_filled_for_position_manager(order_info)
                elif state == "partially_filled":
                    self.logger.info(f"🔄 订单部分成交，等待完全成交: {order_id}")
                elif state == "canceled":
                    self.logger.warning(f"❌ 订单被取消: {order_id} - 可能影响仓位状态")
                    await self._handle_order_canceled(order_info)
            
            self.last_order_update_time = datetime.now()
            
        except Exception as e:
            # 记录错误指标
            # 错误统计通过结构化日志记录
            
            self.logger.error(f"处理订单更新失败: {e}")
    
    async def _handle_order_filled_for_position_manager(self, order_info: Dict[str, Any]):
        """处理订单成交事件（PositionManager状态变更驱动）"""
        try:
            order_id = order_info.get("ordId")
            inst_id = order_info.get("instId")
            side = order_info.get("side")
            filled_size = float(order_info.get("fillSz", 0))
            avg_price = float(order_info.get("avgPx", 0))
            
            self.logger.info(
                f"✅ 订单完全成交: {order_id} ({inst_id}) {side} {filled_size} @ {avg_price}"
            )
            
            # 关键：仅在有活跃仓位时才处理PositionManager状态变更
            if self.position_manager.current_position:
                current_status = self.position_manager.current_position.status.value
                
                if current_status == "opening":
                    # 开仓订单成交 - 只有在收到交易所确认时才更新状态
                    await self._handle_entry_order_filled_real_time(order_info)
                elif current_status == "closing":
                    # 平仓订单成交 - 只有在收到交易所确认时才更新状态
                    await self._handle_exit_order_filled_real_time(order_info)
                else:
                    self.logger.warning(
                        f"⚠️  意外的订单成交：仓位状态={current_status}, 订单={order_id}"
                    )
            else:
                self.logger.warning(
                    f"⚠️  没有活跃仓位时收到订单成交: {order_id} - 可能是对冲订单"
                )
            
            # 从未完成订单中移除
            if order_id in self.open_orders:
                del self.open_orders[order_id]
                
        except Exception as e:
            self.logger.error(f"处理订单成交驱动状态变更失败: {e}")
    
    async def _handle_order_canceled(self, order_info: Dict[str, Any]):
        """处理订单取消事件"""
        try:
            order_id = order_info.get("ordId")
            inst_id = order_info.get("instId")
            
            # 从未完成订单中移除
            if order_id in self.open_orders:
                del self.open_orders[order_id]
            
            # 如果有活跃仓位且是关键订单被取消，可能需要设置错误状态
            if (self.position_manager.current_position and 
                self.position_manager.current_position.status.value in ["opening", "closing"]):
                
                self.logger.error(
                    f"⚠️  关键订单被取消，可能导致仓位状态不一致: {order_id} ({inst_id})"
                )
                # 可能需要设置仓位为错误状态或触发紧急处理
                
        except Exception as e:
            self.logger.error(f"处理订单取消失败: {e}")
    
    async def _handle_entry_order_filled_real_time(self, order_info: Dict[str, Any]):
        """处理开仓订单成交（事件驱动版本）"""
        try:
            inst_id = order_info.get("instId")
            order_id = order_info.get("ordId")
            filled_size = float(order_info.get("fillSz", 0))
            avg_price = float(order_info.get("avgPx", 0))
            
            self.logger.info(
                f"🔄 开仓订单成交: {inst_id} {order_id} 成交{filled_size} @ {avg_price}"
            )
            
            # 更新PositionManager中的订单信息
            await self._update_position_order_info(order_info)
            
            # 检查是否两条腿都成交了（基于实时订单状态）
            both_legs_filled = await self._check_both_legs_filled_for_entry()
            
            if both_legs_filled:
                # 关键：只有在收到交易所真实成交确认时才调用confirm_position_open
                self.position_manager.confirm_position_open()
                
                # 发布开仓成功事件到Redis（用于仪表盘）
                if self.redis_publisher and self.position_manager.current_position:
                    try:
                        position_info = self.position_manager.get_position_info()
                        trade_data = {
                            "action": "position_opened",
                            "position_id": position_info['position_id'],
                            "position_type": position_info['position_type'],
                            "entry_basis": position_info['entry_basis'],
                            "entry_spot_price": position_info['entry_spot_price'],
                            "entry_futures_price": position_info['entry_futures_price'],
                            "open_time": position_info['open_time']
                        }
                        await self.redis_publisher.publish_trade_event(
                            "position_opened",
                            self.config.TRADE_PAIR,
                            trade_data
                        )
                    except Exception as redis_e:
                        self.logger.debug(f"发布开仓事件到Redis失败: {redis_e}")
                
                # 更新Prometheus持仓状态指标
                # 持仓状态通过结构化日志记录
                # 交易记录通过结构化日志记录
                
                self.logger.info(
                    f"🎉 套利开仓完成！两条腿都收到交易所成交确认"
                )
                
                # 状态已由PositionManager自动保存
                
                # 清理开仓锁定
                self.is_opening_position = False
                
                # 记录关键信息
                position_info = self.position_manager.get_position_info()
                self.logger.info(
                    f"📊 仓位已确认: {position_info['position_id']} "
                    f"{position_info['position_type']} 基差:{position_info.get('entry_basis', 'N/A'):.6f}"
                )
            else:
                self.logger.info(
                    f"🔄 单腿成交完成，等待另一条腿: {inst_id}"
                )
                
        except Exception as e:
            self.logger.error(f"处理开仓订单实时成交失败: {e}")
    
    async def _handle_exit_order_filled_real_time(self, order_info: Dict[str, Any]):
        """处理平仓订单成交（事件驱动版本）"""
        try:
            inst_id = order_info.get("instId")
            order_id = order_info.get("ordId")
            filled_size = float(order_info.get("fillSz", 0))
            avg_price = float(order_info.get("avgPx", 0))
            
            self.logger.info(
                f"🔄 平仓订单成交: {inst_id} {order_id} 成交{filled_size} @ {avg_price}"
            )
            
            # 检查是否两条腿都平仓完成（基于实时订单状态）
            both_legs_filled = await self._check_both_legs_filled_for_exit()
            
            if both_legs_filled:
                # 关键：只有在收到交易所真实成交确认时才调用confirm_position_close
                current_pos = self.position_manager.current_position
                realized_pnl = current_pos.unrealized_pnl if current_pos else 0.0
                
                # 保存平仓信息用于Redis发布
                position_data = {
                    "position_id": current_pos.position_id if current_pos else "unknown",
                    "realized_pnl": realized_pnl,
                    "close_reason": "manual_exit"  # 这里可以传入实际原因
                }
                
                self.position_manager.confirm_position_close(realized_pnl)
                
                # 发布平仓成功事件到Redis（用于仪表盘）
                if self.redis_publisher:
                    try:
                        trade_data = {
                            "action": "position_closed",
                            "position_id": position_data['position_id'],
                            "realized_pnl": realized_pnl,
                            "close_reason": position_data['close_reason'],
                            "close_time": datetime.now()
                        }
                        await self.redis_publisher.publish_trade_event(
                            "position_closed",
                            self.config.TRADE_PAIR,
                            trade_data
                        )
                    except Exception as redis_e:
                        self.logger.debug(f"发布平仓事件到Redis失败: {redis_e}")
                
                # 更新Prometheus平仓相关指标
                # 持仓状态通过结构化日志记录  # 设置为无持仓
                # 交易记录通过结构化日志记录
                
                # 更新总盈亏（这里应该从position_manager获取累计PnL）
                # TODO: 需要在position_manager中维护累计PnL
                # 盈亏统计通过结构化日志记录  # 临时使用单笔PnL作为总PnL
                
                self.logger.info(
                    f"🎉 套利平仓完成！两条腿都收到交易所成交确认 "
                    f"实现盈亏: {realized_pnl:.4f}"
                )
                
                # 空仓状态已由PositionManager自动保存
                
                # 🔥 架构优化：清理多交易对策略状态
                # 根据平仓的交易对清理对应策略状态
                if current_pos and hasattr(current_pos, 'pair_name'):
                    self.strategy_manager.clear_position_entry(current_pos.pair_name)
                else:
                    # 兼容性处理：清理所有交易对状态（如果无法确定具体交易对）
                    for pair_name in self.strategy_manager.pair_strategies.keys():
                        self.strategy_manager.clear_position_entry(pair_name)
                
                # 记录关键统计
                total_stats = self.position_manager.get_statistics()
                self.logger.info(
                    f"📊 交易完成: 总交易{total_stats['total_trades']}次 "
                    f"胜率{total_stats['win_rate']:.2%} 总盈亏{total_stats['total_pnl']:.4f}"
                )
            else:
                self.logger.info(
                    f"🔄 单腿平仓完成，等待另一条腿: {inst_id}"
                )
                
        except Exception as e:
            self.logger.error(f"处理平仓订单实时成交失败: {e}")
    
    async def _update_position_order_info(self, order_info: Dict[str, Any]):
        """更新PositionManager中的订单信息"""
        try:
            if not self.position_manager.current_position:
                return
            
            # 从 position_manager 导入 OrderInfo
            from position_manager import OrderInfo
            
            inst_id = order_info.get("instId")
            order_id = order_info.get("ordId")
            side = order_info.get("side")
            filled_size = order_info.get("fillSz", "0")
            avg_price = order_info.get("avgPx")
            
            # 创建 OrderInfo 对象
            order_obj = OrderInfo(
                order_id=order_id,
                inst_id=inst_id,
                side=side,
                size=filled_size,
                price=avg_price,
                status="filled",
                fill_size=filled_size,
                avg_px=avg_price
            )
            
            # 更新对应的订单信息
            if inst_id == self.config.SPOT_ID:
                self.position_manager.current_position.spot_order = order_obj
                # 更新现货订单信息（Prometheus已记录）
                pass
            elif inst_id == self.config.FUTURES_ID:
                self.position_manager.current_position.futures_order = order_obj
                # 更新期货订单信息（Prometheus已记录）
                pass
                
        except Exception as e:
            self.logger.error(f"更新仓位订单信息失败: {e}")
    
    async def _check_both_legs_filled_for_entry(self) -> bool:
        """检查开仓的两条腿是否都成交（基于实时订单状态）"""
        try:
            # 检查是否有现货和期货的成交订单
            spot_filled = False
            futures_filled = False
            
            for order_id, order_data in self.open_orders.items():
                if order_data["state"] == "filled":
                    if order_data["inst_id"] == self.config.SPOT_ID:
                        spot_filled = True
                    elif order_data["inst_id"] == self.config.FUTURES_ID:
                        futures_filled = True
            
            return spot_filled and futures_filled
            
        except Exception as e:
            self.logger.error(f"检查开仓两条腿状态失败: {e}")
            return False
    
    async def _check_both_legs_filled_for_exit(self) -> bool:
        """检查平仓的两条腿是否都成交（基于实时订单状态）"""
        try:
            # 平仓逻辑与开仓类似，检查两条腿的平仓订单状态
            spot_filled = False
            futures_filled = False
            
            for order_id, order_data in self.open_orders.items():
                if order_data["state"] == "filled":
                    if order_data["inst_id"] == self.config.SPOT_ID:
                        spot_filled = True
                    elif order_data["inst_id"] == self.config.FUTURES_ID:
                        futures_filled = True
            
            return spot_filled and futures_filled
            
        except Exception as e:
            self.logger.error(f"检查平仓两条腿状态失败: {e}")
            return False
    
    async def _check_position_risks_realtime(self, position_data: Dict[str, Any]):
        """实时风险检查（取代轮询检查）"""
        try:
            # 检查时间止损
            if self.position_manager.check_time_stop_loss():
                self.logger.warning("⚠️  时间止损触发")
                await self._execute_exit("时间止损")
                return
            
            # 检查PnL止损
            if self.position_manager.check_pnl_stop_loss():
                self.logger.warning("⚠️  PnL止损触发")
                await self._execute_exit("PnL止损")
                return
            
            # 检查保证金风险
            if self.position_manager.check_margin_risk(position_data):
                self.logger.warning("⚠️  保证金风险触发")
                await self._execute_exit("保证金风险")
                return
                
        except Exception as e:
            self.logger.error(f"实时风险检查失败: {e}")
    
    # 保留原有的_process_price_update逻辑，但由handle_ticker_update直接调用_check_entry_signals
    async def _check_strategy_readiness(self):
        """检查策略状态是否就绪 - 不再重复更新价格"""
        try:
            # 🔥 架构优化：移除冗余的 update_prices 调用
            # 价格更新已由事件驱动的 handle_ticker_update 处理
            
            # 检查多交易对策略管理器是否有可用的策略数据
            strategy_count = len(self.strategy_manager.pair_strategies)
            if strategy_count == 0:
                self.logger.debug("⚠️ 没有可用的交易对策略")
                return False
            
            # 增强的数据一致性检查
            data_consistency_check = await self._validate_data_consistency()
            if not data_consistency_check:
                return False
            
            # 检查是否有足够的实时数据用于信号计算
            ready_pairs = 0
            for pair_name, pair_status in self.strategy_manager.pair_strategies.items():
                if pair_status.enabled:
                    strategy = pair_status.strategy
                    # 检查战略层数据是否充足
                    strategic_data_count = len(strategy.strategic_basis_history)
                    if strategic_data_count >= max(strategy.strategic_window // 2, 5):
                        ready_pairs += 1
            
            strategy_ready = ready_pairs > 0
            
            if not strategy_ready:
                if self.total_updates % 100 == 0:  # 减少日志频率
                    self.logger.debug(f"策略数据收集中: {ready_pairs}/{strategy_count}个交易对就绪")
                return False
            
            # 策略已就绪，可以进行信号检查
            self.logger.debug(f"✅ 策略状态就绪: {ready_pairs}/{strategy_count}个交易对可用")
            return True
            
        except Exception as e:
            self.logger.error(f"检查策略就绪状态失败: {e}")
            return False
    
    async def _validate_data_consistency(self) -> bool:
        """验证数据一致性和新鲜度"""
        try:
            current_time = datetime.now()
            issues = []
            
            # 检查价格数据新鲜度（应在5秒内更新）
            price_freshness_threshold = 5.0
            for symbol, last_update in self.last_price_updates.items():
                if last_update:
                    age = (current_time - last_update).total_seconds()
                    if age > price_freshness_threshold:
                        issues.append(f"{symbol}价格数据过时({age:.1f}s)")
            
            # 检查策略布林带数据是否正常更新
            bb_issues = 0
            for pair_name, pair_status in self.strategy_manager.pair_strategies.items():
                if pair_status.enabled:
                    strategy = pair_status.strategy
                    # 检查战略布林带是否为None
                    if (strategy.strategic_bb_middle is None or 
                        strategy.strategic_bb_upper is None or 
                        strategy.strategic_bb_lower is None):
                        bb_issues += 1
            
            if bb_issues > 0:
                issues.append(f"{bb_issues}个交易对布林带数据缺失")
            
            # 检查事件驱动vs策略循环的数据流一致性
            event_driven_updates = self.total_updates
            strategy_data_points = sum(
                len(ps.strategy.strategic_basis_history) 
                for ps in self.strategy_manager.pair_strategies.values()
                if ps.enabled
            )
            
            # 如果数据点数量与更新次数差异过大，可能存在问题
            if event_driven_updates > 100 and strategy_data_points < event_driven_updates * 0.1:
                issues.append(f"策略数据积累异常(更新{event_driven_updates}次但只有{strategy_data_points}个数据点)")
            
            # 记录检查结果
            if issues:
                self.logger.warning(f"⚠️ 数据一致性问题: {'; '.join(issues)}")
                
                # 每100次检查输出一次详细诊断
                if self.total_updates % 100 == 0:
                    await self._log_data_consistency_diagnostics()
                
                # 如果问题严重，暂停信号检查
                critical_issues = [issue for issue in issues if "布林带数据缺失" in issue or "数据积累异常" in issue]
                return len(critical_issues) == 0
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据一致性验证失败: {e}")
            return True  # 验证失败时不阻塞系统运行
    
    async def _log_data_consistency_diagnostics(self):
        """记录数据一致性诊断信息"""
        try:
            current_time = datetime.now()
            diagnostics = {
                "timestamp": current_time.isoformat(),
                "event_driven_updates": self.total_updates,
                "pairs_status": {}
            }
            
            for pair_name, pair_status in self.strategy_manager.pair_strategies.items():
                if pair_status.enabled:
                    strategy = pair_status.strategy
                    
                    # 价格数据状态
                    spot_age = None
                    futures_age = None
                    if pair_status.spot_symbol in self.last_price_updates:
                        spot_age = (current_time - self.last_price_updates[pair_status.spot_symbol]).total_seconds()
                    if pair_status.futures_symbol in self.last_price_updates:
                        futures_age = (current_time - self.last_price_updates[pair_status.futures_symbol]).total_seconds()
                    
                    diagnostics["pairs_status"][pair_name] = {
                        "strategic_data_points": len(strategy.strategic_basis_history),
                        "tactical_data_points": len(strategy.tactical_basis_history),
                        "bb_middle": strategy.strategic_bb_middle,
                        "bb_upper": strategy.strategic_bb_upper,
                        "bb_lower": strategy.strategic_bb_lower,
                        "spot_price_age_seconds": spot_age,
                        "futures_price_age_seconds": futures_age,
                        "last_forced_calculation": strategy.last_forced_calculation_time.isoformat() if strategy.last_forced_calculation_time else None,
                        "last_successful_calculation": strategy.last_successful_calculation_time.isoformat() if strategy.last_successful_calculation_time else None
                    }
            
            self.logger.info(f"🔍 数据一致性诊断: {diagnostics}")
            
        except Exception as e:
            self.logger.error(f"记录诊断信息失败: {e}")
    
    async def _check_all_strategies_async(self):
        """检查所有交易对策略 - 多交易对版本"""
        if self.is_opening_position:
            return  # 静默跳过，减少日志噪音
        
        try:
            # 检查所有交易对的入场信号
            signals = self.strategy_manager.check_all_entry_signals(self.funding_rates)
            
            # 调试日志
            if signals:
                self.logger.info(f"🔍 检测到{len(signals)}个信号，持仓状态: {self.position_manager.in_position}")
            
            if signals and self.position_manager.in_position:
                self.logger.info("⚠️  有活跃仓位，跳过新信号")
                return  # 简化：如果有持仓就不开新仓
            
            # 获取最优信号
            best_signal = self.strategy_manager.get_priority_signal(signals)
            
            if best_signal:
                self.logger.info(f"🎯 准备执行最优信号: {best_signal['pair_name']} - {best_signal['signal_type']}")
                await self._execute_multi_pair_signal(best_signal)
            elif signals:
                self.logger.warning(f"⚠️  有{len(signals)}个信号但get_priority_signal返回None")
            
            # 检查出场信号
            exit_pairs = self.strategy_manager.check_all_exit_signals()
            for pair_name in exit_pairs:
                await self._execute_pair_exit(pair_name)
                
        except Exception as e:
            self.logger.error(f"多交易对策略检查失败: {e}")
    
    async def _execute_multi_pair_signal(self, signal: Dict[str, Any]):
        """执行多交易对信号"""
        try:
            pair_name = signal["pair_name"]
            spot_symbol = signal["spot_symbol"] 
            futures_symbol = signal["futures_symbol"]
            signal_type = signal["signal_type"]
            position_size = signal["position_size"]
            
            self.logger.info(
                f"🎯 执行{pair_name}套利信号 - 类型:{signal_type}, "
                f"评级:{signal['risk_grade']}, 置信度:{signal['confidence']:.3f}"
            )
            
            # 记录信号指标
            # 信号统计通过结构化日志记录
            
            # 设置开仓锁定
            self.is_opening_position = True
            
            try:
                # 执行开仓交易
                success = await self._execute_arbitrage_trades(
                    pair_name, signal_type, position_size, 
                    spot_symbol, futures_symbol,
                    signal["current_spot_price"], signal["current_futures_price"]
                )
                
                if success:
                    # 更新策略管理器状态
                    self.strategy_manager.set_position_entry(
                        pair_name, signal_type, signal["current_spot_price"]
                    )
                
            finally:
                self.is_opening_position = False
                
        except Exception as e:
            self.logger.error(f"执行多交易对信号失败: {e}")
            self.is_opening_position = False
    
    async def _execute_pair_exit(self, pair_name: str):
        """执行交易对平仓"""
        try:
            pair_status = self.strategy_manager.get_pair_status(pair_name)
            if not pair_status or not pair_status["active_position"]:
                return
            
            self.logger.info(f"🚪 执行{pair_name}平仓 - 类型:{pair_status['position_type']}")
            
            # 执行平仓交易（具体实现需要根据position_manager接口调整）
            success = await self._close_position_for_pair(pair_name, pair_status)
            
            if success:
                # 清除策略管理器状态
                self.strategy_manager.clear_position_entry(pair_name)
                
        except Exception as e:
            self.logger.error(f"执行{pair_name}平仓失败: {e}")
    
    async def _execute_arbitrage_trades(self, pair_name: str, signal_type: str, 
                                       position_size: float, spot_symbol: str, 
                                       futures_symbol: str, spot_price: float, 
                                       futures_price: float) -> bool:
        """执行套利交易 - 多交易对版本"""
        try:
            # 🛡️ 应急检查：确认交易是否被允许
            if self.emergency_handler and not self.emergency_handler.is_trading_allowed():
                self.logger.warning(f"⛔ 交易被应急系统阻止: {pair_name}")
                return False
            
            self.logger.info(
                f"🚀 开始执行{pair_name}套利交易: {signal_type}, 仓位:{position_size}"
            )
            
            # 确定仓位类型 - 简化为单一套利类型
            if signal_type == "short_futures_long_spot":
                position_type = PositionType.SHORT_FUTURES_LONG_SPOT
            else:
                self.logger.error(f"未知信号类型: {signal_type}")
                return False
            
            # 创建仓位
            position_id = self.position_manager.create_position(
                position_type, spot_price, futures_price
            )
            
            # 确定交易方向
            spot_side, futures_side = self._determine_trade_sides(signal_type, "entry")
            
            # 使用动态合约规格计算期货数量
            futures_contracts = self.connector.calculate_futures_quantity(
                spot_quantity=position_size,
                spot_price=spot_price,
                futures_inst_id=futures_symbol
            )
            
            # 确保不少于最小交易单位
            min_contracts = self.connector.get_lot_size(futures_symbol)
            futures_contracts = max(futures_contracts, min_contracts)
            
            # 准备订单参数 - 移除ord_type让智能执行系统自动处理
            spot_params = {
                "inst_id": spot_symbol,
                "trade_mode": "cash",
                "side": spot_side,
                "sz": str(position_size)
            }
            
            futures_params = {
                "inst_id": futures_symbol,
                "trade_mode": "cross",
                "side": futures_side,
                "sz": str(futures_contracts)
            }
            
            self.logger.info(
                f"📋 订单参数: 现货({spot_side})={position_size}, 期货({futures_side})={futures_contracts:.6f}"
            )
            
            # 并发执行套利订单
            spot_result, futures_result = await self.connector.place_arbitrage_orders(
                spot_params, futures_params
            )
            
            # 检查执行结果
            spot_success = spot_result and spot_result.get("success", False)
            futures_success = futures_result and futures_result.get("success", False)
            
            if spot_success and futures_success:
                self.logger.info(f"✅ {pair_name}套利交易执行成功")
                return True
            else:
                self.logger.error(
                    f"❌ {pair_name}套利交易执行失败 - "
                    f"现货:{spot_success}, 期货:{futures_success}"
                )
                return False
                
        except Exception as e:
            self.logger.error(f"执行{pair_name}套利交易异常: {e}")
            return False
    
    async def _close_position_for_pair(self, pair_name: str, pair_status: Dict) -> bool:
        """平仓指定交易对 - 多交易对版本"""
        # 这里需要根据实际的position_manager接口来实现
        # 暂时返回True，具体实现需要在后续步骤中完成
        self.logger.info(f"平仓{pair_name}交易对")
        return True
    
    async def _check_entry_signals(self):
        """检查入场信号（多交易对事件驱动版本）"""
        # 并发开仓锁定检查
        if self.is_opening_position:
            return  # 静默跳过，减少日志噪音
            
        if self.position_manager.in_position:
            return  # 简化：如果有持仓就不开新仓
        
        try:
            # 🔥 架构优化：检查策略状态是否就绪（不再重复更新价格）
            if not await self._check_strategy_readiness():
                return  # 策略数据收集中，等待就绪
            
            # 🔥 架构优化：使用多交易对策略管理器检查信号
            # 检查所有交易对的入场信号
            signals = self.strategy_manager.check_all_entry_signals(self.funding_rates)
            
            if not signals:
                # 没有信号，正常返回
                return
            
            # 获取最优信号（已按优先级和置信度排序）
            signal = self.strategy_manager.get_priority_signal(signals)
            
            if signal:
                # 🔥 架构优化：多交易对信号处理
                pair_name = signal["pair_name"]
                position_size = signal["position_size"]  # 多交易对管理器已计算好仓位大小
                
                # 获取该交易对策略的微观结构仓位调整建议
                pair_strategy = self.strategy_manager.get_strategy(pair_name)
                if pair_strategy:
                    microstructure_multiplier = pair_strategy.get_microstructure_position_multiplier(signal)
                    final_position_size = position_size * microstructure_multiplier
                else:
                    microstructure_multiplier = 1.0
                    final_position_size = position_size
                
                # 更新微观结构置信度指标
                microstructure_info = signal.get("microstructure_analysis", {})
                if microstructure_info.get('available', False):
                    micro_confidence = microstructure_info.get('micro_confidence', 0)
                    # 微观结构置信度通过结构化日志记录
                
                # 更新信号中的实际仓位大小
                signal['final_position_size'] = final_position_size
                signal['base_position_size'] = position_size
                signal['microstructure_multiplier'] = microstructure_multiplier
                
                net_analysis = signal.get("net_profit_analysis", {})
                microstructure_info = signal.get("microstructure_analysis", {})
                
                self.logger.info_structured(
                    f"多交易对净利润信号检测 - {pair_name}: {signal['signal_type']}",
                    event_type="profitable_signal_detected",
                    metrics={
                        "pair_name": pair_name,
                        "spot_symbol": signal["spot_symbol"],
                        "futures_symbol": signal["futures_symbol"],
                        "signal_type": signal['signal_type'],
                        "risk_grade": signal['risk_grade'],
                        "confidence": signal['confidence'],
                        "priority": signal['priority'],
                        "original_confidence": signal.get('original_confidence'),
                        "potential_gross_profit": net_analysis.get('potential_gross_profit', 0),
                        "total_costs": net_analysis.get('total_costs', 0),
                        "net_profit": net_analysis.get('net_profit', 0),
                        "profit_margin": net_analysis.get('profit_margin', 0),
                        "base_position_size": position_size,
                        "final_position_size": final_position_size,
                        "microstructure_multiplier": microstructure_multiplier,
                        "current_spot_price": signal["current_spot_price"],
                        "current_futures_price": signal["current_futures_price"]
                    },
                    details={
                        "funding_impact": net_analysis.get('funding_impact', 0),
                        "safety_margin": net_analysis.get('safety_margin_applied'),
                        "microstructure_available": microstructure_info.get('available', False),
                        "microstructure_signal": microstructure_info.get('micro_signal'),
                        "enhancement_reason": microstructure_info.get('enhancement_reason')
                    },
                    trace_id=signal.get('trace_id')
                )
                
                # 发布Redis信号事件（用于仪表盘）
                if self.redis_publisher:
                    try:
                        signal_data = {
                            "pair_name": pair_name,
                            "spot_symbol": signal["spot_symbol"],
                            "futures_symbol": signal["futures_symbol"],
                            "signal_type": signal['signal_type'],
                            "risk_grade": signal['risk_grade'],
                            "confidence": signal['confidence'],
                            "priority": signal['priority'],
                            "original_confidence": signal.get('original_confidence'),
                            "net_profit": net_analysis.get('net_profit', 0),
                            "profit_margin": net_analysis.get('profit_margin', 0),
                            "base_position_size": position_size,
                            "final_position_size": final_position_size,
                            "microstructure_multiplier": microstructure_multiplier,
                            "microstructure_available": microstructure_info.get('available', False),
                            "microstructure_signal": microstructure_info.get('micro_signal'),
                            "current_spot_price": signal["current_spot_price"],
                            "current_futures_price": signal["current_futures_price"]
                        }
                        # 移除信号事件发布 - 现在由策略计算引擎统一管理
                        pass
                    except Exception as redis_e:
                        self.logger.debug(f"信号事件处理完成")
                
                # 额外的微观结构增强日志
                if microstructure_multiplier != 1.0:
                    enhancement_type = "增强" if microstructure_multiplier > 1.0 else "减弱"
                    self.logger.info(
                        f"🧠 {pair_name} 微观结构{enhancement_type}: 仓位乘数 {microstructure_multiplier:.2f}x "
                        f"({position_size:.4f} → {final_position_size:.4f})"
                    )
                
                # 获取该交易对的策略状态摘要
                if pair_strategy:
                    market_summary = pair_strategy.get_market_summary()
                    self.logger.info(f"📈 {pair_name} 策略状态: {market_summary}")
                
                await self._execute_entry(signal)
            else:
                # 定期记录多交易对策略状态（改为DEBUG级别）
                if self.price_log_counter % 100 == 0:
                    strategy_summaries = []
                    for pair_name, pair_status in self.strategy_manager.pair_strategies.items():
                        if pair_status.enabled:
                            summary = pair_status.strategy.get_market_summary()
                            strategy_summaries.append(f"{pair_name}: {summary}")
                    
                    if strategy_summaries:
                        self.logger.debug(f"🔍 多交易对策略监控: {'; '.join(strategy_summaries)}")
        
        except Exception as e:
            # 记录错误指标
            # 错误统计通过结构化日志记录
            
            self.logger.error(f"检查入场信号失败: {e}")
    
    async def _check_exit_signals(self):
        """检查出场信号（事件驱动版本）"""
        if not self.position_manager.in_position:
            return
        
        try:
            position_info = self.position_manager.get_position_info()
            position_type = position_info["position_type"]
            
            # 🔥 架构优化：检查多交易对策略出场信号
            # 检查所有活跃持仓的交易对出场信号
            exit_pairs = self.strategy_manager.check_all_exit_signals()
            strategy_exit = len(exit_pairs) > 0
            
            if strategy_exit:
                self.logger.info(f"🚨 检测到策略出场信号: {', '.join(exit_pairs)}")
                await self._execute_exit(f"策略信号-{exit_pairs[0]}")  # 使用第一个信号的交易对
                return
            
            # 风控检查由handle_position_update实时处理
        
        except Exception as e:
            # 记录错误指标
            # 错误统计通过结构化日志记录
            
            self.logger.error(f"检查出场信号失败: {e}")
    
    async def _execute_entry(self, signal: Dict[str, Any]):
        """执行入场交易"""
        try:
            # 提取trace_id并设置追踪上下文
            trace_id = signal.get("trace_id")
            if trace_id:
                trace_manager.set_trace_id(trace_id)
            
            # 设置开仓锁定标志
            self.is_opening_position = True
            
            signal_type = signal.get("signal_type")
            risk_grade = signal.get("risk_grade", "A")
            confidence = signal.get("confidence", 1.0)
            
            # 结构化日志记录交易开始
            self.logger.info_structured(
                "开始执行分级入场交易",
                event_type="trade_entry_start",
                details={
                    "signal_type": signal_type,
                    "risk_grade": risk_grade,
                    "confidence": confidence
                },
                trace_id=trace_id
            )
            
            # 确定仓位类型 - 简化为单一套利类型
            if signal_type == "short_futures_long_spot":
                position_type = PositionType.SHORT_FUTURES_LONG_SPOT
            else:
                self.logger.error(f"未知信号类型: {signal_type}")
                return
            
            # 从信号中获取价格信息
            current_spot_price = signal.get("current_spot_price")
            current_futures_price = signal.get("current_futures_price")

            if not current_spot_price or not current_futures_price:
                self.logger.error("信号中缺少价格信息，无法创建仓位")
                return

            # 创建仓位
            position_id = self.position_manager.create_position(
                position_type, current_spot_price, current_futures_price
            )
            
            # 使用通用方法执行交易
            trade_result = await self._execute_arbitrage_trade(
                position_type=signal_type,
                direction="entry",
                signal=signal
            )
            
            if trade_result["success"]:
                # 阶段三：实时差额对冲
                await self._stage_three_hedge_imbalance(
                    trade_result["spot_result"], 
                    trade_result["futures_result"], 
                    position_id, 
                    signal_type, 
                    signal
                )
            else:
                # 记录交易失败指标
                # 交易记录通过结构化日志记录
                # 错误统计通过结构化日志记录
                
                self.logger.error(f"交易执行失败: {trade_result.get('error', '未知错误')}")
                if self.position_manager.current_position:
                    self.position_manager.set_position_error(trade_result.get('error', '交易执行失败'))
            
        except Exception as e:
            self.logger.error(f"执行入场交易失败: {e}")
            if self.position_manager.current_position:
                self.position_manager.set_position_error(str(e))
        finally:
            # 无论成功、失败或异常，都要释放开仓锁定
            self.is_opening_position = False
            self.logger.info("🔓 释放开仓锁定")
    
    async def _stage_three_hedge_imbalance(self, spot_result: Dict, futures_result: Dict, 
                                          position_id: str, signal_type: str, signal: Dict[str, Any]):
        """阶段三：实时差额对冲逻辑"""
        try:
            self.logger.info("🔄 阶段三: 分析成交差额并执行对冲")
            
            # 检查执行结果格式
            spot_success = spot_result.get('success', False) if isinstance(spot_result, dict) else False
            futures_success = futures_result.get('success', False) if isinstance(futures_result, dict) else False
            
            if not spot_success and not futures_success:
                error_msg = "现货和期货订单均执行失败"
                self.logger.error(error_msg)
                self.position_manager.set_position_error(error_msg)
                return
            
            # 获取实际成交量
            spot_filled = spot_result.get('filled_size', 0.0) if spot_success else 0.0
            futures_filled = futures_result.get('filled_size', 0.0) if futures_success else 0.0
            
            # 转换期货成交量为等价现货数量（基于合约面值）
            futures_contract_value = self.connector.get_contract_value(self.config.FUTURES_ID)
            futures_equivalent = futures_filled * futures_contract_value
            
            # 计算差额（imbalance）
            imbalance = spot_filled - futures_equivalent
            hedge_threshold = self.config.ORDER_EXECUTION["SMART_EXECUTION"]["MIN_HEDGE_THRESHOLD"]
            
            self.logger.info(
                f"📊 成交分析: 现货={spot_filled:.6f}, 期货={futures_filled:.6f} "
                f"(等价现货={futures_equivalent:.6f}), 差额={imbalance:.6f}"
            )
            
            # 检查是否需要对冲
            if abs(imbalance) < hedge_threshold:
                self.logger.info("✅ 成交差额在可接受范围内，无需对冲")
                await self._finalize_successful_entry(spot_result, futures_result, position_id, signal_type, signal)
                return
            
            # 执行对冲
            self.logger.warning(f"⚠️  检测到成交差额 {imbalance:.6f}，开始执行对冲")
            await self._execute_hedge_orders(imbalance, spot_filled, futures_filled)
            
            # 最终确认成功
            await self._finalize_successful_entry(spot_result, futures_result, position_id, signal_type, signal)
            
        except Exception as e:
            self.logger.error(f"差额对冲执行失败: {e}")
            self.position_manager.set_position_error(f"对冲失败: {str(e)}")
    
    async def _execute_hedge_orders(self, imbalance: float, spot_filled: float, futures_filled: float):
        """执行对冲订单"""
        try:
            max_attempts = self.config.ORDER_EXECUTION["SMART_EXECUTION"]["MAX_HEDGE_ATTEMPTS"]
            
            for attempt in range(max_attempts):
                self.logger.info(f"🔄 执行对冲尝试 {attempt + 1}/{max_attempts}")
                
                if imbalance > 0:
                    # 现货多了，需要卖出现货或买入期货
                    # 优先买入期货（成本更低）
                    futures_needed = imbalance / self.connector.get_contract_value(self.config.FUTURES_ID)
                    
                    hedge_result = await self.connector.place_market_hedge_order(
                        inst_id=self.config.FUTURES_ID,
                        side="buy",
                        size=futures_needed
                    )
                    
                else:
                    # 期货多了，需要卖出期货或买入现货
                    # 优先卖出期货
                    futures_excess = abs(imbalance) / self.connector.get_contract_value(self.config.FUTURES_ID)
                    
                    hedge_result = await self.connector.place_market_hedge_order(
                        inst_id=self.config.FUTURES_ID,
                        side="sell",
                        size=futures_excess
                    )
                
                if hedge_result.get('code') == '0':
                    self.logger.info(f"✅ 对冲订单执行成功")
                    break
                else:
                    self.logger.warning(f"⚠️  对冲尝试 {attempt + 1} 失败: {hedge_result.get('msg')}")
                    if attempt < max_attempts - 1:
                        await asyncio.sleep(0.1)  # 短暂等待后重试
            
        except Exception as e:
            self.logger.error(f"执行对冲订单异常: {e}")
            raise
    
    async def _finalize_successful_entry(self, spot_result: Dict, futures_result: Dict, 
                                        position_id: str, signal_type: str, signal: Dict[str, Any]):
        """完成成功入场的最终处理"""
        try:
            # 提取订单信息（适配智能执行结果格式）
            spot_order_ids = spot_result.get('order_ids', [])
            futures_order_ids = futures_result.get('order_ids', [])
            
            spot_order = OrderInfo(
                order_id=spot_order_ids[0] if spot_order_ids else "unknown",
                inst_id=self.config.SPOT_ID,
                side=spot_result.get('side', 'unknown'),
                size=str(spot_result.get('filled_size', 0))
            )
            
            futures_order = OrderInfo(
                order_id=futures_order_ids[0] if futures_order_ids else "unknown", 
                inst_id=self.config.FUTURES_ID,
                side=futures_result.get('side', 'unknown'),
                size=str(futures_result.get('filled_size', 0))
            )
            
            self.position_manager.update_position_orders(spot_order, futures_order)
            
            # 注意：confirm_position_open()已经在_handle_entry_order_filled_real_time()中
            # 基于真实订单成交事件调用，这里不再重复调用
            
            # 🔥 架构优化：设置多交易对策略入场状态
            if 'pair_name' in signal:
                pair_name = signal['pair_name']
                spot_price = signal.get('current_spot_price')
                if spot_price:
                    self.strategy_manager.set_position_entry(pair_name, signal_type, spot_price)
                else:
                    self.logger.warning(f"信号缺少现货价格信息，无法设置策略状态: {pair_name}")
            else:
                # 兼容性处理
                self.logger.warning("信号缺少交易对信息，无法设置策略状态")
            
            # 仓位状态已由PositionManager自动保存
            
            # 记录执行阶段统计
            self._log_execution_stages(spot_result, futures_result)
            
            self.logger.info(f"🎉 入场交易完全执行成功 - 仓位ID: {position_id}")
            
        except Exception as e:
            self.logger.error(f"完成入场处理失败: {e}")
            raise
    
    def _log_execution_stages(self, spot_result: Dict, futures_result: Dict):
        """记录执行阶段统计信息"""
        try:
            spot_stages = spot_result.get('execution_stages', [])
            futures_stages = futures_result.get('execution_stages', [])
            
            self.logger.debug("📈 执行阶段统计:")
            
            # 现货执行阶段
            self.logger.debug(f"   现货 ({self.config.SPOT_ID}):")
            for stage in spot_stages:
                stage_name = stage.get('stage', 'unknown')
                success = "✅" if stage.get('success') else "❌"
                self.logger.debug(f"     {stage_name}: {success}")
            
            # 期货执行阶段  
            self.logger.debug(f"   期货 ({self.config.FUTURES_ID}):")
            for stage in futures_stages:
                stage_name = stage.get('stage', 'unknown')
                success = "✅" if stage.get('success') else "❌"
                self.logger.debug(f"     {stage_name}: {success}")
                
        except Exception as e:
            # 执行阶段统计记录失败（非关键，已有Prometheus监控）
            pass
    
    async def _execute_arbitrage_trade(self, 
                                      position_type: str, 
                                      direction: str,
                                      signal: Dict[str, Any] = None,
                                      reason: str = None) -> Dict[str, Any]:
        """
        通用的套利交易执行方法
        
        Args:
            position_type: 仓位类型 ('short_futures_long_spot')
            direction: 交易方向 ('entry' | 'exit')
            signal: 入场信号字典（仅对entry有效）
            reason: 出场原因（仅对exit有效）
            
        Returns:
            Dict: 包含交易结果的字典
        """
        try:
            # 获取当前追踪ID（如果有的话）
            trace_id = trace_manager.get_trace_id()
            order_start_time = time.time()
            
            self.logger.info_structured(
                f"开始执行{direction}交易",
                event_type=f"trade_{direction}_execute",
                details={
                    "position_type": position_type,
                    "reason": reason
                },
                trace_id=trace_id
            )
            
            # 确定交易方向
            spot_side, futures_side = self._determine_trade_sides(position_type, direction)
            
            # 计算仓位大小
            if direction == "entry" and signal:
                position_size = self._calculate_graded_position_size(signal)
            else:
                position_size = self._calculate_position_size()
            
            futures_contracts = self._calculate_futures_contracts(position_size)
            
            # 准备订单参数
            spot_params = {
                "inst_id": self.config.SPOT_ID,
                "trade_mode": "cash",
                "side": spot_side,
                "ord_type": "market",
                "sz": str(position_size)
            }
            
            futures_params = {
                "inst_id": self.config.FUTURES_ID,
                "trade_mode": "cross",
                "side": futures_side,
                "ord_type": "market", 
                "sz": str(futures_contracts)
            }
            
            self.logger.debug(
                f"订单参数: 现货({spot_side})={position_size}, 期货({futures_side})={futures_contracts:.6f}"
            )
            
            # 并发执行三阶段智能套利订单
            action_name = "开仓" if direction == "entry" else "平仓"
            self.logger.info(f"🚀 执行三阶段智能{action_name}订单对")
            
            spot_result, futures_result = await self.connector.place_arbitrage_orders(
                spot_params, futures_params
            )
            
            # 计算订单延迟并记录KPI
            order_end_time = time.time()
            order_latency = KPICalculator.order_latency_ms(order_start_time, order_end_time)
            
            # 记录订单延迟指标
            # 订单延迟统计通过结构化日志记录
            
            # 记录订单执行结果
            spot_success = spot_result and spot_result.get("success", False)
            futures_success = futures_result and futures_result.get("success", False)
            
            if spot_success and futures_success:
                # 计算滑点
                spot_slippage = 0.0
                futures_slippage = 0.0
                
                if spot_result.get("avg_price") and spot_result.get("filled_size"):
                    # 市价单的滑点基于市场价格计算
                    spot_slippage = 0.0  # 市价单滑点难以准确计算
                
                if futures_result.get("avg_price") and futures_result.get("filled_size"):
                    futures_slippage = 0.0  # 市价单滑点难以准确计算
                
                self.logger.info_structured(
                    f"订单执行成功 - {action_name}",
                    event_type="orders_executed",
                    metrics={
                        "order_latency_ms": order_latency,
                        "spot_slippage_bps": spot_slippage,
                        "futures_slippage_bps": futures_slippage,
                        "spot_filled_size": spot_result.get("filled_size", 0),
                        "futures_filled_size": futures_result.get("filled_size", 0),
                        "spot_avg_price": spot_result.get("avg_price", 0),
                        "futures_avg_price": futures_result.get("avg_price", 0)
                    },
                    details={
                        "spot_side": spot_side,
                        "futures_side": futures_side,
                        "position_size": position_size,
                        "futures_contracts": futures_contracts
                    },
                    trace_id=trace_id
                )
            else:
                self.logger.error_structured(
                    f"订单执行失败 - {action_name}",
                    event_type="orders_failed",
                    metrics={
                        "order_latency_ms": order_latency
                    },
                    details={
                        "spot_success": spot_success,
                        "futures_success": futures_success,
                        "spot_error": spot_result.get("error") if spot_result else "No result",
                        "futures_error": futures_result.get("error") if futures_result else "No result"
                    },
                    trace_id=trace_id
                )
            
            return {
                "success": True,
                "spot_result": spot_result,
                "futures_result": futures_result,
                "position_size": position_size,
                "futures_contracts": futures_contracts,
                "spot_side": spot_side,
                "futures_side": futures_side,
                "order_latency_ms": order_latency
            }
            
        except Exception as e:
            self.logger.error(f"执行{direction}交易失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "spot_result": None,
                "futures_result": None
            }
    
    def _determine_trade_sides(self, position_type: str, direction: str) -> Tuple[str, str]:
        """
        根据仓位类型和交易方向确定现货和期货的买卖方向
        
        Args:
            position_type: 仓位类型 ('short_futures_long_spot')
            direction: 交易方向 ('entry' | 'exit')

        Returns:
            Tuple[str, str]: (现货方向, 期货方向)
        """
        if position_type == "short_futures_long_spot":
            if direction == "entry":
                return "buy", "sell"    # 基差套利：买现货，卖期货
            else:  # exit
                return "sell", "buy"    # 平仓套利：卖现货，买期货
        else:
            raise ValueError(f"未知的仓位类型: {position_type}")

    async def _execute_exit(self, reason: str):
        """执行出场交易"""
        try:
            self.logger.info(f"开始执行出场交易，原因: {reason}")
            
            if not self.position_manager.in_position:
                return
            
            position_info = self.position_manager.get_position_info()
            position_type = position_info["position_type"]
            
            # 开始平仓流程
            self.position_manager.start_position_close()
            
            # 使用通用方法执行交易
            trade_result = await self._execute_arbitrage_trade(
                position_type=position_type,
                direction="exit",
                reason=reason
            )
            
            if trade_result["success"]:
                # 阶段三：平仓差额对冲
                await self._stage_three_exit_hedge(
                    trade_result["spot_result"], 
                    trade_result["futures_result"], 
                    reason
                )
            else:
                # 记录平仓失败指标
                # 交易记录通过结构化日志记录
                # 错误统计通过结构化日志记录
                
                self.logger.error(f"平仓交易执行失败: {trade_result.get('error', '未知错误')}")
            
        except Exception as e:
            self.logger.error(f"执行出场交易失败: {e}")
    
    async def _stage_three_exit_hedge(self, spot_result: Dict, futures_result: Dict, reason: str):
        """平仓阶段三：差额对冲逻辑"""
        try:
            self.logger.info("🔄 平仓阶段三: 分析平仓差额并执行对冲")
            
            # 检查执行结果
            spot_success = spot_result.get('success', False) if isinstance(spot_result, dict) else False
            futures_success = futures_result.get('success', False) if isinstance(futures_result, dict) else False
            
            if not spot_success and not futures_success:
                error_msg = f"平仓失败 - 现货和期货订单均执行失败"
                self.logger.error(error_msg)
                self.position_manager.set_position_error(error_msg)
                return
            
            # 获取实际平仓量
            spot_filled = spot_result.get('filled_size', 0.0) if spot_success else 0.0
            futures_filled = futures_result.get('filled_size', 0.0) if futures_success else 0.0
            
            # 转换期货平仓量为等价现货数量
            futures_contract_value = self.connector.get_contract_value(self.config.FUTURES_ID)
            futures_equivalent = futures_filled * futures_contract_value
            
            # 计算平仓差额
            imbalance = spot_filled - futures_equivalent
            hedge_threshold = self.config.ORDER_EXECUTION["SMART_EXECUTION"]["MIN_HEDGE_THRESHOLD"]
            
            self.logger.info(
                f"📊 平仓分析: 现货={spot_filled:.6f}, 期货={futures_filled:.6f} "
                f"(等价现货={futures_equivalent:.6f}), 差额={imbalance:.6f}"
            )
            
            # 检查是否需要对冲
            if abs(imbalance) >= hedge_threshold:
                self.logger.warning(f"⚠️  检测到平仓差额 {imbalance:.6f}，开始执行对冲")
                await self._execute_hedge_orders(imbalance, spot_filled, futures_filled)
            else:
                self.logger.info("✅ 平仓差额在可接受范围内，无需对冲")
            
            # 完成平仓处理
            await self._finalize_successful_exit(spot_result, futures_result, reason)
            
        except Exception as e:
            self.logger.error(f"平仓差额对冲执行失败: {e}")
            self.position_manager.set_position_error(f"平仓对冲失败: {str(e)}")
    
    async def _finalize_successful_exit(self, spot_result: Dict, futures_result: Dict, reason: str):
        """完成成功平仓的最终处理"""
        try:
            # 计算已实现盈亏（简化计算）
            realized_pnl = self.position_manager.current_position.unrealized_pnl
            
            # 确认平仓完成
            self.position_manager.confirm_position_close(realized_pnl)
            
            # 🔥 架构优化：清除多交易对策略入场状态
            current_pos = self.position_manager.current_position
            if current_pos and hasattr(current_pos, 'pair_name'):
                self.strategy_manager.clear_position_entry(current_pos.pair_name)
            else:
                # 兼容性处理：清理所有交易对状态
                for pair_name in self.strategy_manager.pair_strategies.keys():
                    self.strategy_manager.clear_position_entry(pair_name)
            
            # 空仓状态已由PositionManager自动保存
            
            # 记录平仓执行阶段统计
            self._log_execution_stages(spot_result, futures_result)
            
            self.logger.info(f"🎉 出场交易完全执行成功 - 原因: {reason}, 盈亏: {realized_pnl:.4f}")
            
        except Exception as e:
            self.logger.error(f"完成平仓处理失败: {e}")
            raise
    
    async def _emergency_close_position(self, reason: str):
        """紧急平仓"""
        self.logger.critical(f"执行紧急平仓: {reason}")
        await self._execute_exit(f"紧急平仓: {reason}")
    
    
    def _calculate_futures_contracts(self, spot_position_size: float) -> float:
        """计算期货合约数量（基于现货仓位大小）"""
        try:
            futures_contract_value = self.connector.get_contract_value(self.config.FUTURES_ID)
            futures_lot_size = self.connector.get_lot_size(self.config.FUTURES_ID)
            futures_min_size = self.connector.get_min_size(self.config.FUTURES_ID)
            
            # 基于合约面值计算合约数量
            futures_contracts = spot_position_size / futures_contract_value
            
            # 确保满足最小要求
            futures_contracts = max(futures_contracts, futures_min_size)
            
            # 调整为最小交易单位的整数倍
            futures_contracts = (futures_contracts // futures_lot_size) * futures_lot_size
            
            # 最终验证
            if futures_contracts < futures_min_size:
                futures_contracts = futures_min_size
            
            return futures_contracts
            
        except Exception as e:
            self.logger.error(f"计算期货合约数量失败: {e}")
            return 1.0  # 默认最小值
    
    def _calculate_position_size(self, spot_price: float = None, spot_symbol: str = None) -> float:
        """计算仓位大小（使用动态规格信息）"""
        if not spot_price:
            return 100.0  # 默认值

        try:
            # 使用传入的现货符号或默认配置
            spot_id = spot_symbol or self.config.SPOT_ID

            # 获取现货最小交易单位
            spot_lot_size = self.connector.get_lot_size(spot_id)
            spot_min_size = self.connector.get_min_size(spot_id)

            # 计算基础仓位大小
            from config import calculate_position_size
            base_position_size = calculate_position_size(
                spot_price,
                risk_ratio=0.5
            )
            
            # 确保仓位大小符合交易规格
            min_required = max(spot_lot_size, spot_min_size)
            position_size = max(base_position_size, min_required)
            
            # 调整为最小交易单位的整数倍
            position_size = (position_size // spot_lot_size) * spot_lot_size
            
            # 验证最终结果
            if position_size < spot_min_size:
                position_size = spot_min_size
            
            # 仓位大小计算详情（现已由Prometheus指标监控）
            pass
            
            return position_size
            
        except Exception as e:
            self.logger.error(f"计算仓位大小失败: {e}")
            return 100.0
    
    def _calculate_graded_position_size(self, signal: Dict[str, Any]) -> float:
        """计算分级风险仓位大小（含微观结构增强）"""
        spot_price = signal.get("current_spot_price")
        if not spot_price:
            return 100.0  # 默认值
        
        try:
            # 优先使用已经计算好的微观结构增强仓位大小
            final_position_size = signal.get('final_position_size')
            if final_position_size is not None:
                # 微观结构增强的仓位大小（现已由Prometheus微观结构置信度指标监控）
                pass
                return final_position_size
            
            # 回退到传统分级风险计算（如果没有微观结构数据）
            base_position_size = self._calculate_position_size()
            
            # 获取风险评级和置信度
            risk_grade = signal.get("risk_grade", "A")
            confidence = signal.get("confidence", 1.0)
            
            # 获取分级配置
            graded_config = self.config.RISK_MANAGEMENT.get("GRADED_POSITION_SYSTEM", {})
            suboptimal_ratio = graded_config.get("SUBOPTIMAL_TRADE_SIZE_RATIO", 0.5)
            min_position_ratio = graded_config.get("MIN_POSITION_RATIO", 0.2)
            
            # 根据风险评级调整仓位
            if risk_grade == "A":
                # A级信号：使用满仓位，但根据置信度微调
                size_multiplier = confidence
            elif risk_grade == "B":
                # B级信号：使用次优仓位比例
                size_multiplier = suboptimal_ratio * confidence
            else:
                # 未知评级：使用最小仓位
                size_multiplier = min_position_ratio
            
            # 确保不低于最小仓位比例
            size_multiplier = max(size_multiplier, min_position_ratio)
            
            # 计算最终仓位大小
            adjusted_position_size = base_position_size * size_multiplier
            
            # 确保符合交易规格
            spot_lot_size = self.connector.get_lot_size(self.config.SPOT_ID)
            spot_min_size = self.connector.get_min_size(self.config.SPOT_ID)
            
            # 调整为最小交易单位的整数倍
            adjusted_position_size = (adjusted_position_size // spot_lot_size) * spot_lot_size
            
            # 验证最终结果
            if adjusted_position_size < spot_min_size:
                adjusted_position_size = spot_min_size
            
            # 分级仓位计算详情（现已由Prometheus指标监控仓位大小变化）
            pass
            
            return adjusted_position_size
            
        except Exception as e:
            self.logger.error(f"计算分级仓位大小失败: {e}")
            return self._calculate_position_size()  # 回退到基础计算
    
    # ======= 策略计算主循环 - 驱动系统心跳 =======
    
    async def _strategy_calculation_loop(self):
        """策略计算主循环 - 驱动策略模块进行持续计算并发布数据到Redis"""
        self.logger.info("🔄 启动策略计算主循环...")
        
        # 循环间隔配置（秒）
        calculation_interval = getattr(self.config, 'STRATEGY_CALCULATION_INTERVAL', 1.0)
        
        try:
            while self.is_running:
                loop_start_time = time.time()
                self.loop_counter += 1
                
                try:
                    # 1. 准备系统上下文信息
                    system_context = {
                        "start_time": self.start_time,
                        "loop_counter": self.loop_counter,
                        "is_running": self.is_running
                    }
                    
                    # 2. 获取策略计算的完整数据包（唯一数据源）
                    complete_data = self.strategy_manager.get_all_strategy_data(system_context)
                    
                    # 3. 通过数据分发器分发到所有消费者
                    if self.data_dispatcher:
                        dispatch_result = await self.data_dispatcher.dispatch_strategy_data(complete_data)
                        
                        # 只在分发出现错误时记录
                        if dispatch_result.errors:
                            for error in dispatch_result.errors:
                                self.logger.debug(f"数据分发错误: {error}")
                    else:
                        # 降级处理：如果分发器不可用，直接记录心跳
                        if complete_data.get("heartbeat", {}).get("should_log", False):
                            message = complete_data["heartbeat"]["message"]
                            self.logger.info(message)
                    
                    # 4. 性能监控（使用策略数据中的loop_counter）
                    loop_duration = time.time() - loop_start_time
                    if loop_duration > 0.5:  # 如果循环执行超过500ms则记录警告
                        loop_count = complete_data.get("loop_count", self.loop_counter)
                        self.logger.warning(
                            f"策略计算循环执行较慢: {loop_duration:.3f}s, 循环#{loop_count}"
                        )
                    
                except Exception as calc_e:
                    self.logger.error(f"策略计算循环内部错误: {calc_e}")
                    # 继续运行，不退出循环
                
                # 5. 休眠到下一个计算周期
                await asyncio.sleep(calculation_interval)
                
        except asyncio.CancelledError:
            self.logger.info("策略计算主循环被取消")
            raise
        except Exception as e:
            self.logger.error(f"策略计算主循环异常: {e}")
            raise
        finally:
            self.logger.info("策略计算主循环已停止")
    
    # ======= 原有轮询任务已移除，由事件驱动替代 =======
    
    async def _perform_health_check(self):
        """执行健康检查"""
        try:
            # 检查连接器健康状态
            connector_healthy = await self.connector.health_check()
            
            # 检查数据流新鲜度
            data_fresh = True
            if self.last_price_update_time:
                time_since_update = datetime.now() - self.last_price_update_time
                timeout = timedelta(seconds=self.config.MONITORING["STALE_DATA_TIMEOUT_SECONDS"])
                data_fresh = time_since_update < timeout
            
            # 检查紧急停止状态
            if self.emergency_stop:
                self.logger.critical("检测到紧急停止状态，准备关闭系统")
                await self.stop()
                return
            
            # 记录健康状态
            if not connector_healthy:
                self.logger.warning("连接器健康检查失败")
            
            if not data_fresh:
                self.logger.warning("价格数据流已过时")

            # 定期报告市场健康度状态
            market_health = self.get_market_health_summary()
            if market_health.get("market_health_score", 100) < 80:
                self.logger.warning_structured(
                    f"市场健康度评分: {market_health.get('market_health_score', 100):.1f} - {market_health.get('health_status', '未知')}",
                    event_type="market_health_report",
                    metrics=market_health.get("recent_anomalies_1h", {}),
                    details=market_health
                )

                # 发布市场健康度警报到Redis
                if self.redis_publisher:
                    try:
                        await self.redis_publisher.publish_alert(
                            "WARNING",
                            f"市场健康度评分低: {market_health.get('market_health_score', 100):.1f}",
                            {"market_health": market_health}
                        )
                    except Exception as redis_e:
                        self.logger.debug(f"发布市场健康度警报到Redis失败: {redis_e}")

            # 移除系统状态发布 - 系统状态现在由策略计算引擎统一管理
            # 健康检查结果将通过策略数据包发布

            # 发布连接器健康状态警报
            if not connector_healthy and self.redis_publisher:
                try:
                    await self.redis_publisher.publish_alert(
                        "ERROR",
                        "连接器健康检查失败",
                        {"component": "okx_connector", "healthy": connector_healthy}
                    )
                except Exception as redis_e:
                    self.logger.debug(f"发布连接器警报到Redis失败: {redis_e}")

            # 发布数据新鲜度警报
            if not data_fresh and self.redis_publisher:
                try:
                    time_since_update = datetime.now() - self.last_price_update_time if self.last_price_update_time else timedelta(0)
                    await self.redis_publisher.publish_alert(
                        "WARNING",
                        f"价格数据流已过时 ({time_since_update.total_seconds():.0f}秒)",
                        {"component": "price_data", "last_update": self.last_price_update_time.isoformat() if self.last_price_update_time else None}
                    )
                except Exception as redis_e:
                    self.logger.debug(f"发布数据新鲜度警报到Redis失败: {redis_e}")

        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
    
    # 统计报告由定时检查处理，不再使用独立轮询任务
    
    def _log_statistics(self):
        """记录统计信息"""
        try:
            # 性能统计
            avg_processing_time = 0
            if self.processing_times:
                avg_processing_time = sum(self.processing_times) / len(self.processing_times)
            
            # 🔥 架构优化：获取多交易对策略和仓位统计
            strategy_state = self.strategy_manager.get_all_status()
            position_stats = self.position_manager.get_statistics()
            
            # 运行时长
            runtime = datetime.now() - self.start_time if self.start_time else timedelta(0)
            
            self.logger.info(
                f"=== 机器人状态报告 ===\n"
                f"运行时长: {runtime}\n"
                f"消息处理数: {self.message_count}\n"
                f"平均处理延迟: {avg_processing_time:.2f}ms\n"
                f"错误计数: {self.error_count}\n"
                f"当前价格: {[(k, f'{v:.4f}') for k, v in list(self.spot_prices.items())[:3]]}\n"
                f"多交易对策略状态: {len(strategy_state['pair_statuses'])}个交易对活跃\n"
                f"仓位统计: {position_stats}\n"
                f"===================="
            )
        
        except Exception as e:
            self.logger.error(f"记录统计信息失败: {e}")
    
    def _log_final_statistics(self):
        """记录最终统计信息"""
        try:
            position_stats = self.position_manager.get_statistics()
            runtime = datetime.now() - self.start_time if self.start_time else timedelta(0)
            
            self.logger.info(
                f"=== 最终统计报告 ===\n"
                f"总运行时长: {runtime}\n"
                f"总处理消息数: {self.message_count}\n"
                f"总交易次数: {position_stats['total_trades']}\n"
                f"胜率: {position_stats['win_rate']:.2%}\n"
                f"总盈亏: {position_stats['total_pnl']:.4f}\n"
                f"平均每笔盈亏: {position_stats['avg_pnl_per_trade']:.4f}\n"
                f"总错误数: {self.error_count}\n"
                f"==================="
            )
        
        except Exception as e:
            self.logger.error(f"记录最终统计失败: {e}")
    
    def get_realtime_state(self) -> Dict[str, Any]:
        """获取实时状态概览"""
        # 获取第一个交易对的价格作为示例（兼容性）
        first_pair_prices = {}
        if self.strategy_manager.pair_strategies:
            first_pair = next(iter(self.strategy_manager.pair_strategies.values()))
            spot_symbol = first_pair.spot_symbol
            futures_symbol = first_pair.futures_symbol
            spot_price = self.spot_prices.get(spot_symbol)
            futures_price = self.futures_prices.get(futures_symbol)

            first_pair_prices = {
                "spot_price": spot_price,
                "futures_price": futures_price,
                "spot_symbol": spot_symbol,
                "futures_symbol": futures_symbol,
                "basis": (futures_price - spot_price) / spot_price if spot_price and futures_price else None
            }

        return {
            "prices": first_pair_prices,
            "funding": {
                "rate": self.futures_funding_rate,
                "last_update": self.last_funding_update_time.isoformat() if self.last_funding_update_time else None
            },
            "positions": {
                "count": len(self.current_positions),
                "last_update": self.last_position_update_time.isoformat() if self.last_position_update_time else None,
                "position_manager_status": "in_position" if self.position_manager.in_position else "no_position"
            },
            "orders": {
                "open_count": len(self.open_orders),
                "last_update": self.last_order_update_time.isoformat() if self.last_order_update_time else None
            },
            "account": {
                "balances_count": len(self.account_balance),
                "last_update": self.last_balance_update_time.isoformat() if self.last_balance_update_time else None
            },
            "performance": {
                "message_count": self.message_count,
                "error_count": self.error_count,
                "avg_processing_time_ms": sum(self.processing_times) / len(self.processing_times) if self.processing_times else 0,
                "is_opening_position": self.is_opening_position
            },
            "running_status": {
                "is_running": self.is_running,
                "start_time": self.start_time.isoformat() if self.start_time else None,
                "emergency_stop": self.emergency_stop
            }
        }

    async def _perform_data_preheating(self):
        """
        执行策略数据预热 - 获取历史数据填充战略层
        """
        try:
            self.logger.info("🔥 开始策略数据预热...")

            # 获取启用的交易对列表
            enabled_pairs = get_enabled_trading_pairs()
            if not enabled_pairs:
                self.logger.warning("⚠️ 未找到启用的交易对，跳过数据预热")
                return

            # 构建需要预热的交易对列表（现货和期货）
            trading_pairs_to_preheat = []
            
            for pair_config in enabled_pairs:
                pair_name = pair_config["name"]
                spot_id = pair_config["spot_id"]
                futures_id = pair_config["futures_id"]
                
                trading_pairs_to_preheat.append({
                    "name": pair_name,
                    "spot_id": spot_id,
                    "futures_id": futures_id
                })

            if not trading_pairs_to_preheat:
                self.logger.warning("⚠️ 未找到有效的交易对，跳过数据预热")
                return

            self.logger.info(f"📊 准备预热交易对: {[pair['name'] for pair in trading_pairs_to_preheat]}")

            # 对每个交易对执行数据预热
            total_preheated = 0
            total_failed = 0

            for pair_info in trading_pairs_to_preheat:
                pair_name = pair_info["name"]
                spot_id = pair_info["spot_id"]
                futures_id = pair_info["futures_id"]
                
                try:
                    # 获取该交易对的策略实例
                    strategy = self.strategy_manager.get_strategy(pair_name)
                    if not strategy:
                        self.logger.warning(f"⚠️ 未找到 {pair_name} 的策略实例，跳过预热")
                        total_failed += 1
                        continue

                    # 🔥 关键修复：同时传递现货和期货ID
                    self.logger.info(f"📡 开始预热 {pair_name}: 现货={spot_id}, 期货={futures_id}")
                    
                    # 执行单个交易对的数据预热，传递现货和期货ID
                    preheat_result = await strategy.preheat_strategy_data(
                        self.connector,
                        spot_id,
                        futures_id
                    )

                    if preheat_result['success']:
                        strategic_points = preheat_result.get('strategic_data_points', 0)
                        strategic_ready = preheat_result.get('strategic_ready', False)

                        self.logger.info(f"✅ {pair_name} 预热成功: {strategic_points} 个战略层数据点, "
                                       f"战略层就绪: {'是' if strategic_ready else '否'}")
                        total_preheated += 1

                        # 移除系统状态发布 - 预热状态现在由策略计算引擎统一管理
                        # 预热结果将通过策略数据包发布
                        if self.redis_publisher:
                            try:
                                pass  # 移除发布调用
                            except Exception as redis_e:
                                self.logger.debug(f"发布预热事件到Redis失败: {redis_e}")
                    else:
                        error_msg = preheat_result.get('error', 'Unknown error')
                        self.logger.warning(f"⚠️ {pair_name} 预热失败: {error_msg}")
                        total_failed += 1

                except Exception as e:
                    self.logger.error(f"❌ {pair_name} 预热异常: {e}")
                    total_failed += 1

            # 预热完成总结
            total_pairs = len(enabled_pairs)
            success_rate = (total_preheated / total_pairs * 100) if total_pairs > 0 else 0

            self.logger.info(f"🔥 数据预热完成: {total_preheated}/{total_pairs} 成功 "
                           f"({success_rate:.1f}%), {total_failed} 失败")

            if total_preheated > 0:
                self.logger.info("✅ 战略层数据预热成功，布林带计算已就绪")
                self.logger.info("⏱️ 战术层将在接收实时数据后逐步就绪（约需60秒）")
            else:
                self.logger.warning("⚠️ 所有交易对预热失败，策略将在实时数据积累后启动")

            # 移除系统状态发布 - 预热完成状态现在由策略计算引擎统一管理
            # 预热完成信息将通过策略数据包发布

        except Exception as e:
            self.logger.error(f"数据预热过程异常: {e}")
            # 预热失败不应该阻止系统启动，只记录错误

    def __repr__(self):
        status = "事件驱动运行中" if self.is_running else "已停止"
        position_status = "有仓位" if self.position_manager.in_position else "无仓位"
        return f"ArbitrageBot(status={status}, {position_status}, messages={self.message_count})"