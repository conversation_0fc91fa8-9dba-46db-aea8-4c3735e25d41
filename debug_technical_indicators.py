#!/usr/bin/env python3
"""
技术指标数据传输链路调试脚本

用于验证策略引擎→多交易对管理器→Redis发布→仪表盘接收的完整数据传输链路
"""

import asyncio
import json
import redis.asyncio as redis
from datetime import datetime
from typing import Dict, Any
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("debug_tech_indicators")

class TechnicalIndicatorDebugger:
    """技术指标数据传输链路调试器"""
    
    def __init__(self):
        self.redis_client = None
        self.received_messages = []
        self.technical_indicators_log = []
        
    async def connect_redis(self):
        """连接Redis"""
        try:
            self.redis_client = redis.Redis(
                host='localhost',
                port=6379,
                decode_responses=True
            )
            await self.redis_client.ping()
            logger.info("✅ Redis连接成功")
            return True
        except Exception as e:
            logger.error(f"❌ Redis连接失败: {e}")
            return False
    
    async def subscribe_and_monitor(self, duration_seconds: int = 60):
        """订阅Redis频道并监控技术指标数据"""
        if not self.redis_client:
            logger.error("Redis未连接")
            return
        
        logger.info(f"🔍 开始监控技术指标数据传输，持续 {duration_seconds} 秒...")
        
        # 订阅策略数据频道
        pubsub = self.redis_client.pubsub()
        await pubsub.subscribe('trading_bot:strategy')
        
        start_time = datetime.now()
        message_count = 0
        
        try:
            async for message in pubsub.listen():
                if message['type'] == 'message':
                    message_count += 1
                    await self._process_message(message, message_count)
                    
                    # 检查是否超时
                    elapsed = (datetime.now() - start_time).total_seconds()
                    if elapsed >= duration_seconds:
                        break
                        
        except KeyboardInterrupt:
            logger.info("用户中断监控")
        finally:
            await pubsub.unsubscribe('trading_bot:strategy')
            await pubsub.close()
            
        # 生成报告
        await self._generate_report()
    
    async def _process_message(self, message: Dict, message_count: int):
        """处理接收到的消息"""
        try:
            data = json.loads(message['data'])
            timestamp = data.get('timestamp', 'N/A')
            event_type = data.get('event_type', 'unknown')
            
            if event_type == 'strategy_update':
                payload = data.get('payload', {})
                pairs_data = payload.get('pairs', {})
                
                # 分析每个交易对的技术指标
                for pair_name, pair_data in pairs_data.items():
                    tech_indicators = pair_data.get('technical_indicators', {})
                    
                    if tech_indicators:
                        indicator_info = {
                            'message_count': message_count,
                            'timestamp': timestamp,
                            'pair_name': pair_name,
                            'atr': tech_indicators.get('atr'),
                            'adx': tech_indicators.get('adx'),
                            'indicators_ready': tech_indicators.get('indicators_ready', False),
                            'incremental_indicators': tech_indicators.get('incremental_indicators', {}),
                            'dashboard_info': tech_indicators.get('dashboard_info', {})
                        }
                        
                        self.technical_indicators_log.append(indicator_info)
                        
                        # 实时输出关键信息
                        if message_count % 5 == 0:  # 每5条消息输出一次
                            atr_str = f"{tech_indicators.get('atr'):.6f}" if tech_indicators.get('atr') else "None"
                            adx_str = f"{tech_indicators.get('adx'):.2f}" if tech_indicators.get('adx') else "None"
                            ready = tech_indicators.get('indicators_ready', False)
                            
                            logger.info(
                                f"📊 消息#{message_count} [{pair_name}]: "
                                f"ATR={atr_str}, ADX={adx_str}, 就绪={ready}"
                            )
                            
        except Exception as e:
            logger.error(f"处理消息失败: {e}")
    
    async def _generate_report(self):
        """生成调试报告"""
        logger.info("\n" + "="*60)
        logger.info("📋 技术指标数据传输链路调试报告")
        logger.info("="*60)
        
        if not self.technical_indicators_log:
            logger.warning("❌ 未接收到任何技术指标数据")
            return
        
        # 按交易对分组统计
        pair_stats = {}
        for entry in self.technical_indicators_log:
            pair_name = entry['pair_name']
            if pair_name not in pair_stats:
                pair_stats[pair_name] = {
                    'total_messages': 0,
                    'atr_values': [],
                    'adx_values': [],
                    'ready_count': 0,
                    'first_ready_message': None,
                    'last_message': None
                }
            
            stats = pair_stats[pair_name]
            stats['total_messages'] += 1
            stats['last_message'] = entry['message_count']
            
            if entry['atr'] is not None:
                stats['atr_values'].append(entry['atr'])
            if entry['adx'] is not None:
                stats['adx_values'].append(entry['adx'])
            if entry['indicators_ready'] and stats['first_ready_message'] is None:
                stats['first_ready_message'] = entry['message_count']
                stats['ready_count'] += 1
        
        # 输出统计结果
        for pair_name, stats in pair_stats.items():
            logger.info(f"\n🔍 交易对: {pair_name}")
            logger.info(f"  总消息数: {stats['total_messages']}")
            logger.info(f"  ATR数据点: {len(stats['atr_values'])}")
            logger.info(f"  ADX数据点: {len(stats['adx_values'])}")
            logger.info(f"  首次就绪: 消息#{stats['first_ready_message'] or '未就绪'}")
            
            if stats['atr_values']:
                atr_latest = stats['atr_values'][-1]
                atr_range = f"{min(stats['atr_values']):.6f} - {max(stats['atr_values']):.6f}"
                logger.info(f"  ATR最新值: {atr_latest:.6f} (范围: {atr_range})")
            
            if stats['adx_values']:
                adx_latest = stats['adx_values'][-1]
                adx_range = f"{min(stats['adx_values']):.2f} - {max(stats['adx_values']):.2f}"
                logger.info(f"  ADX最新值: {adx_latest:.2f} (范围: {adx_range})")
        
        logger.info("\n" + "="*60)

async def main():
    """主函数"""
    debugger = TechnicalIndicatorDebugger()
    
    # 连接Redis
    if not await debugger.connect_redis():
        return
    
    # 开始监控
    try:
        await debugger.subscribe_and_monitor(duration_seconds=120)  # 监控2分钟
    except Exception as e:
        logger.error(f"监控过程中发生错误: {e}")
    finally:
        if debugger.redis_client:
            await debugger.redis_client.close()

if __name__ == "__main__":
    asyncio.run(main())
