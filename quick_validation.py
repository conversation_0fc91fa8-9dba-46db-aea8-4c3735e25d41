#!/usr/bin/env python3
"""
快速验证技术指标修复效果

简化的验证脚本，用于快速检查技术指标数据传输是否正常
"""

import asyncio
import json
import redis.asyncio as redis
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("quick_validation")

class QuickValidator:
    """快速验证器"""
    
    def __init__(self):
        self.redis_client = None
        self.message_count = 0
        self.indicators_found = 0
        self.pairs_with_data = set()
        
    async def connect_redis(self):
        """连接Redis"""
        try:
            self.redis_client = redis.Redis(
                host='localhost',
                port=6379,
                decode_responses=True
            )
            await self.redis_client.ping()
            logger.info("✅ Redis连接成功")
            return True
        except Exception as e:
            logger.error(f"❌ Redis连接失败: {e}")
            return False
    
    async def quick_check(self, duration_seconds: int = 30):
        """快速检查技术指标数据"""
        if not self.redis_client:
            logger.error("Redis未连接")
            return False
        
        logger.info(f"🔍 开始快速验证，持续 {duration_seconds} 秒...")
        
        # 订阅策略数据频道
        pubsub = self.redis_client.pubsub()
        await pubsub.subscribe('trading_bot:strategy')
        
        start_time = datetime.now()
        
        try:
            async for message in pubsub.listen():
                if message['type'] == 'message':
                    self.message_count += 1
                    await self._check_message(message)
                    
                    # 每5条消息输出一次状态
                    if self.message_count % 5 == 0:
                        logger.info(f"📊 已处理 {self.message_count} 条消息，发现 {self.indicators_found} 条包含技术指标")
                    
                    # 检查是否超时
                    elapsed = (datetime.now() - start_time).total_seconds()
                    if elapsed >= duration_seconds:
                        break
                        
        except KeyboardInterrupt:
            logger.info("用户中断验证")
        finally:
            await pubsub.unsubscribe('trading_bot:strategy')
            await pubsub.close()
            
        # 输出结果
        await self._show_results()
        return True
    
    async def _check_message(self, message):
        """检查消息中的技术指标"""
        try:
            data = json.loads(message['data'])
            
            if data.get('event_type') == 'strategy_update':
                payload = data.get('payload', {})
                pairs_data = payload.get('pairs', {})
                
                has_indicators = False
                
                for pair_name, pair_data in pairs_data.items():
                    tech_indicators = pair_data.get('technical_indicators', {})
                    
                    if tech_indicators:
                        self.pairs_with_data.add(pair_name)
                        
                        atr = tech_indicators.get('atr')
                        adx = tech_indicators.get('adx')
                        dashboard_info = tech_indicators.get('dashboard_info', {})
                        
                        if atr is not None or adx is not None:
                            has_indicators = True
                            
                            # 输出详细信息（仅前几次）
                            if self.indicators_found < 3:
                                atr_str = f"{atr:.6f}" if atr else "None"
                                adx_str = f"{adx:.2f}" if adx else "None"
                                ready = dashboard_info.get('all_ready', False)
                                status = dashboard_info.get('status_message', 'N/A')
                                
                                logger.info(
                                    f"🎯 [{pair_name}] ATR={atr_str}, ADX={adx_str}, "
                                    f"就绪={ready}, 状态='{status}'"
                                )
                
                if has_indicators:
                    self.indicators_found += 1
                    
        except Exception as e:
            logger.error(f"处理消息失败: {e}")
    
    async def _show_results(self):
        """显示验证结果"""
        logger.info("\n" + "="*60)
        logger.info("📋 快速验证结果")
        logger.info("="*60)
        
        logger.info(f"总消息数: {self.message_count}")
        logger.info(f"包含技术指标的消息: {self.indicators_found}")
        logger.info(f"有数据的交易对: {len(self.pairs_with_data)}")
        
        if self.pairs_with_data:
            logger.info(f"交易对列表: {', '.join(sorted(self.pairs_with_data))}")
        
        if self.message_count > 0:
            coverage = (self.indicators_found / self.message_count) * 100
            logger.info(f"技术指标覆盖率: {coverage:.1f}%")
            
            if coverage > 80:
                logger.info("✅ 技术指标数据传输正常")
            elif coverage > 50:
                logger.info("⚠️  技术指标数据传输部分正常")
            else:
                logger.info("❌ 技术指标数据传输可能存在问题")
        else:
            logger.info("❌ 未接收到任何消息，请检查系统是否运行")
        
        logger.info("="*60)

async def main():
    """主函数"""
    validator = QuickValidator()
    
    # 连接Redis
    if not await validator.connect_redis():
        return
    
    # 运行快速验证
    try:
        await validator.quick_check(duration_seconds=30)  # 验证30秒
    except Exception as e:
        logger.error(f"验证过程中发生错误: {e}")
    finally:
        if validator.redis_client:
            await validator.redis_client.close()

if __name__ == "__main__":
    asyncio.run(main())
