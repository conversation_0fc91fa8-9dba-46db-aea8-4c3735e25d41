"""
基差套利策略引擎 - 布林带统计套利算法
纯计算模块，不涉及异步I/O操作
"""
import logging
import numpy as np
import pandas as pd
from collections import deque
from datetime import datetime, timedelta
from typing import Optional, Tuple, Dict, Any, List
from log_utils import get_structured_logger, trace_manager, KPICalculator
from okx_microstructure_adapter import OKXMicrostructureAdapter
from data_standards import (
    DataConverter, RiskLevel, StandardPositionData,
    StandardMarketData, DATA_CONVERTER, DATA_STANDARDS
)
from incremental_indicators import IndicatorManager
from position_manager import PositionManager, Position, PositionType, PositionStatus
# 移除Redis发布器导入 - 策略类不再直接发布数据
# from redis_publisher import get_redis_publisher

# 尝试导入pandas_ta，如果失败则提供回退方案
try:
    import pandas_ta as ta
    PANDAS_TA_AVAILABLE = True
except ImportError:
    print("Warning: pandas_ta not available, using fallback calculations")
    PANDAS_TA_AVAILABLE = False


class DataPreprocessor:
    """
    数据预处理层 - 标准化输入数据处理

    核心功能：
    1. 接收原始毫秒级Tick数据
    2. 聚合成固定时间窗口的VWAP
    3. 数据质量控制和异常值过滤
    4. 价格平滑处理
    """

    def __init__(self, config):
        self.config = config
        self.logger = get_structured_logger(f"{__name__}.DataPreprocessor")

        # 获取预处理配置
        self.preprocessing_config = config.DATA_PREPROCESSING
        self.vwap_config = self.preprocessing_config["VWAP_AGGREGATION"]
        self.quality_config = self.preprocessing_config["DATA_QUALITY_CONTROL"]
        self.smoothing_config = self.preprocessing_config["SMOOTHING"]

        # 原始tick数据缓存
        self.raw_ticks = deque(maxlen=10000)  # 保留最近10000个tick

        # 聚合数据缓存
        self.second_aggregates = deque(maxlen=3600)  # 1小时的秒级数据
        self.minute_aggregates = deque(maxlen=1440)  # 24小时的分钟级数据

        # 时间边界追踪
        self.last_second_boundary = None
        self.last_minute_boundary = None

        # 当前聚合窗口的临时数据
        self.current_second_window = []
        self.current_minute_window = []

        # 平滑处理状态
        self.smoothed_prices = {"spot": None, "futures": None}

        self.logger.info("数据预处理层初始化完成")

    def process_tick(self, timestamp: datetime, spot_price: float, futures_price: float,
                    spot_volume: float = 0.0, futures_volume: float = 0.0) -> Optional[Dict[str, Any]]:
        """
        处理单个tick数据

        Args:
            timestamp: tick时间戳
            spot_price: 现货价格
            futures_price: 期货价格
            spot_volume: 现货成交量（可选）
            futures_volume: 期货成交量（可选）

        Returns:
            Dict: 处理后的标准化数据，如果数据不足则返回None
        """
        try:
            # 数据质量检查
            if not self._validate_tick_data(spot_price, futures_price, spot_volume, futures_volume):
                return None

            # 异常值检测
            if self.quality_config["ENABLE_OUTLIER_DETECTION"]:
                if self._is_outlier(spot_price, futures_price):
                    self.logger.debug(f"检测到异常值，跳过: spot={spot_price}, futures={futures_price}")
                    return None

            # 添加到原始tick缓存
            tick_data = {
                "timestamp": timestamp,
                "spot_price": spot_price,
                "futures_price": futures_price,
                "spot_volume": spot_volume,
                "futures_volume": futures_volume,
                "basis": (futures_price - spot_price) / spot_price
            }
            self.raw_ticks.append(tick_data)

            # 时间窗口聚合
            self._aggregate_by_time_windows(tick_data)

            # 生成标准化输出
            return self._generate_standardized_output(timestamp, spot_price, futures_price)

        except Exception as e:
            self.logger.error(f"处理tick数据时发生错误: {e}")
            return None

    def _validate_tick_data(self, spot_price: float, futures_price: float,
                           spot_volume: float, futures_volume: float) -> bool:
        """数据质量验证"""
        try:
            # 基本数值检查
            if not all(isinstance(x, (int, float)) and x > 0 for x in [spot_price, futures_price]):
                return False

            # 价格合理性检查
            if spot_volume > 0 and spot_volume < self.quality_config["MIN_VOLUME_THRESHOLD"]:
                return False

            # 价格偏离检查（如果有历史数据）
            if len(self.raw_ticks) > 0:
                last_tick = self.raw_ticks[-1]
                spot_deviation = abs(spot_price - last_tick["spot_price"]) / last_tick["spot_price"]
                futures_deviation = abs(futures_price - last_tick["futures_price"]) / last_tick["futures_price"]

                max_deviation = self.quality_config["MAX_PRICE_DEVIATION_PERCENT"] / 100.0
                if spot_deviation > max_deviation or futures_deviation > max_deviation:
                    return False

            return True

        except Exception as e:
            self.logger.error(f"数据验证失败: {e}")
            return False

    def _is_outlier(self, spot_price: float, futures_price: float) -> bool:
        """异常值检测（基于Z-score）"""
        try:
            if len(self.raw_ticks) < 10:  # 数据不足，跳过异常值检测
                return False

            # 获取最近的价格数据
            recent_ticks = list(self.raw_ticks)[-20:]  # 最近20个tick
            spot_prices = [tick["spot_price"] for tick in recent_ticks]
            futures_prices = [tick["futures_price"] for tick in recent_ticks]

            # 计算Z-score
            spot_mean, spot_std = np.mean(spot_prices), np.std(spot_prices)
            futures_mean, futures_std = np.mean(futures_prices), np.std(futures_prices)

            if spot_std > 0:
                spot_z_score = abs(spot_price - spot_mean) / spot_std
            else:
                spot_z_score = 0

            if futures_std > 0:
                futures_z_score = abs(futures_price - futures_mean) / futures_std
            else:
                futures_z_score = 0

            threshold = self.quality_config["OUTLIER_Z_SCORE_THRESHOLD"]
            return spot_z_score > threshold or futures_z_score > threshold

        except Exception as e:
            self.logger.error(f"异常值检测失败: {e}")
            return False

    def _aggregate_by_time_windows(self, tick_data: Dict[str, Any]):
        """按时间窗口聚合数据 - 增强版本，包含调试信息"""
        try:
            timestamp = tick_data["timestamp"]

            # 秒级聚合
            second_boundary = timestamp.replace(microsecond=0)
            if self.last_second_boundary != second_boundary:
                if self.last_second_boundary is not None and self.current_second_window:
                    # 完成上一秒的聚合
                    second_aggregate = self._calculate_vwap_aggregate(self.current_second_window)
                    if second_aggregate:
                        second_aggregate["timestamp"] = self.last_second_boundary
                        self.second_aggregates.append(second_aggregate)

                        # 调试信息：每10个秒级聚合打印一次
                        if len(self.second_aggregates) % 10 == 0:
                            self.logger.debug(f"秒级聚合进度: {len(self.second_aggregates)}个数据点")

                self.last_second_boundary = second_boundary
                self.current_second_window = []

            self.current_second_window.append(tick_data)

            # 分钟级聚合
            minute_boundary = timestamp.replace(second=0, microsecond=0)
            if self.last_minute_boundary != minute_boundary:
                if self.last_minute_boundary is not None and self.current_minute_window:
                    # 完成上一分钟的聚合
                    minute_aggregate = self._calculate_vwap_aggregate(self.current_minute_window)
                    if minute_aggregate:
                        minute_aggregate["timestamp"] = self.last_minute_boundary
                        self.minute_aggregates.append(minute_aggregate)

                        # 调试信息：每个分钟级聚合都打印
                        self.logger.info(f"✅ 分钟级聚合完成: 第{len(self.minute_aggregates)}个数据点, "
                                       f"基差={minute_aggregate['basis']:.6f}, "
                                       f"时间={self.last_minute_boundary}")

                self.last_minute_boundary = minute_boundary
                self.current_minute_window = []

            self.current_minute_window.append(tick_data)

        except Exception as e:
            self.logger.error(f"时间窗口聚合失败: {e}")

    def _calculate_vwap_aggregate(self, tick_window: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """计算VWAP聚合数据"""
        try:
            if not tick_window:
                return None

            if len(tick_window) < self.vwap_config.get("MIN_TICKS", 1):
                return None

            # 提取数据
            spot_prices = [tick["spot_price"] for tick in tick_window]
            futures_prices = [tick["futures_price"] for tick in tick_window]
            spot_volumes = [tick["spot_volume"] for tick in tick_window]
            futures_volumes = [tick["futures_volume"] for tick in tick_window]

            # 计算VWAP或简单平均
            if self.vwap_config["VOLUME_WEIGHTED"] and any(v > 0 for v in spot_volumes + futures_volumes):
                # 成交量加权平均
                total_spot_volume = sum(spot_volumes) or 1.0  # 避免除零
                total_futures_volume = sum(futures_volumes) or 1.0

                spot_vwap = sum(p * v for p, v in zip(spot_prices, spot_volumes)) / total_spot_volume
                futures_vwap = sum(p * v for p, v in zip(futures_prices, futures_volumes)) / total_futures_volume
            else:
                # 简单平均
                spot_vwap = np.mean(spot_prices)
                futures_vwap = np.mean(futures_prices)

            return {
                "spot_price": spot_vwap,
                "futures_price": futures_vwap,
                "basis": (futures_vwap - spot_vwap) / spot_vwap,
                "tick_count": len(tick_window),
                "total_spot_volume": sum(spot_volumes),
                "total_futures_volume": sum(futures_volumes)
            }

        except Exception as e:
            self.logger.error(f"VWAP聚合计算失败: {e}")
            return None

    def _generate_standardized_output(self, timestamp: datetime, spot_price: float,
                                    futures_price: float) -> Dict[str, Any]:
        """生成标准化输出数据"""
        try:
            # 价格平滑处理
            if self.smoothing_config["ENABLE_PRICE_SMOOTHING"]:
                spot_price = self._apply_smoothing("spot", spot_price)
                futures_price = self._apply_smoothing("futures", futures_price)

            return {
                "timestamp": timestamp,
                "spot_price": spot_price,
                "futures_price": futures_price,
                "basis": (futures_price - spot_price) / spot_price,
                "data_quality": "processed",
                "preprocessing_applied": True
            }

        except Exception as e:
            self.logger.error(f"生成标准化输出失败: {e}")
            return {
                "timestamp": timestamp,
                "spot_price": spot_price,
                "futures_price": futures_price,
                "basis": (futures_price - spot_price) / spot_price,
                "data_quality": "raw",
                "preprocessing_applied": False
            }

    def _apply_smoothing(self, price_type: str, current_price: float) -> float:
        """应用价格平滑"""
        try:
            method = self.smoothing_config["SMOOTHING_METHOD"]

            if method == "none":
                return current_price

            if self.smoothed_prices[price_type] is None:
                self.smoothed_prices[price_type] = current_price
                return current_price

            if method == "ema":
                alpha = self.smoothing_config["SMOOTHING_ALPHA"]
                smoothed = alpha * current_price + (1 - alpha) * self.smoothed_prices[price_type]
                self.smoothed_prices[price_type] = smoothed
                return smoothed

            # 其他平滑方法可以在这里添加
            return current_price

        except Exception as e:
            self.logger.error(f"价格平滑失败: {e}")
            return current_price

    def get_aggregated_data(self, level: str = "second", count: int = 100) -> List[Dict[str, Any]]:
        """获取聚合数据 - 增强版本，包含调试信息"""
        try:
            if level == "second":
                available_data = list(self.second_aggregates)[-count:]
                self.logger.debug(f"获取秒级聚合数据: 请求{count}个，实际返回{len(available_data)}个")
                return available_data
            elif level == "minute":
                available_data = list(self.minute_aggregates)[-count:]
                self.logger.debug(f"获取分钟级聚合数据: 请求{count}个，实际返回{len(available_data)}个")
                return available_data
            else:
                raise ValueError(f"不支持的聚合级别: {level}")

        except Exception as e:
            self.logger.error(f"获取聚合数据失败: {e}")
            return []


class BasisArbitrageStrategy:
    """基差套利策略 - 基于布林带的统计套利"""
    
    def __init__(self, config):
        self.config = config
        self.logger = get_structured_logger(__name__)

        # 传统单层策略参数（向后兼容）
        self.window = config.BOLLINGER_WINDOW
        self.std_dev = config.BOLLINGER_STD_DEV
        self.basis_entry_threshold = config.BASIS_ENTRY_THRESHOLD
        self.basis_profit_target = config.BASIS_PROFIT_TARGET
        self.basis_stop_loss = config.BASIS_STOP_LOSS

        # 分层布林带系统参数
        self.layered_bb = config.STRATEGY_OPTIMIZATION.get("LAYERED_BOLLINGER_BANDS", {})

        # 战略层布林带参数（长周期，机会识别）
        self.strategic_bb = self.layered_bb.get("STRATEGIC_BB", {})
        self.strategic_window = self.strategic_bb.get("WINDOW", 30)  # 30分钟
        self.strategic_std_dev = self.strategic_bb.get("STD_DEV", 2.0)

        # 战术层布林带参数（短周期，时机选择）
        self.tactical_bb = self.layered_bb.get("TACTICAL_BB", {})
        self.tactical_window = self.tactical_bb.get("WINDOW", 60)  # 60秒
        self.tactical_std_dev = self.tactical_bb.get("STD_DEV", 2.5)

        # 初始化数据预处理层
        self.data_preprocessor = DataPreprocessor(config)
        self.logger.info("数据预处理层已集成到策略引擎")

        # 初始化仓位管理器
        self.position_manager = PositionManager(config)
        self.logger.info("仓位管理器已集成到策略引擎")
        
        # 分级仓位系统参数
        self.graded_config = config.RISK_MANAGEMENT.get("GRADED_POSITION_SYSTEM", {})
        self.filter_controls = self.graded_config.get("FILTER_CONTROLS", {})
        self.volatility_filter = self.graded_config.get("VOLATILITY_FILTER", {})
        self.trend_filter = self.graded_config.get("TREND_FILTER", {})
        
        # ATR计算历史数据
        self.high_prices = deque(maxlen=self.volatility_filter.get("MIN_ATR_PERIODS", 14) * 2)
        self.low_prices = deque(maxlen=self.volatility_filter.get("MIN_ATR_PERIODS", 14) * 2)
        self.close_prices = deque(maxlen=self.volatility_filter.get("MIN_ATR_PERIODS", 14) * 2)
        
        # ADX计算不再需要price_changes，直接使用高低收盘价数据
        
        # 增量技术指标管理器
        atr_periods = self.volatility_filter.get("MIN_ATR_PERIODS", 14)
        adx_periods = self.trend_filter.get("ADX_PERIODS", 14)
        self.indicator_manager = IndicatorManager(
            atr_period=atr_periods,
            adx_period=adx_periods
        )
        
        # 当前技术指标值（用于向后兼容）
        self.current_atr = None
        self.current_adx = None
        
        # 技术指标初始化标志（用于调试日志）
        self._first_indicators_ready = False
        
        # 传统价格历史数据（向后兼容）
        self.spot_prices = deque(maxlen=self.window * 2)
        self.futures_prices = deque(maxlen=self.window * 2)
        self.basis_history = deque(maxlen=self.window * 2)
        self.timestamps = deque(maxlen=self.window * 2)
        
        # 分层布林带数据结构
        # 战略层：分钟级聚合数据
        max_strategic_points = max(self.strategic_window * 2, 100)
        self.strategic_basis_history = deque(maxlen=max_strategic_points)
        self.strategic_timestamps = deque(maxlen=max_strategic_points)
        
        # 战术层：秒级聚合数据
        max_tactical_points = max(self.tactical_window * 2, 200)
        self.tactical_basis_history = deque(maxlen=max_tactical_points)
        self.tactical_timestamps = deque(maxlen=max_tactical_points)
        
        # 数据聚合缓存
        self.minute_vwap_cache = {}  # 分钟级VWAP缓存
        self.second_vwap_cache = {}  # 秒级VWAP缓存
        self.last_minute_boundary = None
        self.last_second_boundary = None

        # 当前状态
        self.current_spot_price = None
        self.current_futures_price = None
        self.current_basis = None
        self.last_update_time = None

        # 传统布林带已移除 - 使用分层布林带系统
        
        # 分层布林带指标
        # 战略层布林带
        self.strategic_bb_middle = None
        self.strategic_bb_upper = None
        self.strategic_bb_lower = None
        
        # 战术层布林带
        self.tactical_bb_middle = None
        self.tactical_bb_upper = None
        self.tactical_bb_lower = None
        
        # 分层信号状态
        self.strategy_state = "standby"  # standby, strategic_alert, tactical_ready
        self.strategic_signal_active = False
        self.tactical_signal_confirmed = False

        # 策略状态
        self.entry_basis = None  # 入场时的基差
        self.entry_time = None   # 入场时间

        # 统计信息
        self.total_updates = 0
        self.signal_count = {"entry": 0, "exit": 0}

        # 微观结构分析模块
        self.microstructure_adapter = None  # 将在主程序中设置
        
        # Redis发布器（仪表盘通信）
        # 移除Redis发布器 - 策略类不再直接发布数据
        # self.redis_publisher = None
        
        # 🔥 强制计算机制：确保布林带持续更新
        self.last_forced_calculation_time = None
        self.forced_calculation_interval = 60  # 每60秒强制计算一次布林带
        self.force_calculation_threshold = 5  # 当超过5秒没有成功计算时触发强制计算
        self.last_successful_calculation_time = None

        # 🔥 布林带日志输出控制
        self._bb_calculation_count = 0
        self._last_bb_log_time = datetime.now()

    def set_microstructure_adapter(self, adapter: OKXMicrostructureAdapter):
        """设置微观结构分析适配器"""
        self.microstructure_adapter = adapter
        self.logger.info_structured(
            "微观结构分析模块已启用",
            event_type="microstructure_enabled",
            metrics={'status': 'active'}
        )
    
    # 移除Redis发布器设置方法 - 策略类不再直接发布数据
    # def set_redis_publisher(self, publisher):
    #     """设置Redis发布器"""
    #     self.redis_publisher = publisher
    
    def finalize_initialization(self):
        """完成策略初始化"""
        self.logger.info(f"策略初始化完成 - 窗口:{self.window}, 标准差:{self.std_dev}")
    
    def update_prices(self, spot_price: float, futures_price: float,
                     high_price: Optional[float] = None, low_price: Optional[float] = None,
                     spot_volume: float = 0.0, futures_volume: float = 0.0) -> bool:
        """
        更新价格数据并计算基差 - 集成数据预处理层

        Args:
            spot_price: 现货价格（原始tick数据）
            futures_price: 期货价格（原始tick数据）
            high_price: 最高价（可选）
            low_price: 最低价（可选）
            spot_volume: 现货成交量（可选）
            futures_volume: 期货成交量（可选）

        Returns:
            bool: 是否成功更新（数据足够计算布林带）
        """
        try:
            current_time = datetime.now()

            # 阶段1：数据预处理 - 标准化输入层
            processed_data = self.data_preprocessor.process_tick(
                timestamp=current_time,
                spot_price=spot_price,
                futures_price=futures_price,
                spot_volume=spot_volume,
                futures_volume=futures_volume
            )

            if not processed_data:
                self.logger.debug("数据预处理失败，跳过本次更新")
                return False

            # 阶段2：使用预处理后的标准化数据
            processed_spot = processed_data["spot_price"]
            processed_futures = processed_data["futures_price"]
            processed_basis = processed_data["basis"]

            # 更新当前价格（使用预处理后的数据）
            self.current_spot_price = processed_spot
            self.current_futures_price = processed_futures
            self.current_basis = processed_basis
            self.last_update_time = current_time

            # 添加到历史数据
            self.spot_prices.append(processed_spot)
            self.futures_prices.append(processed_futures)
            self.basis_history.append(processed_basis)
            self.timestamps.append(current_time)

            # 🔥 修复：确保战略层基差历史数据正常积累
            # 战略层需要基差历史数据来计算布林带，必须保持数据流
            self.strategic_basis_history.append(processed_basis)
            self.strategic_timestamps.append(current_time)

            # 🔥 新增：更新分层布林带数据（通过VWAP聚合）
            self._update_layered_bollinger_data(current_time, processed_basis)

            # 智能数据窗口管理：确保数据量适中，避免内存膨胀
            max_strategic_buffer = self.strategic_window * 3  # 保留3倍窗口数据作为缓冲
            if len(self.strategic_basis_history) > max_strategic_buffer:
                # 保留最新的数据
                trim_count = len(self.strategic_basis_history) - max_strategic_buffer
                self.strategic_basis_history = deque(list(self.strategic_basis_history)[trim_count:], maxlen=max_strategic_buffer)
                self.strategic_timestamps = deque(list(self.strategic_timestamps)[trim_count:], maxlen=max_strategic_buffer)

                if self.total_updates % 100 == 0:  # 减少日志频率
                    self.logger.debug(f"🔧 战略层数据窗口管理: 修剪到{len(self.strategic_basis_history)}个数据点")
            
            # 记录实时基差更新（优化日志频率）
            if self.total_updates % 50 == 0:  # 降低日志频率从20改为50
                recent_basis_trend = ""
                if len(self.strategic_basis_history) >= 3:
                    recent_values = list(self.strategic_basis_history)[-3:]
                    recent_basis_trend = f" 趋势:[{recent_values[0]:.6f}→{recent_values[1]:.6f}→{recent_values[2]:.6f}]"
                
                self.logger.debug(f"📊 实时基差更新: {processed_basis:.6f}, "
                               f"战略层数据点: {len(self.strategic_basis_history)}/{max_strategic_buffer}"
                               f"{recent_basis_trend}")

            # 更新技术指标计算所需数据
            # 使用现货价格作为收盘价，如果没有提供高低价则使用现货价格
            self.close_prices.append(processed_spot)
            self.high_prices.append(high_price if high_price else processed_spot)
            self.low_prices.append(low_price if low_price else processed_spot)

            self.total_updates += 1

            # 阶段3：更新分层布林带数据（使用聚合数据）
            # 保留原有逻辑用于数据预处理和验证
            self._update_layered_bollinger_data_enhanced(current_time)

            # 🔥 关键修复：使用分层布林带系统，不再依赖传统basis_history判断
            # 强制执行技术指标计算和布林带计算，确保持续更新
            try:
                # 始终执行技术指标计算（ATR和ADX）
                self._calculate_technical_indicators_incremental(high_price, low_price, processed_spot)

                # 🔥 新增：独立的布林带计算检查，不依赖策略信号
                # 只要有足够的原始数据就尝试计算布林带
                if self._has_data_for_calculation():
                    calculation_success = self._calculate_layered_bollinger_bands()
                    if calculation_success:
                        self.last_successful_calculation_time = current_time
                else:
                    # 检查是否需要强制计算布林带
                    need_forced_calculation = self._should_force_bollinger_calculation(current_time)
                    if need_forced_calculation:
                        calculation_success = self._calculate_layered_bollinger_bands_with_force_check(True)
                        if calculation_success:
                            self.last_successful_calculation_time = current_time
                
                # 检查是否有足够的战略层数据来判断更新成功
                strategic_data_sufficient = len(self.strategic_basis_history) >= max(self.strategic_window // 2, 5)  # 至少需要一半数据
                
                if strategic_data_sufficient:
                    # 实时数据充足，更新成功
                    if self.total_updates % 50 == 0:  # 每50次更新记录一次状态
                        self.logger.debug(f"✅ 实时数据更新成功: 战略层={len(self.strategic_basis_history)}个数据点")
                    return True
                else:
                    # 实时数据不足，但仍然执行了计算
                    remaining = max(self.strategic_window - len(self.strategic_basis_history), 0)
                    if self.total_updates % 10 == 0:  # 每10次更新打印一次进度
                        self.logger.info(f"实时数据收集中... 战略层还需要{remaining}个数据点")
                    
                    # 兼容性检查：如果传统数据足够，也认为更新成功
                    if len(self.basis_history) >= self.window:
                        self.logger.debug("📊 使用传统数据作为备用，更新成功")
                        return True
                    return False
                    
            except Exception as calc_e:
                self.logger.error(f"布林带和技术指标计算失败: {calc_e}")
                # 即使计算失败，也要确保价格更新继续进行
                return True  # 返回True确保数据流不中断

        except Exception as e:
            self.logger.error(f"更新价格时发生错误: {e}")
            return False

    def calculate_enhanced_data_for_dashboard(self, position_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """计算仪表盘需要的增强数据项"""
        try:
            enhanced_data = {}

            # 1. 计算入场成本数据
            if position_data:
                enhanced_data.update(self._calculate_entry_cost_data(position_data))

            # 2. 计算止盈目标数据
            enhanced_data.update(self._calculate_profit_target_data())

            # 3. 计算风险评估数据
            if position_data:
                enhanced_data.update(self._calculate_risk_assessment_data(position_data))

            # 4. 计算微观结构数据
            enhanced_data.update(self._calculate_microstructure_data())

            # 5. 计算市场状态增强数据
            enhanced_data.update(self._calculate_market_state_enhanced())

            # 6. 计算持仓时长数据
            if position_data:
                enhanced_data.update(self._calculate_position_duration_data(position_data))

            return enhanced_data

        except Exception as e:
            self.logger.error(f"计算增强数据失败: {e}")
            return {}

    def _calculate_entry_cost_data(self, position_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算入场成本数据"""
        try:
            position_size = position_data.get('position_size', 0.0)
            entry_spot_price = position_data.get('entry_spot_price', self.current_spot_price)
            entry_futures_price = position_data.get('entry_futures_price', self.current_futures_price)

            # 计算现货成本和期货成本
            spot_cost = position_size * entry_spot_price
            futures_cost = position_size * entry_futures_price  # 简化：假设1:1对冲

            # 总入场成本（包含手续费）
            if hasattr(self.config, 'TRADING_COSTS') and isinstance(self.config.TRADING_COSTS, dict):
                taker_fee_rate = self.config.TRADING_COSTS.get("TAKER_FEE_RATE", 0.001)
            else:
                taker_fee_rate = 0.001  # 默认手续费率
            spot_fees = spot_cost * taker_fee_rate
            futures_fees = futures_cost * taker_fee_rate

            total_entry_cost = spot_cost + futures_cost + spot_fees + futures_fees

            return {
                'entry_cost': round(total_entry_cost, DATA_STANDARDS.AMOUNT_PRECISION),
                'spot_cost': round(spot_cost, DATA_STANDARDS.AMOUNT_PRECISION),
                'futures_cost': round(futures_cost, DATA_STANDARDS.AMOUNT_PRECISION),
                'total_fees': round(spot_fees + futures_fees, DATA_STANDARDS.AMOUNT_PRECISION)
            }

        except Exception as e:
            self.logger.error(f"计算入场成本失败: {e}")
            return {'entry_cost': 0.0}

    def _calculate_profit_target_data(self) -> Dict[str, Any]:
        """计算止盈目标数据"""
        try:
            # 从配置获取止盈目标 - 修复多交易对配置访问
            if hasattr(self.config, 'PAIR_CONFIG') and isinstance(self.config.PAIR_CONFIG, dict):
                profit_target_config = self.config.PAIR_CONFIG.get("profit_target", {})
            else:
                profit_target_config = {}
            target_basis_bps = profit_target_config.get("basis_bps", 50)  # 默认50基点

            # 转换为小数形式
            profit_target_basis = DATA_CONVERTER.bps_to_basis(target_basis_bps)

            # 计算距离止盈的距离
            if self.current_basis is not None:
                current_basis_bps = DATA_CONVERTER.basis_to_bps(abs(self.current_basis))
                profit_distance_bps = abs(target_basis_bps - current_basis_bps)
            else:
                profit_distance_bps = target_basis_bps

            return {
                'profit_target_basis': round(profit_target_basis, DATA_STANDARDS.BASIS_DECIMAL_PRECISION),
                'profit_target_bps': target_basis_bps,
                'profit_distance_bps': round(profit_distance_bps, DATA_STANDARDS.BASIS_BPS_PRECISION)
            }

        except Exception as e:
            self.logger.error(f"计算止盈目标失败: {e}")
            return {
                'profit_target_basis': 0.0,
                'profit_distance_bps': 0.0
            }

    def _calculate_risk_assessment_data(self, position_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算风险评估数据"""
        try:
            # 获取止损配置
            if hasattr(self.config, 'RISK_MANAGEMENT') and isinstance(self.config.RISK_MANAGEMENT, dict):
                stop_loss_config = self.config.RISK_MANAGEMENT.get("STOP_LOSS_PERCENT", 0.02)
            else:
                stop_loss_config = 0.02  # 默认2%止损
            stop_loss_bps = stop_loss_config * 10000  # 转换为基点

            # 计算当前距离止损的距离
            entry_basis = position_data.get('entry_basis', 0.0)
            current_basis = self.current_basis or 0.0

            # 计算基差变化（基点）
            basis_change = entry_basis - current_basis
            basis_change_bps = DATA_CONVERTER.basis_to_bps(basis_change)

            # 计算距离止损线的距离
            stop_loss_distance_bps = stop_loss_bps - abs(basis_change_bps)

            # 评估风险等级
            risk_level = DATA_CONVERTER.assess_risk_level(stop_loss_distance_bps)

            return {
                'risk_level': risk_level.value,
                'stop_loss_distance_bps': round(stop_loss_distance_bps, DATA_STANDARDS.BASIS_BPS_PRECISION),
                'basis_change_bps': round(basis_change_bps, DATA_STANDARDS.BASIS_BPS_PRECISION),
                'stop_loss_threshold_bps': stop_loss_bps
            }

        except Exception as e:
            self.logger.error(f"计算风险评估失败: {e}")
            return {
                'risk_level': RiskLevel.SAFE.value,
                'stop_loss_distance_bps': 100.0,
                'basis_change_bps': 0.0
            }

    def _calculate_microstructure_data(self) -> Dict[str, Any]:
        """计算微观结构数据"""
        try:
            micro_data = {}

            # 1. 计算买卖压力指标
            if hasattr(self, 'microstructure_adapter') and self.microstructure_adapter:
                # 获取OBI和买卖比数据
                obi = getattr(self, 'current_obi', 0.0)
                buy_sell_ratio = getattr(self, 'current_buy_sell_ratio', 1.0)

                # 转换为压力指标 (0-100)
                buy_pressure = min(100, max(0, (obi + 1) * 50))  # OBI范围[-1,1]转换为[0,100]
                sell_pressure = 100 - buy_pressure

                # 成交量压力（基于买卖比）
                if buy_sell_ratio > 1:
                    volume_pressure = min(100, (buy_sell_ratio - 1) * 50 + 50)
                else:
                    volume_pressure = max(0, buy_sell_ratio * 50)

                micro_data.update({
                    'buy_pressure': round(buy_pressure, 1),
                    'sell_pressure': round(sell_pressure, 1),
                    'volume_pressure': round(volume_pressure, 1),
                    'obi': round(obi, 3),
                    'buy_sell_ratio': round(buy_sell_ratio, 3)
                })
            else:
                # 默认中性值
                micro_data.update({
                    'buy_pressure': 50.0,
                    'sell_pressure': 50.0,
                    'volume_pressure': 50.0,
                    'obi': 0.0,
                    'buy_sell_ratio': 1.0
                })

            # 2. 计算价差数据
            if self.current_spot_price and self.current_futures_price:
                spread = abs(self.current_futures_price - self.current_spot_price)
                spread_bps = DATA_CONVERTER.basis_to_bps(spread / self.current_spot_price)

                micro_data.update({
                    'spread': round(spread, DATA_STANDARDS.PRICE_PRECISION),
                    'spread_bps': round(spread_bps, DATA_STANDARDS.BASIS_BPS_PRECISION)
                })
            else:
                micro_data.update({
                    'spread': 0.0,
                    'spread_bps': 0.0
                })

            # 3. 订单簿深度（模拟数据，实际应从交易所获取）
            micro_data['orderbook_depth'] = self._generate_orderbook_depth_display()

            # 4. 交易强度（基于最近的价格更新频率）
            micro_data['trade_intensity'] = self._calculate_trade_intensity()

            # 5. 近期大单（占位符，实际需要从交易数据获取）
            micro_data['recent_large_orders'] = []

            return micro_data

        except Exception as e:
            self.logger.error(f"计算微观结构数据失败: {e}")
            return {
                'buy_pressure': 50.0,
                'sell_pressure': 50.0,
                'volume_pressure': 50.0,
                'spread': 0.0,
                'spread_bps': 0.0,
                'orderbook_depth': [],
                'trade_intensity': 0,
                'recent_large_orders': []
            }

    def _calculate_market_state_enhanced(self) -> Dict[str, Any]:
        """计算市场状态增强数据"""
        try:
            enhanced_data = {}

            # 计算距离布林带上下轨的距离
            if (self.current_basis is not None and
                self.strategic_bb_upper is not None and
                self.strategic_bb_lower is not None):

                current_basis_bps = DATA_CONVERTER.basis_to_bps(self.current_basis)
                upper_bps = DATA_CONVERTER.basis_to_bps(self.strategic_bb_upper)
                lower_bps = DATA_CONVERTER.basis_to_bps(self.strategic_bb_lower)

                distance_to_upper_bps = upper_bps - current_basis_bps
                distance_to_lower_bps = current_basis_bps - lower_bps

                enhanced_data.update({
                    'distance_to_upper_bps': round(distance_to_upper_bps, DATA_STANDARDS.BASIS_BPS_PRECISION),
                    'distance_to_lower_bps': round(distance_to_lower_bps, DATA_STANDARDS.BASIS_BPS_PRECISION),
                    'basis_bps': round(current_basis_bps, DATA_STANDARDS.BASIS_BPS_PRECISION)
                })
            else:
                enhanced_data.update({
                    'distance_to_upper_bps': 0.0,
                    'distance_to_lower_bps': 0.0,
                    'basis_bps': 0.0
                })

            return enhanced_data

        except Exception as e:
            self.logger.error(f"计算市场状态增强数据失败: {e}")
            return {
                'distance_to_upper_bps': 0.0,
                'distance_to_lower_bps': 0.0,
                'basis_bps': 0.0
            }

    def _generate_orderbook_depth_display(self) -> List[str]:
        """生成订单簿深度显示（占位符实现）"""
        try:
            # 这里应该从实际的订单簿数据生成
            # 当前提供模拟数据作为占位符
            depth_display = [
                "卖5 ████████ 45,010.5",
                "卖4 ██████   45,009.8",
                "卖3 ████     45,009.1",
                "卖2 ██████   45,008.4",
                "卖1 ████████ 45,007.7",
                "─────────────────────",
                "买1 ████████ 45,007.0",
                "买2 ██████   45,006.3",
                "买3 ████     45,005.6",
                "买4 ██████   45,004.9",
                "买5 ████████ 45,004.2"
            ]
            return depth_display

        except Exception as e:
            self.logger.error(f"生成订单簿深度显示失败: {e}")
            return ["等待订单簿数据..."]

    def _calculate_trade_intensity(self) -> int:
        """计算交易强度（每分钟tick数）"""
        try:
            # 基于最近的价格更新频率估算
            if hasattr(self, 'last_update_time') and self.last_update_time:
                current_time = datetime.now()
                time_diff = (current_time - self.last_update_time).total_seconds()

                # 简单估算：假设每次更新代表一个tick
                if time_diff > 0:
                    ticks_per_second = 1 / time_diff
                    ticks_per_minute = int(ticks_per_second * 60)
                    return min(ticks_per_minute, 1000)  # 限制最大值

            return 0

        except Exception as e:
            self.logger.error(f"计算交易强度失败: {e}")
            return 0

    def _calculate_position_duration_data(self, position_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算持仓时长数据"""
        try:
            # 获取开仓时间
            open_time_str = position_data.get('open_time')
            if not open_time_str:
                return {'position_duration': 'N/A'}

            # 解析开仓时间
            if isinstance(open_time_str, str):
                try:
                    # 尝试解析ISO格式
                    open_time = datetime.fromisoformat(open_time_str.replace('Z', '+00:00'))
                except ValueError:
                    # 尝试其他格式
                    open_time = datetime.strptime(open_time_str, "%Y-%m-%d %H:%M:%S")
            else:
                open_time = open_time_str

            # 计算持仓时长
            current_time = datetime.now()
            duration_seconds = (current_time - open_time.replace(tzinfo=None)).total_seconds()

            # 使用标准化格式
            position_duration = DATA_CONVERTER.format_duration(duration_seconds)

            return {
                'position_duration': position_duration,
                'position_duration_seconds': duration_seconds
            }

        except Exception as e:
            self.logger.error(f"计算持仓时长失败: {e}")
            return {'position_duration': 'N/A'}

    def _update_layered_bollinger_data_enhanced(self, current_time: datetime):
        """增强版分层布林带数据更新 - 确保持续稳定的实时更新"""
        try:
            # 🔥 关键修复：优先使用实时数据，聚合数据仅作为补充验证
            
            # 记录当前实时数据状态
            strategic_count = len(self.strategic_basis_history)
            tactical_count = len(self.tactical_basis_history)
            
            self.logger.debug(f"🔍 实时数据状态: "
                            f"战略层={strategic_count}个, 战术层={tactical_count}个")

            # 战略层数据检查：主要依赖实时更新（lines 517-525），聚合数据仅作验证
            if strategic_count >= self.strategic_window:
                self.logger.debug(f"✅ 战略层实时数据充足: {strategic_count}/{self.strategic_window}")
            else:
                # 数据不足时，尝试从聚合数据补充
                try:
                    minute_aggregates = self.data_preprocessor.get_aggregated_data("minute", self.strategic_window)
                    if minute_aggregates and len(minute_aggregates) > strategic_count:
                        self.logger.info(f"🔄 从聚合数据补充战略层: {len(minute_aggregates)}个分钟级数据点")
                        
                        # 谨慎地补充数据，避免清空现有数据
                        for aggregate in minute_aggregates[-self.strategic_window:]:
                            if aggregate["timestamp"] not in [ts for ts in self.strategic_timestamps[-10:]]:  # 避免重复
                                self.strategic_basis_history.append(aggregate["basis"])
                                self.strategic_timestamps.append(aggregate["timestamp"])
                except Exception as agg_e:
                    self.logger.warning(f"聚合数据补充失败，继续使用实时数据: {agg_e}")

            # 🔥 重构战术层数据更新：避免清空，采用增量更新
            try:
                second_aggregates = self.data_preprocessor.get_aggregated_data("second", self.tactical_window)
                if second_aggregates:
                    old_count = len(self.tactical_basis_history)

                    # 🔥 关键修复：不再清空数据，而是增量更新
                    # 获取最新的时间戳，避免重复添加
                    last_timestamp = self.tactical_timestamps[-1] if self.tactical_timestamps else None

                    new_data_count = 0
                    for aggregate in second_aggregates:
                        aggregate_timestamp = aggregate["timestamp"]
                        # 只添加新的数据点
                        if last_timestamp is None or aggregate_timestamp > last_timestamp:
                            self.tactical_basis_history.append(aggregate["basis"])
                            self.tactical_timestamps.append(aggregate_timestamp)
                            new_data_count += 1

                    # 维护窗口大小
                    max_tactical_size = self.tactical_window * 2
                    if len(self.tactical_basis_history) > max_tactical_size:
                        trim_size = len(self.tactical_basis_history) - self.tactical_window
                        self.tactical_basis_history = deque(list(self.tactical_basis_history)[trim_size:], maxlen=max_tactical_size)
                        self.tactical_timestamps = deque(list(self.tactical_timestamps)[trim_size:], maxlen=max_tactical_size)

                    if new_data_count > 0:
                        self.logger.debug(f"⚡ 战术层增量更新: +{new_data_count}个新数据点，总计{len(self.tactical_basis_history)}个")
                else:
                    self.logger.debug("⚠️ 战术层聚合数据为空")
            except Exception as tactical_e:
                self.logger.warning(f"战术层数据更新失败: {tactical_e}")

            # 确保数据窗口不超过限制（防止内存泄露）
            max_strategic_size = self.strategic_window * 2  # 允许2倍缓冲
            if len(self.strategic_basis_history) > max_strategic_size:
                trim_size = len(self.strategic_basis_history) - self.strategic_window
                self.strategic_basis_history = deque(list(self.strategic_basis_history)[trim_size:], maxlen=max_strategic_size)
                self.strategic_timestamps = deque(list(self.strategic_timestamps)[trim_size:], maxlen=max_strategic_size)
                self.logger.debug(f"🔧 修剪战略层数据: 保留最近{self.strategic_window}个数据点")

        except Exception as e:
            self.logger.error(f"增强版分层布林带数据更新失败: {e}")
            # 不再回退到旧方法，确保实时数据流稳定性
            self.logger.warning("继续使用现有实时数据，跳过本次聚合数据更新")
    
    # 传统布林带计算已移除 - 使用分层布林带系统
    
    def _update_layered_bollinger_data(self, current_time: datetime, basis: float):
        """更新分层布林带数据 - 基于时间边界的VWAP聚合，确保数据连续性"""
        try:
            # 1. 更新战略层数据（分钟级聚合）
            minute_boundary = current_time.replace(second=0, microsecond=0)

            if self.last_minute_boundary != minute_boundary:
                # 新的分钟，计算上一分钟的VWAP并添加到战略层
                if self.last_minute_boundary is not None:
                    minute_vwap_basis = self._calculate_minute_vwap_basis(self.last_minute_boundary)
                    if minute_vwap_basis is not None:
                        # 🔥 关键修复：检查是否已存在该时间戳的数据，避免重复
                        if not self.strategic_timestamps or self.last_minute_boundary not in self.strategic_timestamps:
                            self.strategic_basis_history.append(minute_vwap_basis)
                            self.strategic_timestamps.append(self.last_minute_boundary)

                            self.logger.debug(f"📈 战略层VWAP更新: {minute_vwap_basis:.6f} @ {self.last_minute_boundary}")
                        else:
                            self.logger.debug(f"⚠️ 跳过重复的战略层数据: {self.last_minute_boundary}")

                self.last_minute_boundary = minute_boundary
                self.minute_vwap_cache = {}  # 清空缓存
            
            # 累积当前分钟的数据
            minute_key = minute_boundary.isoformat()
            if minute_key not in self.minute_vwap_cache:
                self.minute_vwap_cache[minute_key] = []
            self.minute_vwap_cache[minute_key].append({
                'basis': basis,
                'timestamp': current_time,
                'spot_price': self.current_spot_price,
                'futures_price': self.current_futures_price
            })

            # 🔥 新增：实时数据备份机制，确保即使VWAP计算失败也有数据
            # 如果战略层数据不足，直接添加当前基差作为备用
            if len(self.strategic_basis_history) < self.strategic_window:
                # 检查是否是新的分钟数据
                if not self.strategic_timestamps or minute_boundary > self.strategic_timestamps[-1]:
                    self.strategic_basis_history.append(basis)
                    self.strategic_timestamps.append(minute_boundary)
                    self.logger.debug(f"📊 战略层实时备份: {basis:.6f} @ {minute_boundary}")

            # 2. 更新战术层数据（秒级聚合）
            second_boundary = current_time.replace(microsecond=0)
            
            if self.last_second_boundary != second_boundary:
                # 新的秒，计算上一秒的VWAP并添加到战术层
                if self.last_second_boundary is not None:
                    second_vwap_basis = self._calculate_second_vwap_basis(self.last_second_boundary)
                    if second_vwap_basis is not None:
                        self.tactical_basis_history.append(second_vwap_basis)
                        self.tactical_timestamps.append(self.last_second_boundary)
                        
                        self.logger.debug(f"⚡ 战术层更新: {second_vwap_basis:.6f} @ {self.last_second_boundary}")
                
                self.last_second_boundary = second_boundary
                self.second_vwap_cache = {}  # 清空缓存
            
            # 累积当前秒的数据
            second_key = second_boundary.isoformat()
            if second_key not in self.second_vwap_cache:
                self.second_vwap_cache[second_key] = []
            self.second_vwap_cache[second_key].append({
                'basis': basis,
                'timestamp': current_time,
                'spot_price': self.current_spot_price,
                'futures_price': self.current_futures_price
            })
            
        except Exception as e:
            self.logger.error(f"更新分层布林带数据时发生错误: {e}")
    
    def _calculate_minute_vwap_basis(self, minute_boundary: datetime) -> Optional[float]:
        """计算指定分钟的VWAP基差"""
        try:
            minute_key = minute_boundary.isoformat()
            data_points = self.minute_vwap_cache.get(minute_key, [])
            
            if not data_points:
                return None
            
            # 简化VWAP计算：等权重平均（实际应该基于成交量加权）
            basis_sum = sum(point['basis'] for point in data_points)
            return basis_sum / len(data_points)
            
        except Exception as e:
            self.logger.error(f"计算分钟VWAP基差时发生错误: {e}")
            return None
    
    def _calculate_second_vwap_basis(self, second_boundary: datetime) -> Optional[float]:
        """计算指定秒的VWAP基差"""
        try:
            second_key = second_boundary.isoformat()
            data_points = self.second_vwap_cache.get(second_key, [])
            
            if not data_points:
                return None
            
            # 简化VWAP计算：等权重平均（实际应该基于成交量加权）
            basis_sum = sum(point['basis'] for point in data_points)
            return basis_sum / len(data_points)
            
        except Exception as e:
            self.logger.error(f"计算秒级VWAP基差时发生错误: {e}")
            return None

    def _check_data_continuity(self):
        """检查数据连续性，诊断数据流问题"""
        try:
            current_time = datetime.now()

            # 检查战略层数据的时间连续性
            if len(self.strategic_timestamps) >= 2:
                last_timestamp = self.strategic_timestamps[-1]
                time_gap = (current_time - last_timestamp).total_seconds()

                # 如果超过5分钟没有新数据，发出警告
                if time_gap > 300:  # 5分钟
                    self.logger.warning(f"⚠️ 战略层数据可能断流: 最后更新时间 {last_timestamp}, 距今 {time_gap:.1f}秒")

                    # 检查是否需要强制添加当前数据
                    if self.current_basis is not None and time_gap > 120:  # 超过2分钟
                        minute_boundary = current_time.replace(second=0, microsecond=0)
                        if minute_boundary > last_timestamp:
                            self.strategic_basis_history.append(self.current_basis)
                            self.strategic_timestamps.append(minute_boundary)
                            self.logger.info(f"🔧 强制添加战略层数据: {self.current_basis:.6f} @ {minute_boundary}")

            # 检查数据质量
            if len(self.strategic_basis_history) >= 5:
                recent_data = list(self.strategic_basis_history)[-5:]
                if len(set(recent_data)) == 1:  # 所有数据都相同
                    self.logger.warning(f"⚠️ 检测到战略层数据停滞: 最近5个数据点都是 {recent_data[0]:.6f}")

        except Exception as e:
            self.logger.error(f"数据连续性检查失败: {e}")

    def _calculate_layered_bollinger_bands(self) -> bool:
        """计算分层布林带指标 - 增强版本，确保持续稳定计算

        Returns:
            bool: 是否成功计算了至少一层布林带
        """
        try:
            strategic_data_len = len(self.strategic_basis_history)
            tactical_data_len = len(self.tactical_basis_history)

            # 🔥 新增：数据连续性检查
            self._check_data_continuity()

            # 详细状态检查
            self.logger.debug(f"📊 布林带计算检查: "
                            f"战略层数据={strategic_data_len}个 (需要{self.strategic_window}个), "
                            f"战术层数据={tactical_data_len}个 (需要{self.tactical_window}个)")

            calculation_success = False

            # 🔥 关键修复：战略层布林带强制计算机制
            strategic_calculated = False
            if strategic_data_len >= self.strategic_window:
                try:
                    recent_strategic = list(self.strategic_basis_history)[-self.strategic_window:]
                    strategic_array = np.array(recent_strategic, dtype=np.float64)
                    
                    # 数据验证
                    if len(strategic_array) == self.strategic_window and not np.any(np.isnan(strategic_array)):
                        old_middle = self.strategic_bb_middle
                        
                        self.strategic_bb_middle = np.mean(strategic_array)
                        strategic_std = np.std(strategic_array, ddof=1)

                        # 🔥 关键修复：处理极低波动性情况
                        min_volatility = 1e-6  # 设置最小波动性阈值
                        if strategic_std < min_volatility:
                            strategic_std = min_volatility
                            self.logger.warning(f"⚠️ 检测到极低波动性，使用最小波动性: {min_volatility:.6f}")

                        self.strategic_bb_upper = self.strategic_bb_middle + (self.strategic_std_dev * strategic_std)
                        self.strategic_bb_lower = self.strategic_bb_middle - (self.strategic_std_dev * strategic_std)

                        # 🔥 修复变化检测阈值和日志策略
                        change_threshold = 1e-6  # 放宽变化阈值
                        significant_change = old_middle is None or abs(self.strategic_bb_middle - old_middle) > change_threshold

                        # 🔥 改进：每5次计算强制输出一次状态，并增加时间间隔检查
                        if not hasattr(self, '_bb_calculation_count'):
                            self._bb_calculation_count = 0
                        if not hasattr(self, '_last_bb_log_time'):
                            self._last_bb_log_time = datetime.now()

                        self._bb_calculation_count += 1
                        current_time = datetime.now()
                        time_since_last_log = (current_time - self._last_bb_log_time).total_seconds()

                        # 强制输出条件：每5次计算 或 超过30秒未输出日志
                        force_log = (self._bb_calculation_count % 5 == 0) or (time_since_last_log >= 30)

                        if significant_change or force_log:
                            if significant_change:
                                change_indicator = "📈 变化" if old_middle else "🆕 首次"
                            else:
                                change_indicator = "🔄 稳定"

                            self.logger.info(f"🎯 战略层布林带计算完成 {change_indicator}: "
                                           f"中轨={self.strategic_bb_middle:.6f}, "
                                           f"上轨={self.strategic_bb_upper:.6f}, "
                                           f"下轨={self.strategic_bb_lower:.6f}, "
                                           f"波动性={strategic_std:.6f}")

                            # 更新最后日志输出时间
                            self._last_bb_log_time = current_time
                        else:
                            self.logger.debug(f"✅ 战略层布林带数值稳定: 中轨={self.strategic_bb_middle:.6f}")
                            
                        strategic_calculated = True
                        calculation_success = True
                    else:
                        self.logger.warning(f"⚠️ 战略层数据质量问题: 长度={len(strategic_array)}, 包含NaN={np.any(np.isnan(strategic_array))}")

                except Exception as strategic_e:
                    self.logger.error(f"战略层布林带计算异常: {strategic_e}")
            else:
                # 数据不足的详细诊断
                missing_count = self.strategic_window - strategic_data_len
                self.logger.debug(f"⚠️ 战略层数据不足: 还需要{missing_count}个数据点")
                
                # 如果数据接近充足，显示最近的基差数据用于调试
                if strategic_data_len > 0:
                    recent_data = list(self.strategic_basis_history)[-min(5, strategic_data_len):]
                    self.logger.debug(f"📊 最近战略层基差: {[f'{x:.6f}' for x in recent_data]}")

            # 战术层布林带计算（保持原有逻辑但增强错误处理）
            tactical_calculated = False
            if tactical_data_len >= self.tactical_window:
                try:
                    recent_tactical = list(self.tactical_basis_history)[-self.tactical_window:]
                    tactical_array = np.array(recent_tactical, dtype=np.float64)
                    
                    if len(tactical_array) == self.tactical_window and not np.any(np.isnan(tactical_array)):
                        self.tactical_bb_middle = np.mean(tactical_array)
                        tactical_std = np.std(tactical_array, ddof=1)
                        self.tactical_bb_upper = self.tactical_bb_middle + (self.tactical_std_dev * tactical_std)
                        self.tactical_bb_lower = self.tactical_bb_middle - (self.tactical_std_dev * tactical_std)

                        self.logger.debug(f"⚡ 战术层布林带计算完成: "
                                        f"中轨={self.tactical_bb_middle:.6f}, "
                                        f"上轨={self.tactical_bb_upper:.6f}, "
                                        f"下轨={self.tactical_bb_lower:.6f}")
                        tactical_calculated = True
                        calculation_success = True
                    else:
                        self.logger.warning(f"⚠️ 战术层数据质量问题")

                except Exception as tactical_e:
                    self.logger.error(f"战术层布林带计算异常: {tactical_e}")
            else:
                self.logger.debug(f"⚠️ 战术层数据不足: {tactical_data_len}/{self.tactical_window}")

            # 计算成功率统计
            if hasattr(self, '_bollinger_calculation_count'):
                self._bollinger_calculation_count += 1
                if strategic_calculated:
                    self._strategic_success_count = getattr(self, '_strategic_success_count', 0) + 1
            else:
                self._bollinger_calculation_count = 1
                self._strategic_success_count = 1 if strategic_calculated else 0
                
            # 每100次计算输出成功率统计
            if self._bollinger_calculation_count % 100 == 0:
                success_rate = (self._strategic_success_count / self._bollinger_calculation_count) * 100
                self.logger.info(f"📊 布林带计算统计: 成功率={success_rate:.1f}% ({self._strategic_success_count}/{self._bollinger_calculation_count})")

            return calculation_success

        except Exception as e:
            self.logger.error(f"计算分层布林带时发生严重错误: {e}")
            # 确保系统继续运行，不因布林带计算失败而中断
            import traceback
            self.logger.error(f"布林带计算错误堆栈: {traceback.format_exc()}")
            return False
            
    def _should_force_bollinger_calculation(self, current_time: datetime) -> bool:
        """检查是否需要强制计算布林带"""
        try:
            # 如果从未强制计算过，立即进行
            if self.last_forced_calculation_time is None:
                return True
            
            # 检查距离上次强制计算的时间
            time_since_forced = (current_time - self.last_forced_calculation_time).total_seconds()
            if time_since_forced >= self.forced_calculation_interval:
                return True
            
            # 检查距离上次成功计算的时间
            if self.last_successful_calculation_time:
                time_since_success = (current_time - self.last_successful_calculation_time).total_seconds()
                if time_since_success >= self.force_calculation_threshold:
                    self.logger.warning(f"⏰ 布林带计算已超过{self.force_calculation_threshold}秒未成功，触发强制计算")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查强制计算条件失败: {e}")
            return False
    
    def _calculate_layered_bollinger_bands_with_force_check(self, force_calculation: bool = False) -> bool:
        """带强制检查的分层布林带计算"""
        try:
            # 正常计算
            self._calculate_layered_bollinger_bands()
            
            # 检查是否成功计算了战略层布林带
            has_strategic_values = (
                self.strategic_bb_middle is not None and 
                self.strategic_bb_upper is not None and 
                self.strategic_bb_lower is not None
            )
            
            # 如果需要强制计算且当前数据不足，尝试强制计算
            if force_calculation and not has_strategic_values:
                self.logger.info("🔧 执行强制布林带计算...")
                self._force_calculate_bollinger_bands()
                self.last_forced_calculation_time = datetime.now()
                
                # 再次检查是否成功
                has_strategic_values = (
                    self.strategic_bb_middle is not None and 
                    self.strategic_bb_upper is not None and 
                    self.strategic_bb_lower is not None
                )
            
            return has_strategic_values
            
        except Exception as e:
            self.logger.error(f"强制检查布林带计算失败: {e}")
            return False
    
    def _force_calculate_bollinger_bands(self):
        """强制计算布林带 - 使用可用数据进行计算"""
        try:
            strategic_data_len = len(self.strategic_basis_history)
            
            # 如果有任何实时数据，尝试计算
            if strategic_data_len >= 5:  # 至少需要5个数据点
                # 使用可用的所有数据
                available_data = list(self.strategic_basis_history)
                strategic_array = np.array(available_data, dtype=np.float64)
                
                if not np.any(np.isnan(strategic_array)):
                    self.strategic_bb_middle = np.mean(strategic_array)
                    strategic_std = np.std(strategic_array, ddof=1)

                    # 🔥 强制计算中也应用最小波动性保护
                    min_volatility = 1e-6
                    if strategic_std < min_volatility:
                        strategic_std = min_volatility
                        self.logger.warning(f"⚠️ 强制计算中检测到极低波动性，使用最小波动性: {min_volatility:.6f}")

                    self.strategic_bb_upper = self.strategic_bb_middle + (self.strategic_std_dev * strategic_std)
                    self.strategic_bb_lower = self.strategic_bb_middle - (self.strategic_std_dev * strategic_std)

                    self.logger.warning(f"🔧 强制计算布林带成功: 使用{strategic_data_len}个数据点 "
                                      f"(标准需要{self.strategic_window}个), "
                                      f"中轨={self.strategic_bb_middle:.6f}, 波动性={strategic_std:.6f}")
                    return True
            
            # 如果实时数据不足，尝试从预处理器获取数据
            try:
                minute_aggregates = self.data_preprocessor.get_aggregated_data("minute", max(10, strategic_data_len))
                if minute_aggregates and len(minute_aggregates) >= 5:
                    basis_values = [agg["basis"] for agg in minute_aggregates]
                    strategic_array = np.array(basis_values, dtype=np.float64)
                    
                    if not np.any(np.isnan(strategic_array)):
                        self.strategic_bb_middle = np.mean(strategic_array)
                        strategic_std = np.std(strategic_array, ddof=1)

                        # 🔥 聚合数据强制计算中也应用最小波动性保护
                        min_volatility = 1e-6
                        if strategic_std < min_volatility:
                            strategic_std = min_volatility
                            self.logger.warning(f"⚠️ 聚合数据强制计算中检测到极低波动性，使用最小波动性: {min_volatility:.6f}")

                        self.strategic_bb_upper = self.strategic_bb_middle + (self.strategic_std_dev * strategic_std)
                        self.strategic_bb_lower = self.strategic_bb_middle - (self.strategic_std_dev * strategic_std)

                        self.logger.warning(f"🔧 强制计算布林带成功: 使用聚合数据{len(minute_aggregates)}个点, "
                                          f"中轨={self.strategic_bb_middle:.6f}, 波动性={strategic_std:.6f}")
                        return True
            except Exception as agg_e:
                self.logger.debug(f"聚合数据强制计算失败: {agg_e}")
            
            self.logger.warning("🚨 强制计算布林带失败: 数据不足或质量问题")
            return False
            
        except Exception as e:
            self.logger.error(f"强制计算布林带时发生错误: {e}")
            return False
    
    def _calculate_technical_indicators_incremental(self, high_price: Optional[float], 
                                                  low_price: Optional[float], close_price: float):
        """
        增量计算技术指标：ATR和ADX - 高性能版本（增强调试）
        
        Args:
            high_price: 当前最高价
            low_price: 当前最低价  
            close_price: 当前收盘价（使用现货价格）
        """
        try:
            # 使用实际高低价，如果没有提供则使用收盘价
            actual_high = high_price if high_price is not None else close_price
            actual_low = low_price if low_price is not None else close_price
            
            # 增量更新所有指标
            indicators = self.indicator_manager.update_all(
                high=actual_high,
                low=actual_low, 
                close=close_price
            )
            
            # 更新当前值（向后兼容）
            self.current_atr = indicators['atr']
            self.current_adx = indicators['adx']
            
            # 获取增强的调试信息
            dashboard_values = self.indicator_manager.get_dashboard_ready_values()
            
            # 🔥 增强调试日志 - 更频繁的状态输出用于排查问题
            if self.total_updates % 5 == 0:  # 每5次更新显示一次状态（提高频率）
                if not self.indicator_manager.is_all_ready():
                    self.logger.info(
                        f"🔄 [{self.pair_name}] 技术指标初始化中: {dashboard_values['status_message']} "
                        f"(ATR: {dashboard_values['atr_data_count']}/{self.indicator_manager.atr_calculator.period}, "
                        f"ADX: {dashboard_values['adx_data_count']}/{self.indicator_manager.adx_calculator.period}) "
                        f"- 总更新#{self.total_updates}"
                    )
                else:
                    # 所有指标就绪时的详细日志
                    plus_di = indicators.get('plus_di')
                    minus_di = indicators.get('minus_di')
                    atr_str = f"{self.current_atr:.6f}" if self.current_atr is not None else "N/A"
                    adx_str = f"{self.current_adx:.2f}" if self.current_adx is not None else "N/A"
                    plus_di_str = f"{plus_di:.2f}" if plus_di is not None else "N/A"
                    minus_di_str = f"{minus_di:.2f}" if minus_di is not None else "N/A"
                    self.logger.info(  # 改为INFO级别便于观察
                        f"📈 [{self.pair_name}] 技术指标就绪 - ATR: {atr_str}, "
                        f"ADX: {adx_str}, +DI: {plus_di_str}, -DI: {minus_di_str} "
                        f"(更新#{indicators['update_count']}/总#{self.total_updates})"
                    )
            
            # 首次完成初始化时的特殊日志
            if (dashboard_values['all_ready'] and 
                hasattr(self, '_first_indicators_ready') and 
                not self._first_indicators_ready):
                self._first_indicators_ready = True
                atr_init_str = f"{self.current_atr:.6f}" if self.current_atr is not None else "N/A"
                adx_init_str = f"{self.current_adx:.2f}" if self.current_adx is not None else "N/A"
                self.logger.info(
                    f"✅ 技术指标初始化完成! ATR: {atr_init_str}, "
                    f"ADX: {adx_init_str} (数据量: ATR={dashboard_values['atr_data_count']}, "
                    f"ADX={dashboard_values['adx_data_count']})"
                )
                
        except Exception as e:
            self.logger.error(f"增量计算技术指标时发生错误: {e}")
            # 回退到传统计算
            self._calculate_technical_indicators()

    def _calculate_technical_indicators(self):
        """计算技术指标：ATR和ADX - 传统方法（回退方案）"""
        try:
            # 计算ATR（平均真实范围）
            if len(self.high_prices) >= self.volatility_filter.get("MIN_ATR_PERIODS", 14):
                self._calculate_atr()
            
            # 计算ADX（趋势强度指标）
            if len(self.high_prices) >= self.trend_filter.get("ADX_PERIODS", 14):
                self._calculate_adx()
                
        except Exception as e:
            self.logger.error(f"计算技术指标时发生错误: {e}")
    
    def _calculate_atr(self):
        """计算ATR（平均真实范围）- 使用pandas-ta库或回退方案"""
        try:
            periods = self.volatility_filter.get("MIN_ATR_PERIODS", 14)
            if len(self.high_prices) < periods:
                return
            
            if PANDAS_TA_AVAILABLE:
                # 使用pandas-ta计算ATR
                df_data = {
                    'high': list(self.high_prices),
                    'low': list(self.low_prices),
                    'close': list(self.close_prices)
                }
                df = pd.DataFrame(df_data)
                
                atr_series = ta.atr(high=df['high'], low=df['low'], close=df['close'], length=periods)
                
                if not atr_series.empty and not pd.isna(atr_series.iloc[-1]):
                    self.current_atr = atr_series.iloc[-1]
            else:
                # 回退到传统ATR计算
                self.current_atr = self._calculate_atr_fallback(periods)
            
            if self.current_atr and self.total_updates % 100 == 0:
                method = "pandas-ta" if PANDAS_TA_AVAILABLE else "fallback"
                self.logger.debug(f"ATR更新 ({method}): {self.current_atr:.6f} (周期: {periods})")
            
        except Exception as e:
            self.logger.error(f"计算ATR时发生错误: {e}")
            # 如果pandas-ta失败，尝试回退方案
            if PANDAS_TA_AVAILABLE:
                try:
                    periods = self.volatility_filter.get("MIN_ATR_PERIODS", 14)
                    self.current_atr = self._calculate_atr_fallback(periods)
                except Exception as fallback_e:
                    self.logger.error(f"ATR回退计算也失败: {fallback_e}")

    def _calculate_atr_fallback(self, periods: int) -> Optional[float]:
        """ATR回退计算方法"""
        try:
            if len(self.high_prices) < periods:
                return None
            
            # 获取最近periods个数据
            highs = np.array(list(self.high_prices)[-periods:])
            lows = np.array(list(self.low_prices)[-periods:])
            closes = np.array(list(self.close_prices)[-periods:])
            
            # 计算真实范围
            tr1 = highs[1:] - lows[1:]  # 当日最高价 - 当日最低价
            tr2 = np.abs(highs[1:] - closes[:-1])  # |当日最高价 - 昨日收盘价|
            tr3 = np.abs(lows[1:] - closes[:-1])   # |当日最低价 - 昨日收盘价|
            
            true_range = np.maximum(tr1, np.maximum(tr2, tr3))
            return np.mean(true_range)
            
        except Exception as e:
            self.logger.error(f"ATR回退计算失败: {e}")
            return None
    
    def _calculate_adx(self):
        """计算ADX（趋势强度指标）- 使用pandas-ta库或回退方案"""
        try:
            periods = self.trend_filter.get("ADX_PERIODS", 14)
            if len(self.high_prices) < periods:
                return
            
            if PANDAS_TA_AVAILABLE:
                # 使用pandas-ta计算ADX
                df_data = {
                    'high': list(self.high_prices),
                    'low': list(self.low_prices),
                    'close': list(self.close_prices)
                }
                df = pd.DataFrame(df_data)
                
                adx_series = ta.adx(high=df['high'], low=df['low'], close=df['close'], length=periods)
                
                if adx_series is not None and not adx_series.empty:
                    adx_column = f'ADX_{periods}'
                    if adx_column in adx_series.columns:
                        latest_adx = adx_series[adx_column].iloc[-1]
                        if not pd.isna(latest_adx):
                            self.current_adx = latest_adx
            else:
                # 回退到简化ADX计算
                self.current_adx = self._calculate_adx_fallback(periods)
            
            if self.current_adx and self.total_updates % 100 == 0:
                method = "pandas-ta" if PANDAS_TA_AVAILABLE else "fallback"
                self.logger.debug(f"ADX更新 ({method}): {self.current_adx:.2f} (周期: {periods})")
            
        except Exception as e:
            self.logger.error(f"计算ADX时发生错误: {e}")
            # 如果pandas-ta失败，尝试回退方案
            if PANDAS_TA_AVAILABLE:
                try:
                    periods = self.trend_filter.get("ADX_PERIODS", 14)
                    self.current_adx = self._calculate_adx_fallback(periods)
                except Exception as fallback_e:
                    self.logger.error(f"ADX回退计算也失败: {fallback_e}")

    def _calculate_adx_fallback(self, periods: int) -> Optional[float]:
        """ADX回退计算方法（简化版本）"""
        try:
            if len(self.close_prices) < periods:
                return None
            
            # 简化的ADX计算：基于价格变化的趋势强度
            closes = np.array(list(self.close_prices)[-periods:])
            
            # 计算价格变化
            price_changes = np.diff(closes)
            
            if len(price_changes) > 1:
                # 计算趋势一致性
                positive_changes = price_changes[price_changes > 0]
                negative_changes = price_changes[price_changes < 0]
                
                # 简单的趋势强度计算
                total_movement = np.sum(np.abs(price_changes))
                directional_movement = abs(len(positive_changes) - len(negative_changes))
                
                if total_movement > 0:
                    # 将结果缩放到0-100范围
                    trend_strength = (directional_movement / len(price_changes)) * 100
                    return min(trend_strength, 100)
                
            return 0
            
        except Exception as e:
            self.logger.error(f"ADX回退计算失败: {e}")
            return None
    
    def _generate_graded_signal(self, signal_type: str) -> Optional[Dict[str, Any]]:
        """
        生成分级风险评估信号 - 支持过滤器开关控制
        
        Args:
            signal_type: 基础信号类型 ('short_futures_long_spot')
            
        Returns:
            包含风险评级的信号字典，如果不通过风险过滤则返回None
        """
        try:
            # 初始化风险因子
            risk_factors = {
                "volatility_check": True,
                "trend_check": True,
                "basis_quality": "high"
            }
            
            # 初始评级为A级（最优）
            risk_grade = "A"
            confidence = 1.0
            
            # 获取过滤器开关状态
            enable_atr = self.filter_controls.get("ENABLE_ATR_FILTER", True)
            enable_adx = self.filter_controls.get("ENABLE_ADX_FILTER", True)
            enable_basis_quality = self.filter_controls.get("ENABLE_BASIS_QUALITY_FILTER", True)
            
            # ATR波动率过滤器（可选）
            if enable_atr:
                volatility_risk = self._check_atr_volatility_filter()
                if volatility_risk["risk_level"] == "high":
                    risk_grade = "B"  # 降级到B级
                    confidence *= 0.7
                    risk_factors["volatility_check"] = False
                    risk_factors["volatility_details"] = volatility_risk
                    self.logger.debug(f"ATR过滤器: 高波动率风险，降级到B级")
                elif volatility_risk["risk_level"] == "extreme":
                    # 极高波动率，拒绝信号
                    self.logger.warning(f"拒绝信号 - 波动率过高: ATR={volatility_risk.get('atr_value', 'N/A')}")
                    return None
            else:
                risk_factors["volatility_check"] = "disabled"
                self.logger.debug("ATR波动率过滤器已禁用")
            
            # ADX趋势强度过滤器（可选）
            if enable_adx:
                trend_risk = self._check_adx_trend_filter()
                if trend_risk["trend_strength"] == "weak":
                    if risk_grade == "A":
                        risk_grade = "B"  # 降级到B级
                    confidence *= 0.8
                    risk_factors["trend_check"] = False
                    risk_factors["trend_details"] = trend_risk
                    self.logger.debug(f"ADX过滤器: 趋势较弱，降级到B级")
                elif trend_risk["trend_strength"] == "very_weak":
                    # 趋势过弱，拒绝信号
                    self.logger.warning(f"拒绝信号 - 趋势过弱: ADX={trend_risk.get('adx_value', 'N/A')}")
                    return None
            else:
                risk_factors["trend_check"] = "disabled"
                self.logger.debug("ADX趋势强度过滤器已禁用")
            
            # 基差质量过滤器（可选）
            if enable_basis_quality:
                basis_quality = self._assess_basis_quality()
                risk_factors["basis_quality"] = basis_quality["quality"]
                
                if basis_quality["quality"] == "medium":
                    if risk_grade == "A":
                        risk_grade = "B"
                    confidence *= 0.9
                    self.logger.debug(f"基差质量过滤器: 中等质量，降级到B级")
                elif basis_quality["quality"] == "low":
                    # 基差质量过低，拒绝信号
                    self.logger.warning(f"拒绝信号 - 基差质量过低: {basis_quality}")
                    return None
            else:
                risk_factors["basis_quality"] = "disabled"
                self.logger.debug("基差质量过滤器已禁用")
            
            return {
                "signal_type": signal_type,
                "risk_grade": risk_grade,
                "confidence": confidence,
                "risk_factors": risk_factors,
                "technical_indicators": {
                    "current_atr": self.current_atr,
                    "current_adx": self.current_adx,
                    "basis": self.current_basis,
                    "bb_position": self._get_bollinger_position()
                }
            }
            
        except Exception as e:
            self.logger.error(f"生成分级信号时发生错误: {e}")
            return None
    
    def _check_atr_volatility_filter(self) -> Dict[str, Any]:
        """检查ATR波动率过滤器"""
        try:
            if self.current_atr is None:
                return {"risk_level": "unknown", "reason": "ATR数据不足"}
            
            # 获取波动率阈值
            atr_multiplier = self.volatility_filter.get("ATR_MULTIPLIER", 2.0)
            high_vol_threshold = self.volatility_filter.get("HIGH_VOLATILITY_THRESHOLD", 0.02)
            
            # 基于当前价格计算相对ATR
            if self.current_spot_price:
                relative_atr = self.current_atr / self.current_spot_price
            else:
                relative_atr = self.current_atr  # 回退到绝对值
            
            result = {
                "atr_value": self.current_atr,
                "relative_atr": relative_atr,
                "threshold": high_vol_threshold
            }
            
            if relative_atr > high_vol_threshold * atr_multiplier:
                result["risk_level"] = "extreme"
                result["reason"] = "极高波动率"
            elif relative_atr > high_vol_threshold:
                result["risk_level"] = "high"
                result["reason"] = "高波动率"
            else:
                result["risk_level"] = "normal"
                result["reason"] = "正常波动率"
            
            return result
            
        except Exception as e:
            self.logger.error(f"检查ATR波动率过滤器时发生错误: {e}")
            return {"risk_level": "unknown", "reason": f"计算错误: {e}"}
    
    def _check_adx_trend_filter(self) -> Dict[str, Any]:
        """检查ADX趋势强度过滤器"""
        try:
            if self.current_adx is None:
                return {"trend_strength": "unknown", "reason": "ADX数据不足"}
            
            # 获取趋势强度阈值
            adx_threshold = self.trend_filter.get("ADX_THRESHOLD", 25)
            strong_trend_threshold = self.trend_filter.get("STRONG_TREND_THRESHOLD", 30)
            
            result = {
                "adx_value": self.current_adx,
                "threshold": adx_threshold,
                "strong_threshold": strong_trend_threshold
            }
            
            if self.current_adx < adx_threshold * 0.5:
                result["trend_strength"] = "very_weak"
                result["reason"] = "趋势极弱"
            elif self.current_adx < adx_threshold:
                result["trend_strength"] = "weak"
                result["reason"] = "趋势较弱"
            elif self.current_adx >= strong_trend_threshold:
                result["trend_strength"] = "strong"
                result["reason"] = "强趋势"
            else:
                result["trend_strength"] = "normal"
                result["reason"] = "中等趋势"
            
            return result
            
        except Exception as e:
            self.logger.error(f"检查ADX趋势过滤器时发生错误: {e}")
            return {"trend_strength": "unknown", "reason": f"计算错误: {e}"}
    
    def _assess_basis_quality(self) -> Dict[str, Any]:
        """评估基差质量"""
        try:
            if not self._has_layered_sufficient_data():
                return {"quality": "unknown", "reason": "数据不足"}
            
            # 计算基差偏离程度（使用分层布林带中轨）
            middle_band = self._get_dynamic_middle_band()
            basis_deviation = abs(self.current_basis - middle_band)
            threshold = self.basis_entry_threshold
            
            result = {
                "basis_deviation": basis_deviation,
                "threshold": threshold,
                "basis_value": self.current_basis
            }
            
            if basis_deviation >= threshold * 2:
                result["quality"] = "high"
                result["reason"] = "基差偏离显著"
            elif basis_deviation >= threshold:
                result["quality"] = "medium"
                result["reason"] = "基差偏离适中"
            else:
                result["quality"] = "low"
                result["reason"] = "基差偏离不足"
            
            return result
            
        except Exception as e:
            self.logger.error(f"评估基差质量时发生错误: {e}")
            return {"quality": "unknown", "reason": f"计算错误: {e}"}
    
    def _get_bollinger_position(self) -> str:
        """获取当前基差在分层布林带中的位置"""
        if not self._has_layered_sufficient_data():
            return "unknown"
        
        # 使用战略层布林带判断位置
        if self.strategic_bb_upper and self.current_basis >= self.strategic_bb_upper:
            return "above_upper"
        elif self.strategic_bb_lower and self.current_basis <= self.strategic_bb_lower:
            return "below_lower"
        elif self.strategic_bb_middle and self.current_basis > self.strategic_bb_middle:
            return "upper_half"
        else:
            return "lower_half"
    
    def check_entry_signal(self) -> Optional[Dict[str, Any]]:
        """
        分层布林带入场信号检查 - 战略预警 + 战术确认流程
        
        Returns:
            Dict: 包含信号类型和风险评级的字典，或None如果没有信号
        """
        if not self._has_sufficient_data():
            return None
        
        try:
            return self._check_layered_entry_signal()
            
        except Exception as e:
            self.logger.error(f"检查分层入场信号时发生错误: {e}")
            return None
    
    def _check_layered_entry_signal(self) -> Optional[Dict[str, Any]]:
        """分层布林带信号生成核心逻辑"""
        # 检查数据充分性
        if not self._has_layered_sufficient_data():
            return None  # 分层数据不足，等待更多数据
        
        current_basis = self.current_basis
        
        # 步骤1: 战略预警 (Strategic Alert)
        strategic_alert = self._check_strategic_alert(current_basis)
        
        if not strategic_alert:
            # 重置状态到待机
            if self.strategy_state != "standby":
                self.strategy_state = "standby"
                self.strategic_signal_active = False
                self.tactical_signal_confirmed = False
                self.logger.debug("🔄 策略状态重置为待机")
            return None
        
        # 步骤2: 战术确认 (Tactical Confirmation)
        if strategic_alert:
            tactical_confirmation = self._check_tactical_confirmation(current_basis)
            
            if tactical_confirmation:
                # 步骤3: 微观结构最终确认 (Microstructure Validation)
                signal_type = tactical_confirmation["signal_type"]
                
                # 生成带风险评级的最终信号
                graded_signal = self._generate_graded_signal(signal_type)
                
                if graded_signal:
                    # 添加分层信号信息
                    graded_signal.update({
                        "layered_signal": True,
                        "strategic_level": strategic_alert,
                        "tactical_level": tactical_confirmation,
                        "signal_flow": "Strategic Alert → Tactical Confirmation → Microstructure Validation"
                    })
                    
                    self.signal_count["entry"] += 1
                    self.logger.info_structured(
                        f"🎯 分层信号确认 - {signal_type}",
                        event_type="layered_signal_generated",
                        metrics={
                            "basis": current_basis,
                            "strategic_bb_upper": self.strategic_bb_upper,
                            "tactical_bb_upper": self.tactical_bb_upper,
                            "confidence": graded_signal['confidence']
                        },
                        details={
                            "strategy_state": self.strategy_state,
                            "risk_grade": graded_signal['risk_grade'],
                            "signal_flow": graded_signal["signal_flow"]
                        }
                    )
                    
                    return graded_signal
            
            return None
            
        return None
    
    def _check_strategic_alert(self, current_basis: float) -> Optional[Dict[str, Any]]:
        """战略层预警检查 - 机会过滤器"""
        if self.strategic_bb_upper is None or self.strategic_bb_middle is None:
            return None
        
        # 战略条件：基差突破战略层上轨
        if current_basis > self.strategic_bb_upper and current_basis > 0:
            if not self.strategic_signal_active:
                self.strategy_state = "strategic_alert"
                self.strategic_signal_active = True
                self.logger.info(
                    f"🚨 战略预警激活 - 基差:{current_basis:.6f} > 战略上轨:{self.strategic_bb_upper:.6f}"
                )
            
            return {
                "level": "strategic",
                "triggered": True,
                "basis": current_basis,
                "threshold": self.strategic_bb_upper,
                "deviation": current_basis - self.strategic_bb_middle
            }
        
        return None
    
    def _check_tactical_confirmation(self, current_basis: float) -> Optional[Dict[str, Any]]:
        """战术层确认检查 - 精准时机选择"""
        if not self.strategic_signal_active:
            return None
        
        if self.tactical_bb_upper is None or self.tactical_bb_middle is None:
            return None
        
        # 战术条件：在战略预警状态下，基差突破战术层上轨
        if current_basis > self.tactical_bb_upper:
            if not self.tactical_signal_confirmed:
                self.strategy_state = "tactical_ready"
                self.tactical_signal_confirmed = True
                self.logger.info(
                    f"⚡ 战术确认完成 - 基差:{current_basis:.6f} > 战术上轨:{self.tactical_bb_upper:.6f}"
                )
            
            return {
                "level": "tactical",
                "triggered": True,
                "signal_type": "short_futures_long_spot",
                "basis": current_basis,
                "threshold": self.tactical_bb_upper,
                "tactical_deviation": current_basis - self.tactical_bb_middle
            }
        
        return None
    
    # 传统布林带回退信号已移除 - 完全使用分层布林带系统
    
    def _has_layered_sufficient_data(self) -> bool:
        """检查分层布林带是否有足够数据"""
        strategic_sufficient = len(self.strategic_basis_history) >= self.strategic_window
        tactical_sufficient = len(self.tactical_basis_history) >= self.tactical_window
        bb_calculated = self.strategic_bb_upper is not None and self.tactical_bb_upper is not None

        result = strategic_sufficient and tactical_sufficient and bb_calculated

        # 调试信息
        if not result:
            self.logger.debug(f"🔍 分层数据检查: "
                            f"战略层={len(self.strategic_basis_history)}/{self.strategic_window} "
                            f"({'✅' if strategic_sufficient else '❌'}), "
                            f"战术层={len(self.tactical_basis_history)}/{self.tactical_window} "
                            f"({'✅' if tactical_sufficient else '❌'}), "
                            f"布林带计算={'✅' if bb_calculated else '❌'}")

        return result

    def _has_data_for_calculation(self) -> bool:
        """检查是否有足够数据进行布林带计算（不依赖已计算的布林带值）"""
        strategic_sufficient = len(self.strategic_basis_history) >= self.strategic_window
        tactical_sufficient = len(self.tactical_basis_history) >= self.tactical_window

        return strategic_sufficient and tactical_sufficient
    
    def check_net_entry_signal(self, position_size: float, funding_rate: float = 0.0) -> Optional[Dict[str, Any]]:
        """
        基于净利润的主决策函数 - 将所有交易成本和动态费用纳入决策核心
        
        Args:
            position_size: 预估仓位大小（用于成本计算）
            funding_rate: 当前资金费率
            
        Returns:
            Dict: 净利润分析结果
                {
                    "signal_type": "short_futures_long_spot",
                    "risk_grade": "A" | "B",
                    "confidence": float,
                    "net_profit_analysis": {
                        "potential_gross_profit": float,
                        "total_costs": float,
                        "net_profit": float,
                        "profit_margin": float,
                        "funding_impact": float
                    }
                }
                或 None 如果不满足净利润条件
        """
        # 阶段一：毛利筛选 - 快速判断基础套利空间
        gross_signal = self.check_entry_signal()
        if not gross_signal:
            return None  # 不存在基础套利空间，立即终止
        
        # 阶段二：净利分析 - 详细成本收益计算
        try:
            signal_type = gross_signal["signal_type"]
            
            # 计算潜在毛利润（基差的绝对价值）
            potential_gross_profit = abs(self.current_basis) * position_size * self.current_spot_price
            
            # 计算一次性总成本（开仓+平仓的所有费用）
            total_costs = self._calculate_total_cost(position_size, self.current_spot_price, self.current_futures_price)
            
            # 计算周期性资金影响
            funding_impact = self._calculate_funding_impact(signal_type, funding_rate, position_size, self.current_futures_price)
            
            # 计算潜在净影响（毛利润 + 资金影响）
            potential_net_impact = potential_gross_profit + funding_impact
            
            # 应用利润安全边际
            safety_margin = self.config.TRADING_COSTS["PROFIT_SAFETY_MARGIN"]
            required_coverage = total_costs * safety_margin
            
            # 最终净利润判断：潜在净影响 > 须覆盖的总成本
            if potential_net_impact > required_coverage:
                net_profit = potential_net_impact - total_costs
                profit_margin = net_profit / (position_size * self.current_spot_price)
                
                # 生成全链路交易追踪ID
                trace_id = trace_manager.generate_trace_id("trade")
                
                # 记录结构化日志
                self.logger.info_structured(
                    "净利润决策通过，生成交易信号",
                    event_type="signal_generated",
                    metrics={
                        "basis_bps": KPICalculator.basis_bps(self.current_spot_price, self.current_futures_price),
                        "funding_rate_bps": KPICalculator.funding_rate_bps(funding_rate),
                        "potential_gross_profit": potential_gross_profit,
                        "total_costs": total_costs,
                        "net_profit": net_profit,
                        "profit_margin": profit_margin,
                        "funding_impact": funding_impact
                    },
                    details={
                        "signal_type": signal_type,
                        "risk_grade": gross_signal["risk_grade"],
                        "confidence": gross_signal["confidence"],
                        "safety_margin_applied": safety_margin,
                        "position_size": position_size,
                        "spot_price": self.current_spot_price,
                        "futures_price": self.current_futures_price
                    },
                    trace_id=trace_id
                )
                
                # 微观结构信号增强分析
                microstructure_analysis = self._analyze_microstructure_signals(signal_type)
                
                # 基于微观结构调整最终置信度
                final_confidence = self._calculate_enhanced_confidence(
                    gross_signal["confidence"], 
                    microstructure_analysis
                )
                
                # 继承原信号的风险评级和置信度，并添加trace_id
                signal_result = {
                    "signal_type": signal_type,
                    "risk_grade": gross_signal["risk_grade"],
                    "confidence": final_confidence,  # 使用微观结构增强后的置信度
                    "original_confidence": gross_signal["confidence"],  # 保留原始置信度
                    "trace_id": trace_id,  # 添加全链路追踪ID
                    "net_profit_analysis": {
                        "potential_gross_profit": potential_gross_profit,
                        "total_costs": total_costs,
                        "net_profit": net_profit,
                        "profit_margin": profit_margin,
                        "funding_impact": funding_impact,
                        "safety_margin_applied": safety_margin,
                        "required_coverage": required_coverage
                    },
                    "microstructure_analysis": microstructure_analysis
                }
                
                # 记录微观结构增强日志
                if microstructure_analysis.get('available'):
                    self.logger.info_structured(
                        f"微观结构信号增强 - {signal_type}",
                        event_type="microstructure_enhancement",
                        metrics={
                            "original_confidence": gross_signal["confidence"],
                            "enhanced_confidence": final_confidence,
                            "confidence_boost": final_confidence - gross_signal["confidence"],
                            "microstructure_signal": microstructure_analysis.get('micro_signal'),
                            "microstructure_confidence": microstructure_analysis.get('micro_confidence'),
                            "position_size_multiplier": microstructure_analysis.get('position_multiplier', 1.0)
                        },
                        details={
                            "obi_signal": microstructure_analysis.get('obi_signal'),
                            "flow_signal": microstructure_analysis.get('flow_signal'),
                            "enhancement_reason": microstructure_analysis.get('enhancement_reason')
                        },
                        trace_id=trace_id
                    )
                
                return signal_result
            else:
                self.logger.debug(
                    f"❌ 净利润决策失败 - {signal_type}: "
                    f"净影响={potential_net_impact:.4f} < 要求覆盖={required_coverage:.4f}"
                )
                return None
                
        except Exception as e:
            self.logger.error(f"净利润决策计算失败: {e}")
            return None
    
    def _calculate_total_cost(self, position_size: float, spot_price: float, futures_price: float) -> float:
        """
        计算一整轮套利（开仓+平仓）的总成本
        
        Args:
            position_size: 仓位大小
            spot_price: 现货价格
            futures_price: 期货价格
            
        Returns:
            float: 总成本（USDT）
        """
        try:
            costs = self.config.TRADING_COSTS
            
            # 现货交易量和期货合约数量
            spot_notional = position_size * spot_price
            futures_notional = position_size * futures_price  # 简化：假设1:1对冲
            
            # 一次性成本计算：来回（开仓+平仓）的所有费用
            
            # 1. 交易手续费（现货：买入+卖出，期货：卖出+买入）
            taker_fee_rate = costs["TAKER_FEE_RATE"]
            spot_fees = spot_notional * taker_fee_rate * 2  # 开仓买入 + 平仓卖出
            futures_fees = futures_notional * taker_fee_rate * 2  # 开仓卖出 + 平仓买入
            
            # 2. 滑点成本（现货+期货，开仓+平仓）
            slippage_rate = costs["ESTIMATED_SLIPPAGE_RATE"]
            spot_slippage = spot_notional * slippage_rate * 2
            futures_slippage = futures_notional * slippage_rate * 2
            
            # 3. 市场冲击成本
            market_impact_rate = costs["MARKET_IMPACT_RATE"]
            market_impact = (spot_notional + futures_notional) * market_impact_rate
            
            # 总成本
            total_cost = spot_fees + futures_fees + spot_slippage + futures_slippage + market_impact
            
            self.logger.debug(
                f"成本分解: 现货费用={spot_fees:.4f}, 期货费用={futures_fees:.4f}, "
                f"现货滑点={spot_slippage:.4f}, 期货滑点={futures_slippage:.4f}, "
                f"市场冲击={market_impact:.4f}, 总计={total_cost:.4f}"
            )
            
            return total_cost
            
        except Exception as e:
            self.logger.error(f"计算总成本失败: {e}")
            return float('inf')  # 计算失败时返回无穷大，确保信号被拒绝
    
    def _calculate_funding_impact(self, signal_type: str, funding_rate: float, 
                                position_size: float, futures_price: float) -> float:
        """
        计算资金费率在一个结算周期内的影响
        
        Args:
            signal_type: 信号类型 ("short_futures_long_spot")
            funding_rate: 当前资金费率
            position_size: 仓位大小
            futures_price: 期货价格
            
        Returns:
            float: 资金费用影响（正数表示收益，负数表示成本）
        """
        try:
            costs = self.config.TRADING_COSTS
            expected_holding_hours = costs["EXPECTED_HOLDING_HOURS"]
            settlement_periods_per_day = costs["FUNDING_SETTLEMENT_PERIODS_PER_DAY"]
            
            # 计算预期持仓期间的结算次数
            settlement_periods = (expected_holding_hours / 24.0) * settlement_periods_per_day
            
            # 期货持仓名义价值
            futures_notional = position_size * futures_price
            
            # 根据策略类型确定期货仓位方向
            if signal_type == "short_futures_long_spot":
                # 基差套利：买现货，卖期货（期货空头）
                # 当基差扩大时，卖出期货（做空），买入现货（做多）
                futures_position_direction = -1  # 期货空头
            else:
                # 其他策略类型（暂不支持）
                futures_position_direction = 0
            
            # 资金费用影响 = 期货仓位价值 * 资金费率 * 结算次数 * 方向
            # 空头收取资金费，多头支付资金费
            funding_impact = -futures_position_direction * futures_notional * funding_rate * settlement_periods
            
            self.logger.debug(
                f"资金费率影响: 类型={signal_type}, 费率={funding_rate:.6f}, "
                f"期货价值={futures_notional:.2f}, 结算次数={settlement_periods:.2f}, "
                f"影响={funding_impact:.4f}"
            )
            
            return funding_impact
            
        except Exception as e:
            self.logger.error(f"计算资金费率影响失败: {e}")
            return 0.0  # 计算失败时保守返回0
    
    def calculate_min_entry_basis(self, position_size: float, funding_rate: float = 0.0) -> Optional[float]:
        """
        计算最低开仓基差 - 基于成本效益分析的实际可盈利阈值
        
        这是考虑了所有交易成本后，确保净利润为正的最低基差要求。
        只有当实际基差超过这个阈值时，交易才在经济上可行。
        
        Args:
            position_size: 预估仓位大小
            funding_rate: 当前资金费率
            
        Returns:
            float: 最低开仓基差（绝对值），None表示计算失败
        """
        try:
            if not self.current_spot_price or not self.current_futures_price:
                return None
                
            # 计算一整轮套利的总成本
            total_costs = self._calculate_total_cost(position_size, self.current_spot_price, self.current_futures_price)
            
            # 计算资金费率影响（假设做空期货）
            funding_impact = self._calculate_funding_impact("short_futures_long_spot", funding_rate, position_size, self.current_futures_price)
            
            # 应用利润安全边际
            safety_margin = self.config.TRADING_COSTS["PROFIT_SAFETY_MARGIN"]
            required_coverage = total_costs * safety_margin
            
            # 计算最低净影响需求（总成本 - 资金费率带来的收益）
            min_net_impact_needed = required_coverage - funding_impact
            
            # 计算最低基差：净影响需求 / (仓位大小 * 现货价格)
            # 因为 potential_gross_profit = abs(basis) * position_size * spot_price
            min_basis = min_net_impact_needed / (position_size * self.current_spot_price)
            
            # 确保最低基差为正值（取绝对值）
            min_basis = abs(min_basis)
            
            self.logger.debug(
                f"最低开仓基差计算: 总成本={total_costs:.4f}, 资金影响={funding_impact:.4f}, "
                f"安全边际={safety_margin:.2f}, 所需覆盖={required_coverage:.4f}, "
                f"最低基差={min_basis:.6f} ({min_basis*100:.4f}%)"
            )
            
            return min_basis
            
        except Exception as e:
            self.logger.error(f"计算最低开仓基差失败: {e}")
            return None
    
    def check_exit_signal(self, position_type: str) -> bool:
        """
        重构的优先级平仓决策系统 - 生产级风控逻辑

        Args:
            position_type: 持仓类型（仅支持 'short_futures_long_spot'）

        Returns:
            bool: 是否应该出场
        """
        if not self._has_sufficient_data() or not self.entry_basis:
            return False

        if position_type != "short_futures_long_spot":
            self.logger.warning(f"基差套利策略不支持持仓类型: {position_type}")
            return False
        
        try:
            return self._check_prioritized_exit_conditions(position_type)
            
        except Exception as e:
            self.logger.error(f"检查优先级出场信号时发生错误: {e}")
            return False
    
    def _check_prioritized_exit_conditions(self, position_type: str) -> bool:
        """优先级平仓决策序列"""
        current_basis = self.current_basis
        pnl_basis = self.entry_basis - current_basis
        
        # 获取动态中轨（优先使用战略层，回退到传统）
        dynamic_middle = self._get_dynamic_middle_band()
        
        # 第一优先级：逻辑止损 - 基差穿越中轨，套利逻辑失效
        if self._check_logical_stop_loss(current_basis, dynamic_middle):
            return True
        
        # 第二优先级：动态止盈 - 基差回归动态中枢
        if self._check_dynamic_take_profit(current_basis, dynamic_middle):
            return True
        
        # 第三优先级：硬止损 - 固定亏损阈值
        if self._check_hard_stop_loss(pnl_basis):
            return True
        
        # 第四优先级：时间止损 - 最大持仓时间
        if self._check_time_stop():
            return True
        
        return False
    
    def _get_dynamic_middle_band(self) -> float:
        """获取动态中轨 - 使用分层布林带系统"""
        if self.strategic_bb_middle is not None:
            return self.strategic_bb_middle
        else:
            return self.entry_basis  # 极端情况回退到入场基差
    
    def _check_logical_stop_loss(self, current_basis: float, dynamic_middle: float) -> bool:
        """第一优先级：逻辑止损检查"""
        # 核心逻辑：基差穿越中轨意味着"基差被高估"的前提不再成立
        if current_basis < dynamic_middle:
            self.signal_count["exit"] += 1
            self.logger.warning_structured(
                "🚨 逻辑止损触发 - 套利逻辑失效",
                event_type="logical_stop_loss",
                metrics={
                    "current_basis": current_basis,
                    "dynamic_middle": dynamic_middle,
                    "entry_basis": self.entry_basis,
                    "basis_change": current_basis - self.entry_basis
                },
                details={
                    "reason": "基差穿越动态中轨，套利逻辑失效",
                    "priority": "第一优先级",
                    "action": "无条件立即平仓"
                }
            )
            return True
        return False
    
    def _check_dynamic_take_profit(self, current_basis: float, dynamic_middle: float) -> bool:
        """第二优先级：动态止盈检查"""
        # 检查基差是否回归到动态中枢附近
        middle_tolerance = self.basis_profit_target
        
        # 条件1：基差回归中轨附近
        near_middle = current_basis <= (dynamic_middle + middle_tolerance)
        
        # 条件2：基差转为负值（基差套利的理想结果）
        negative_basis = current_basis <= 0
        
        if near_middle or negative_basis:
            self.signal_count["exit"] += 1
            pnl_basis = self.entry_basis - current_basis
            exit_reason = "基差转负" if negative_basis else "基差回归中枢"
            
            self.logger.info_structured(
                f"✅ 动态止盈触发 - {exit_reason}",
                event_type="dynamic_take_profit",
                metrics={
                    "current_basis": current_basis,
                    "dynamic_middle": dynamic_middle,
                    "entry_basis": self.entry_basis,
                    "pnl_basis": pnl_basis,
                    "profit_target": middle_tolerance
                },
                details={
                    "reason": exit_reason,
                    "priority": "第二优先级",
                    "profit_achieved": pnl_basis > 0
                }
            )
            return True
        return False
    
    def _check_hard_stop_loss(self, pnl_basis: float) -> bool:
        """第三优先级：硬止损检查"""
        if abs(pnl_basis) >= self.basis_stop_loss and pnl_basis < 0:
            self.signal_count["exit"] += 1
            self.logger.warning_structured(
                "🛑 硬止损触发 - 亏损超过阈值",
                event_type="hard_stop_loss",
                metrics={
                    "pnl_basis": pnl_basis,
                    "stop_loss_threshold": self.basis_stop_loss,
                    "entry_basis": self.entry_basis,
                    "current_basis": self.current_basis
                },
                details={
                    "reason": "亏损超过固定阈值",
                    "priority": "第三优先级",
                    "safety_net": True
                }
            )
            return True
        return False
    
    def _check_time_stop(self) -> bool:
        """第四优先级：时间止损检查"""
        if not self.entry_time:
            return False
        
        # 获取最大持仓时间配置
        if hasattr(self.config, 'STRATEGY_OPTIMIZATION') and isinstance(self.config.STRATEGY_OPTIMIZATION, dict):
            max_holding_config = self.config.STRATEGY_OPTIMIZATION.get("RISK_CONTROLS", {})
        else:
            max_holding_config = {}
        max_holding_hours = max_holding_config.get("MAX_HOLDING_DURATION_HOURS", 24)  # 默认24小时
        
        holding_duration = self.get_position_holding_duration()
        if holding_duration and holding_duration.total_seconds() > (max_holding_hours * 3600):
            self.signal_count["exit"] += 1
            pnl_basis = self.entry_basis - self.current_basis
            
            self.logger.warning_structured(
                "⏰ 时间止损触发 - 超过最大持仓时间",
                event_type="time_stop",
                metrics={
                    "holding_hours": holding_duration.total_seconds() / 3600,
                    "max_holding_hours": max_holding_hours,
                    "pnl_basis": pnl_basis,
                    "entry_time": self.entry_time.isoformat(),
                    "current_time": self.last_update_time.isoformat()
                },
                details={
                    "reason": "避免长期低效粘滞",
                    "priority": "第四优先级",
                    "forced_exit": True
                }
            )
            return True
        return False
    
    def set_position_entry(self, position_type: str):
        """设置入场状态 - 同时更新仓位管理器"""
        self.entry_basis = self.current_basis
        self.entry_time = self.last_update_time

        # 创建仓位记录
        try:
            pair_name = getattr(self.config, 'PAIR_NAME', 'DEFAULT')
            position = Position(
                position_id=f"{pair_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                position_type=PositionType.LONG if position_type == "long" else PositionType.SHORT,
                status=PositionStatus.OPEN,
                entry_spot_price=self.current_spot_price,
                entry_futures_price=self.current_futures_price,
                entry_basis=self.entry_basis,
                open_time=self.entry_time or datetime.now()
            )
            self.position_manager.open_position(position)
        except Exception as e:
            self.logger.error(f"创建仓位记录失败: {e}")

        self.logger.info(
            f"记录入场状态 - 类型:{position_type}, "
            f"基差:{self.entry_basis:.6f}, 时间:{self.entry_time}"
        )
    
    def clear_position_entry(self):
        """清除入场状态 - 同时更新仓位管理器"""
        if self.position_manager.current_position:
            try:
                self.position_manager.close_position(
                    exit_spot_price=self.current_spot_price,
                    exit_futures_price=self.current_futures_price,
                    exit_basis=self.current_basis
                )
            except Exception as e:
                self.logger.error(f"关闭仓位记录失败: {e}")

        self.entry_basis = None
        self.entry_time = None
        self.logger.info("清除入场状态")

    def get_position_data(self) -> Optional[Dict[str, Any]]:
        """获取当前仓位数据"""
        if self.position_manager.current_position:
            position = self.position_manager.current_position
            return {
                'position_id': position.position_id,
                'position_type': position.position_type.value,
                'status': position.status.value,
                'entry_spot_price': position.entry_spot_price,
                'entry_futures_price': position.entry_futures_price,
                'entry_basis': position.entry_basis,
                'open_time': position.open_time,
                'unrealized_pnl': position.unrealized_pnl,
                'realized_pnl': position.realized_pnl
            }
        return None
    
    def get_position_holding_duration(self) -> Optional[timedelta]:
        """获取持仓时长"""
        if not self.entry_time:
            return None
        return self.last_update_time - self.entry_time
    
    def get_unrealized_pnl(self, position_type: str) -> Optional[float]:
        """获取未实现盈亏（基差点数） - 单边策略版本"""
        if not self.entry_basis:
            return None
        
        # 基差套利策略验证
        if position_type != "short_futures_long_spot":
            self.logger.warning(f"基差套利策略不支持持仓类型: {position_type}")
            return None

        try:
            current_basis = self.current_basis

            # 基差套利：基差收敛为盈利
            return self.entry_basis - current_basis
                
        except Exception as e:
            self.logger.error(f"计算未实现盈亏时发生错误: {e}")
            return None
    
    def _has_sufficient_data(self) -> bool:
        """检查是否有足够数据进行计算 - 使用分层布林带"""
        return self._has_layered_sufficient_data()
    
    def get_current_state(self) -> Dict[str, Any]:
        """获取当前策略状态 - 包含分层布林带信息"""
        return {
            # 基础状态
            "current_basis": self.current_basis,
            "entry_basis": self.entry_basis,
            "spot_price": self.current_spot_price,
            "futures_price": self.current_futures_price,
            "last_update": self.last_update_time,
            "total_updates": self.total_updates,
            "signals": self.signal_count.copy(),
            
            # 传统布林带已移除 - 使用分层布林带系统
            
            # 分层布林带系统
            "layered_bollinger_bands": {
                "strategic": {
                    "bb_middle": self.strategic_bb_middle,
                    "bb_upper": self.strategic_bb_upper,
                    "bb_lower": self.strategic_bb_lower,
                    "data_points": len(self.strategic_basis_history),
                    "window": self.strategic_window
                },
                "tactical": {
                    "bb_middle": self.tactical_bb_middle,
                    "bb_upper": self.tactical_bb_upper,
                    "bb_lower": self.tactical_bb_lower,
                    "data_points": len(self.tactical_basis_history),
                    "window": self.tactical_window
                }
            },
            
            # 分层信号状态
            "strategy_state": {
                "current_state": self.strategy_state,
                "strategic_signal_active": self.strategic_signal_active,
                "tactical_signal_confirmed": self.tactical_signal_confirmed,
                "layered_data_sufficient": self._has_layered_sufficient_data()
            },
            
            # 技术指标
            "technical_indicators": {
                "atr": self.current_atr,
                "adx": self.current_adx,
                "bb_position": self._get_bollinger_position() if self._has_sufficient_data() else "unknown",
                "incremental_indicators": self.indicator_manager.get_current_values(),
                "indicators_ready": self.indicator_manager.is_all_ready()
            }
        }
    
    def get_market_summary(self) -> str:
        """获取市场状态摘要"""
        if not self._has_sufficient_data():
            return "数据不足，无法分析"
        
        basis_pct = self.current_basis * 100
        
        # 使用分层布林带信息
        if self.strategic_bb_middle and self.strategic_bb_upper and self.strategic_bb_lower:
            middle_pct = self.strategic_bb_middle * 100
            upper_pct = self.strategic_bb_upper * 100
            lower_pct = self.strategic_bb_lower * 100
            
            # 判断当前基差位置
            if self.current_basis >= self.strategic_bb_upper:
                position = "战略上轨附近（偏高）"
            elif self.current_basis <= self.strategic_bb_lower:
                position = "战略下轨附近（偏低）"
            else:
                position = "战略中性区域"
            
            return (
                f"基差: {basis_pct:.4f}% | "
                f"战略中轨: {middle_pct:.4f}% | "
                f"战略区间: [{lower_pct:.4f}%, {upper_pct:.4f}%] | "
                f"位置: {position}"
            )
        else:
            return f"基差: {basis_pct:.4f}% | 分层布林带数据收集中..."
    
    def reset(self):
        """重置策略状态 - 包含分层布林带数据"""
        # 传统数据重置
        self.spot_prices.clear()
        self.futures_prices.clear()
        self.basis_history.clear()
        self.timestamps.clear()
        
        # 分层布林带数据重置
        self.strategic_basis_history.clear()
        self.strategic_timestamps.clear()
        self.tactical_basis_history.clear()
        self.tactical_timestamps.clear()
        
        # VWAP缓存重置
        self.minute_vwap_cache.clear()
        self.second_vwap_cache.clear()
        self.last_minute_boundary = None
        self.last_second_boundary = None
        
        # 重置技术指标历史数据
        self.high_prices.clear()
        self.low_prices.clear()
        self.close_prices.clear()
        
        self.current_spot_price = None
        self.current_futures_price = None
        self.current_basis = None
        self.last_update_time = None
        
        # 传统布林带已移除
        
        # 分层布林带重置
        self.strategic_bb_middle = None
        self.strategic_bb_upper = None
        self.strategic_bb_lower = None
        self.tactical_bb_middle = None
        self.tactical_bb_upper = None
        self.tactical_bb_lower = None
        
        # 分层信号状态重置
        self.strategy_state = "standby"
        self.strategic_signal_active = False
        self.tactical_signal_confirmed = False
        
        # 重置技术指标当前值
        self.current_atr = None
        self.current_adx = None
        
        # 重置技术指标初始化标志
        self._first_indicators_ready = False
        
        # 重置增量指标管理器
        self.indicator_manager.reset_all()
        
        self.entry_basis = None
        self.entry_time = None
        
        self.total_updates = 0
        self.signal_count = {"entry": 0, "exit": 0}
        
        self.logger.info("策略状态已重置（包含分层布林带系统）")
    
    def _analyze_microstructure_signals(self, signal_type: str) -> Dict:
        """
        分析微观结构信号以增强交易决策
        
        Args:
            signal_type: 'short_futures_long_spot'
            
        Returns:
            Dict: 微观结构分析结果
        """
        if not self.microstructure_adapter:
            return {
                'available': False,
                'reason': '微观结构分析模块未启用'
            }
        
        try:
            # 获取现货微观结构信号（主要关注现货流动性）
            spot_symbol = self.config.SPOT_ID
            microstructure_signals = self.microstructure_adapter.get_microstructure_signals(spot_symbol)
            
            if not microstructure_signals:
                return {
                    'available': False,
                    'reason': f'无{spot_symbol}微观结构数据'
                }
            
            # 分析信号与交易方向的一致性 - 统一为套利方向
            trade_direction = 'arbitrage'
            position_analysis = self.microstructure_adapter.should_enhance_position_size(
                spot_symbol, trade_direction
            )
            
            # 提取关键指标
            micro_signal = microstructure_signals.get('micro_signal', 'neutral')
            micro_confidence = microstructure_signals.get('micro_confidence', 0)
            obi_signal = microstructure_signals.get('obi_signal', 'neutral')
            flow_signal = microstructure_signals.get('flow_signal', 'neutral')
            
            # 计算信号强度得分
            alignment_score = self._calculate_signal_alignment_score(
                signal_type, micro_signal, obi_signal, flow_signal
            )
            
            return {
                'available': True,
                'micro_signal': micro_signal,
                'micro_confidence': micro_confidence,
                'obi_signal': obi_signal,
                'flow_signal': flow_signal,
                'alignment_score': alignment_score,
                'position_multiplier': position_analysis.get('multiplier', 1.0),
                'enhancement_reason': position_analysis.get('reason'),
                'enhance_position': position_analysis.get('enhance', False),
                'raw_indicators': self.microstructure_adapter.get_latest_indicators(spot_symbol)
            }
            
        except Exception as e:
            self.logger.error_structured(
                f"微观结构信号分析失败: {e}",
                event_type="microstructure_analysis_error",
                details={'error': str(e)}
            )
            return {
                'available': False,
                'reason': f'分析失败: {str(e)}'
            }
    
    def _calculate_signal_alignment_score(self, signal_type: str, micro_signal: str, 
                                        obi_signal: str, flow_signal: str) -> float:
        """
        计算微观结构信号与交易方向的一致性得分
        
        Returns:
            float: 一致性得分 [-1, 1]
                  > 0: 微观信号支持交易方向
                  < 0: 微观信号与交易方向相悖
                  = 0: 中性
        """
        # 确定期望的微观信号方向 - 统一为套利买现货
        expected_direction = 'bullish'  # 套利策略买现货，期望现货有买压
        
        score = 0
        
        # 主信号权重：60%
        if micro_signal == expected_direction:
            score += 0.6
        elif micro_signal == 'neutral':
            score += 0.0
        else:
            score -= 0.6
        
        # OBI信号权重：25%
        if obi_signal == expected_direction:
            score += 0.25
        elif obi_signal == 'neutral':
            score += 0.0
        else:
            score -= 0.25
        
        # 交易流信号权重：15%
        if flow_signal == expected_direction:
            score += 0.15
        elif flow_signal == 'neutral':
            score += 0.0
        else:
            score -= 0.15
        
        return max(-1.0, min(1.0, score))  # 限制在[-1, 1]范围内
    
    def _calculate_enhanced_confidence(self, original_confidence: float, 
                                     microstructure_analysis: Dict) -> float:
        """
        基于微观结构分析增强或降低原始置信度
        
        Args:
            original_confidence: 原始置信度 [0, 1]
            microstructure_analysis: 微观结构分析结果
            
        Returns:
            float: 增强后的置信度 [0, 1]
        """
        if not microstructure_analysis.get('available', False):
            return original_confidence
        
        alignment_score = microstructure_analysis.get('alignment_score', 0)
        micro_confidence = microstructure_analysis.get('micro_confidence', 0)
        
        # 微观结构增强/减弱系数
        if alignment_score > 0.3:  # 强烈支持
            enhancement_factor = 1 + (alignment_score * micro_confidence * 0.3)  # 最多增强30%
        elif alignment_score < -0.3:  # 强烈相悖  
            enhancement_factor = 1 + (alignment_score * micro_confidence * 0.4)  # 最多减弱40%
        else:  # 中性
            enhancement_factor = 1.0
        
        enhanced_confidence = original_confidence * enhancement_factor
        
        # 确保置信度在合理范围内
        return max(0.0, min(1.0, enhanced_confidence))
    
    def get_microstructure_position_multiplier(self, signal_result: Dict) -> float:
        """
        基于微观结构分析获取仓位调整乘数
        
        Args:
            signal_result: check_net_entry_signal的返回结果
            
        Returns:
            float: 仓位乘数 [0.5, 1.5]
        """
        if not signal_result or 'microstructure_analysis' not in signal_result:
            return 1.0
        
        microstructure = signal_result['microstructure_analysis']
        
        if not microstructure.get('available', False):
            return 1.0
        
        # 获取建议的仓位乘数
        multiplier = microstructure.get('position_multiplier', 1.0)
        
        # 安全限制：防止过度杠杆或过度保守
        return max(0.5, min(1.5, multiplier))

    async def preheat_strategy_data(self, okx_connector, spot_id: str, futures_id: str) -> Dict[str, Any]:
        """
        策略数据预热 - 获取历史K线数据填充战略层

        Args:
            okx_connector: OKX连接器实例
            spot_id: 现货交易对ID，如 "BTC-USDT"
            futures_id: 期货交易对ID，如 "BTC-USDT-SWAP"

        Returns:
            Dict包含预热结果和统计信息
        """
        try:
            self.logger.info(f"🔥 开始策略数据预热，现货: {spot_id}, 期货: {futures_id}")

            # 计算需要获取的历史数据时长
            # 战略层需要分钟级数据，获取比需要的多一些以确保充足
            required_minutes = max(self.strategic_window * 2, 60)  # 至少60分钟

            self.logger.info(f"📊 预热参数: 战略层窗口={self.strategic_window}分钟, 获取历史数据={required_minutes}分钟")

            # 🔥 关键修复：分别获取现货和期货的历史K线数据
            self.logger.info("📡 同时获取现货和期货历史数据...")
            
            # 并行获取现货和期货数据
            spot_result = await okx_connector.get_multiple_historical_candlesticks(
                inst_ids=[spot_id],
                minutes=required_minutes,
                bar="1m"  # 1分钟K线用于战略层
            )
            
            futures_result = await okx_connector.get_multiple_historical_candlesticks(
                inst_ids=[futures_id], 
                minutes=required_minutes,
                bar="1m"  # 1分钟K线用于战略层
            )
            
            # 验证两个数据源都成功获取
            if not spot_result['success']:
                self.logger.error(f"❌ 获取现货历史数据失败: {spot_result.get('error', 'Unknown error')}")
                return {
                    'success': False,
                    'error': f"现货数据获取失败: {spot_result.get('error', 'Unknown error')}",
                    'preheated_pairs': [],
                    'strategic_data_points': 0
                }
                
            if not futures_result['success']:
                self.logger.error(f"❌ 获取期货历史数据失败: {futures_result.get('error', 'Unknown error')}")
                return {
                    'success': False,
                    'error': f"期货数据获取失败: {futures_result.get('error', 'Unknown error')}",
                    'preheated_pairs': [],
                    'strategic_data_points': 0
                }
            
            # 合并数据结果供后续处理
            historical_result = {
                'success': True,
                'data': {
                    'spot': spot_result['data'].get(spot_id, []),
                    'futures': futures_result['data'].get(futures_id, [])
                },
                'summary': {
                    'spot_points': len(spot_result['data'].get(spot_id, [])),
                    'futures_points': len(futures_result['data'].get(futures_id, []))
                }
            }

            # 获取现货和期货数据
            spot_data = historical_result['data']['spot']
            futures_data = historical_result['data']['futures']
            
            if not spot_data:
                self.logger.error(f"❌ 现货 {spot_id} 无历史数据")
                return {
                    'success': False,
                    'error': f'现货 {spot_id} 无历史数据',
                    'preheated_pairs': [],
                    'strategic_data_points': 0
                }
                
            if not futures_data:
                self.logger.error(f"❌ 期货 {futures_id} 无历史数据")
                return {
                    'success': False,
                    'error': f'期货 {futures_id} 无历史数据',
                    'preheated_pairs': [],
                    'strategic_data_points': 0
                }

            self.logger.info(f"📊 数据获取成功: 现货{len(spot_data)}条, 期货{len(futures_data)}条")

            # 🔥 关键修复：处理真实的现货和期货数据
            processed_count = await self._process_paired_historical_candlesticks(
                spot_id, futures_id, spot_data, futures_data
            )

            total_strategic_points = processed_count
            preheated_pairs = [f"{spot_id}_{futures_id}"] if processed_count > 0 else []

            if processed_count > 0:
                self.logger.info(f"✅ {spot_id}<->{futures_id} 预热完成: {processed_count} 个基差数据点")
            else:
                self.logger.warning(f"⚠️ {spot_id}<->{futures_id} 预热失败: 无有效基差数据点")

            # 更新分层布林带计算
            if total_strategic_points > 0:
                self.logger.info("🔄 基于预热数据更新分层布林带...")
                current_time = datetime.now()
                self._update_layered_bollinger_data_enhanced(current_time)

                # 🔥 关键修复：强制触发布林带计算
                self.logger.info("⚡ 预热完成后强制计算布林带指标...")
                
                # 记录预热前的状态用于对比
                before_middle = self.strategic_bb_middle
                before_upper = self.strategic_bb_upper  
                before_lower = self.strategic_bb_lower
                
                # 强制调用布林带计算方法
                self._calculate_layered_bollinger_bands()
                
                # 详细验证计算结果
                strategic_ready = (self.strategic_bb_middle is not None and
                                 self.strategic_bb_upper is not None and
                                 self.strategic_bb_lower is not None)

                if strategic_ready:
                    self.logger.info("✅ 战略层布林带预热成功")
                    self.logger.info(f"📊 预热计算结果: "
                                   f"中轨={self.strategic_bb_middle:.6f}, "
                                   f"上轨={self.strategic_bb_upper:.6f}, "
                                   f"下轨={self.strategic_bb_lower:.6f}")
                    
                    # 验证计算是否真的生效
                    if (before_middle != self.strategic_bb_middle or 
                        before_upper != self.strategic_bb_upper or 
                        before_lower != self.strategic_bb_lower):
                        self.logger.info("🎯 强制计算成功：布林带数值已更新")
                    else:
                        self.logger.warning("⚠️ 强制计算后数值未变化，可能存在问题")
                        
                    # 额外验证：检查数据源
                    self.logger.info(f"📈 验证数据源: 战略层历史数据={len(self.strategic_basis_history)}个点, "
                                   f"所需最少={self.strategic_window}个点")
                    
                else:
                    # 详细诊断为什么预热失败
                    self.logger.warning("⚠️ 战略层布林带预热后仍未就绪")
                    self.logger.warning(f"🔍 诊断信息: "
                                      f"中轨={self.strategic_bb_middle}, "
                                      f"上轨={self.strategic_bb_upper}, "
                                      f"下轨={self.strategic_bb_lower}")
                    self.logger.warning(f"📊 数据状态: 战略历史数据={len(self.strategic_basis_history)}个, "
                                      f"需要={self.strategic_window}个, "
                                      f"数据样本={list(self.strategic_basis_history)[-5:] if self.strategic_basis_history else []}")
                    
                # 同时检查战术层布林带
                tactical_ready = (self.tactical_bb_middle is not None and
                                self.tactical_bb_upper is not None and  
                                self.tactical_bb_lower is not None)
                                
                if tactical_ready:
                    self.logger.info("✅ 战术层布林带也已就绪")
                else:
                    self.logger.debug(f"⚡ 战术层布林带未就绪: 数据={len(self.tactical_basis_history)}个, 需要={self.tactical_window}个")

            # 生成预热报告
            summary = historical_result.get('summary', {})
            # 修复：使用实际的交易对信息而不是未定义的 trading_pairs 变量
            current_pair = f"{spot_id}_{futures_id}"
            all_pairs = [current_pair]  # 单个交易对预热

            preheat_result = {
                'success': len(preheated_pairs) > 0,
                'preheated_pairs': preheated_pairs,
                'failed_pairs': list(set(all_pairs) - set(preheated_pairs)),
                'strategic_data_points': total_strategic_points,
                'strategic_ready': strategic_ready if total_strategic_points > 0 else False,
                'tactical_ready': False,  # 战术层需要实时数据积累
                'summary': {
                    'total_pairs': len(all_pairs),
                    'successful_pairs': len(preheated_pairs),
                    'failed_pairs': len(all_pairs) - len(preheated_pairs),
                    'total_candlesticks': summary.get('total_candlesticks', 0),
                    'requested_minutes': required_minutes,
                    'strategic_window': self.strategic_window,
                    'tactical_window': self.tactical_window
                }
            }

            self.logger.info(f"🔥 数据预热完成: {len(preheated_pairs)}/{len(all_pairs)} 交易对成功, "
                           f"战略层数据点: {total_strategic_points}")

            return preheat_result

        except Exception as e:
            self.logger.error(f"策略数据预热异常: {e}")
            return {
                'success': False,
                'error': str(e),
                'preheated_pairs': [],
                'strategic_data_points': 0,
                'strategic_ready': False,
                'tactical_ready': False
            }

    async def _process_paired_historical_candlesticks(self, spot_id: str, futures_id: str, 
                                                    spot_data: List[List], futures_data: List[List]) -> int:
        """
        处理配对的现货和期货历史K线数据并计算真实基差

        Args:
            spot_id: 现货交易对ID
            futures_id: 期货交易对ID
            spot_data: 现货K线数据
            futures_data: 期货K线数据

        Returns:
            int: 成功处理的基差数据点数量
        """
        try:
            processed_count = 0
            
            # 将数据转换为时间戳索引的字典，便于快速查找
            spot_dict = {}
            futures_dict = {}
            
            # 处理现货数据
            for spot_candle in spot_data:
                try:
                    timestamp_ms = int(spot_candle[0])
                    close_price = float(spot_candle[4])  # 收盘价
                    high_price = float(spot_candle[2])
                    low_price = float(spot_candle[3])
                    volume = float(spot_candle[5])
                    
                    spot_dict[timestamp_ms] = {
                        'close': close_price,
                        'high': high_price,
                        'low': low_price,
                        'volume': volume
                    }
                except (ValueError, IndexError) as e:
                    self.logger.debug(f"跳过无效现货K线数据: {e}")
                    continue
            
            # 处理期货数据
            for futures_candle in futures_data:
                try:
                    timestamp_ms = int(futures_candle[0])
                    close_price = float(futures_candle[4])  # 收盘价
                    high_price = float(futures_candle[2])
                    low_price = float(futures_candle[3])
                    volume = float(futures_candle[5])
                    
                    futures_dict[timestamp_ms] = {
                        'close': close_price,
                        'high': high_price,
                        'low': low_price,
                        'volume': volume
                    }
                except (ValueError, IndexError) as e:
                    self.logger.debug(f"跳过无效期货K线数据: {e}")
                    continue
            
            # 找出现货和期货都有数据的时间点
            common_timestamps = set(spot_dict.keys()) & set(futures_dict.keys())
            
            if not common_timestamps:
                self.logger.warning(f"现货和期货数据没有重叠的时间点")
                return 0
            
            # 按时间顺序处理（从旧到新）
            sorted_timestamps = sorted(common_timestamps)
            
            self.logger.info(f"🔍 找到 {len(sorted_timestamps)} 个重叠时间点，开始计算真实基差")
            
            for timestamp_ms in sorted_timestamps:
                try:
                    # 获取对应时间点的现货和期货数据
                    spot_info = spot_dict[timestamp_ms]
                    futures_info = futures_dict[timestamp_ms]
                    
                    spot_price = spot_info['close']
                    futures_price = futures_info['close']
                    
                    # 🎯 计算真实基差（期货价格 - 现货价格）/ 现货价格
                    basis = (futures_price - spot_price) / spot_price
                    
                    # 转换时间戳
                    timestamp = datetime.fromtimestamp(timestamp_ms / 1000)
                    
                    # 创建分钟级聚合数据点
                    minute_aggregate = {
                        'timestamp': timestamp,
                        'spot_vwap': spot_price,
                        'futures_vwap': futures_price,
                        'basis': basis,
                        'volume_weighted_basis': basis,
                        'total_volume': spot_info['volume'] + futures_info['volume'],
                        'tick_count': 1,
                        'price_range': max(spot_info['high'], futures_info['high']) - min(spot_info['low'], futures_info['low']),
                        'volatility': abs(futures_price - spot_price) / spot_price
                    }
                    
                    # 添加到数据预处理器的分钟级聚合缓存
                    self.data_preprocessor.minute_aggregates.append(minute_aggregate)
                    
                    # 🔥 关键：添加到战略层基差历史数据（用于布林带计算）
                    self.strategic_basis_history.append(basis)
                    self.strategic_timestamps.append(timestamp)
                    
                    processed_count += 1
                    
                    # 每100个数据点输出一次进度
                    if processed_count % 100 == 0:
                        self.logger.debug(f"已处理 {processed_count} 个基差数据点，最新基差: {basis:.6f}")
                
                except Exception as e:
                    self.logger.debug(f"跳过时间戳 {timestamp_ms} 的数据处理: {e}")
                    continue
            
            self.logger.info(f"✅ 基差数据处理完成: {processed_count}/{len(sorted_timestamps)} 个有效数据点")
            self.logger.info(f"📊 基差数据范围: {min(self.strategic_basis_history):.6f} ~ {max(self.strategic_basis_history):.6f}")
            
            return processed_count
            
        except Exception as e:
            self.logger.error(f"处理配对历史数据失败: {e}")
            return 0

    async def _process_historical_candlesticks(self, pair: str, candlestick_data: List[List]) -> int:
        """
        处理历史K线数据并填充到数据预处理器

        Args:
            pair: 交易对名称
            candlestick_data: K线数据数组，格式: [ts, o, h, l, c, vol, volCcy, volCcyQuote, confirm]

        Returns:
            int: 成功处理的数据点数量
        """
        try:
            processed_count = 0

            # 按时间顺序处理K线数据（从旧到新）
            # OKX API返回的数据是从新到旧排序，需要反转
            sorted_candlesticks = sorted(candlestick_data, key=lambda x: int(x[0]))

            for candlestick in sorted_candlesticks:
                try:
                    # 解析K线数据
                    # [ts, o, h, l, c, vol, volCcy, volCcyQuote, confirm]
                    timestamp_ms = int(candlestick[0])
                    open_price = float(candlestick[1])
                    high_price = float(candlestick[2])
                    low_price = float(candlestick[3])
                    close_price = float(candlestick[4])
                    volume = float(candlestick[5])

                    # 转换时间戳
                    timestamp = datetime.fromtimestamp(timestamp_ms / 1000)

                    # 使用收盘价作为代表价格，成交量作为权重
                    # 注意：这里我们只有单一交易对的数据，需要模拟现货和期货价格
                    # 在实际应用中，需要分别获取现货和期货的K线数据

                    # 临时方案：使用同一价格作为现货和期货价格
                    # 在实际预热中，应该分别获取现货和期货的历史数据
                    spot_price = close_price
                    futures_price = close_price * 1.001  # 模拟小幅基差

                    # 计算基差
                    basis = (futures_price - spot_price) / spot_price

                    # 创建分钟级聚合数据点
                    minute_aggregate = {
                        'timestamp': timestamp,
                        'spot_vwap': spot_price,
                        'futures_vwap': futures_price,
                        'basis': basis,  # 添加basis字段
                        'volume_weighted_basis': basis,
                        'total_volume': volume,
                        'tick_count': 1,
                        'price_range': high_price - low_price,
                        'volatility': abs(close_price - open_price) / open_price if open_price > 0 else 0
                    }

                    # 直接添加到数据预处理器的分钟级聚合缓存
                    self.data_preprocessor.minute_aggregates.append(minute_aggregate)

                    # 同时添加到战略层基差历史数据（用于布林带计算）
                    self.strategic_basis_history.append(basis)
                    self.strategic_timestamps.append(timestamp)

                    processed_count += 1

                except (ValueError, IndexError) as e:
                    self.logger.debug(f"跳过无效K线数据: {e}")
                    continue

            self.logger.debug(f"{pair} 历史数据处理完成: {processed_count}/{len(candlestick_data)} 条有效")
            return processed_count

        except Exception as e:
            self.logger.error(f"处理 {pair} 历史K线数据异常: {e}")
            return 0

    def __repr__(self):
        return (
            f"BasisArbitrageStrategy(strategic_window={self.strategic_window}, "
            f"tactical_window={self.tactical_window}, layered_data_points="
            f"{len(self.strategic_basis_history)}/{len(self.tactical_basis_history)})"
        )