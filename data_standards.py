"""
数据标准化模块
统一整个交易系统的数据格式、单位和结构标准
"""

from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum


class DataUnit(Enum):
    """数据单位标准"""
    BASIS_DECIMAL = "decimal"  # 基差：小数形式 (0.001)
    BASIS_PERCENT = "percent"  # 基差：百分比形式 (0.1%)
    BASIS_BPS = "bps"         # 基差：基点形式 (10 bps)
    
    PRICE_USDT = "usdt"       # 价格：USDT
    AMOUNT_USDT = "usdt"      # 金额：USDT
    
    TIME_ISO = "iso"          # 时间：ISO 8601格式
    TIME_TIMESTAMP = "timestamp"  # 时间：Unix时间戳
    TIME_DURATION = "duration"    # 时长：可读格式 "1h 30m"


class RiskLevel(Enum):
    """风险等级标准"""
    SAFE = "safe"
    WARNING = "warning"
    DANGER = "danger"
    CRITICAL = "critical"


@dataclass
class StandardDataFormat:
    """标准数据格式定义"""
    
    # 基差数据标准
    BASIS_DECIMAL_PRECISION = 6      # 基差小数精度
    BASIS_PERCENT_PRECISION = 4      # 基差百分比精度
    BASIS_BPS_PRECISION = 1          # 基差基点精度
    
    # 价格数据标准
    PRICE_PRECISION = 5              # 价格精度
    AMOUNT_PRECISION = 4             # 金额精度
    
    # 时间数据标准
    TIME_FORMAT = "%Y-%m-%dT%H:%M:%S.%fZ"  # ISO 8601格式
    
    # 风险阈值标准
    RISK_SAFE_THRESHOLD = 50         # 安全：>50 bps距离止损
    RISK_WARNING_THRESHOLD = 20      # 警告：20-50 bps距离止损
    RISK_DANGER_THRESHOLD = 10       # 危险：10-20 bps距离止损
    # 临界：<10 bps距离止损


class DataConverter:
    """数据转换工具类"""
    
    @staticmethod
    def basis_to_bps(basis_decimal: float) -> float:
        """基差小数转基点"""
        return basis_decimal * 10000
    
    @staticmethod
    def basis_to_percent(basis_decimal: float) -> float:
        """基差小数转百分比"""
        return basis_decimal * 100
    
    @staticmethod
    def bps_to_basis(bps: float) -> float:
        """基点转基差小数"""
        return bps / 10000
    
    @staticmethod
    def format_duration(seconds: float) -> str:
        """格式化时长为可读格式"""
        if seconds < 60:
            return f"{int(seconds)}s"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            secs = int(seconds % 60)
            return f"{minutes}m {secs}s" if secs > 0 else f"{minutes}m"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}h {minutes}m" if minutes > 0 else f"{hours}h"
    
    @staticmethod
    def assess_risk_level(distance_to_stop_bps: float) -> RiskLevel:
        """评估风险等级"""
        if distance_to_stop_bps >= StandardDataFormat.RISK_SAFE_THRESHOLD:
            return RiskLevel.SAFE
        elif distance_to_stop_bps >= StandardDataFormat.RISK_WARNING_THRESHOLD:
            return RiskLevel.WARNING
        elif distance_to_stop_bps >= StandardDataFormat.RISK_DANGER_THRESHOLD:
            return RiskLevel.DANGER
        else:
            return RiskLevel.CRITICAL
    
    @staticmethod
    def format_timestamp(dt: datetime) -> str:
        """格式化时间戳为标准ISO格式"""
        return dt.strftime(StandardDataFormat.TIME_FORMAT)


@dataclass
class StandardPositionData:
    """标准仓位数据结构"""
    
    # 基础信息
    position_id: str
    pair_name: str
    position_type: str
    status: str
    
    # 价格和基差
    entry_spot_price: float
    entry_futures_price: float
    entry_basis: float  # 小数形式
    current_basis: float  # 小数形式
    
    # 成本和目标
    entry_cost: float  # USDT
    profit_target_basis: float  # 小数形式
    profit_distance_bps: float  # 基点
    
    # 盈亏数据
    unrealized_pnl: float  # USDT
    basis_change_bps: float  # 基点
    
    # 风险数据
    risk_level: str
    stop_loss_distance_bps: float  # 基点
    
    # 时间数据
    open_time: str  # ISO格式
    position_duration: str  # 可读格式
    
    # 仓位大小
    position_size: float


@dataclass
class StandardMarketData:
    """标准市场数据结构"""
    
    # 基础价格
    spot_price: float
    futures_price: float
    basis: float  # 小数形式
    basis_bps: float  # 基点形式
    
    # 技术指标
    bb_upper: float
    bb_middle: float
    bb_lower: float
    distance_to_upper_bps: float  # 基点
    distance_to_lower_bps: float  # 基点
    
    # 微观结构
    spread: float
    spread_bps: float
    buy_pressure: float  # 0-100
    sell_pressure: float  # 0-100
    volume_pressure: float  # 0-100
    
    # 订单簿
    orderbook_depth: List[str]
    trade_intensity: int  # ticks/min
    recent_large_orders: List[Dict[str, Any]]
    
    # 时间戳
    timestamp: str  # ISO格式
    update_source: str


@dataclass
class StandardSystemData:
    """标准系统数据结构"""
    
    # 系统状态
    status: str
    is_running: bool
    uptime_seconds: float
    uptime_formatted: str
    loop_count: int
    
    # 管理器状态
    total_pairs: int
    active_pairs: int
    total_active_positions: int
    total_capital_used: float
    
    # 时间戳
    timestamp: str  # ISO格式
    start_time: str  # ISO格式


class DataValidator:
    """数据验证工具类"""
    
    @staticmethod
    def validate_position_data(data: Dict[str, Any]) -> bool:
        """验证仓位数据完整性"""
        required_fields = [
            'position_id', 'pair_name', 'position_type', 'status',
            'entry_spot_price', 'entry_futures_price', 'entry_basis',
            'entry_cost', 'profit_target_basis', 'risk_level',
            'open_time', 'position_size'
        ]
        
        for field in required_fields:
            if field not in data or data[field] is None:
                return False
        
        return True
    
    @staticmethod
    def validate_market_data(data: Dict[str, Any]) -> bool:
        """验证市场数据完整性"""
        required_fields = [
            'spot_price', 'futures_price', 'basis',
            'bb_upper', 'bb_middle', 'bb_lower',
            'spread', 'buy_pressure', 'sell_pressure',
            'timestamp'
        ]
        
        for field in required_fields:
            if field not in data or data[field] is None:
                return False
        
        return True
    
    @staticmethod
    def validate_data_consistency(position_data: Dict[str, Any], 
                                market_data: Dict[str, Any]) -> List[str]:
        """验证数据一致性，返回不一致的问题列表"""
        issues = []
        
        # 检查基差一致性
        pos_basis = position_data.get('current_basis', 0)
        market_basis = market_data.get('basis', 0)
        if abs(pos_basis - market_basis) > 0.0001:  # 允许小误差
            issues.append(f"基差不一致: 仓位{pos_basis:.6f} vs 市场{market_basis:.6f}")
        
        # 检查价格一致性
        pos_spot = position_data.get('current_spot_price', 0)
        market_spot = market_data.get('spot_price', 0)
        if abs(pos_spot - market_spot) > 0.01:  # 允许小误差
            issues.append(f"现货价格不一致: 仓位{pos_spot:.5f} vs 市场{market_spot:.5f}")
        
        return issues


# 全局数据标准实例
DATA_STANDARDS = StandardDataFormat()
DATA_CONVERTER = DataConverter()
DATA_VALIDATOR = DataValidator()
