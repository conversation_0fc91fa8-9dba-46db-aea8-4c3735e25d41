"""
代理健康检查器 - 自动检测并处理代理连接问题
"""
import asyncio
import aiohttp
import logging
from typing import Optional, Dict, Any
from datetime import datetime, timedelta


class ProxyHealthChecker:
    """代理连接健康检查器"""
    
    def __init__(self, proxy_url: str, test_url: str = "https://www.okx.com/api/v5/public/time"):
        self.proxy_url = proxy_url
        self.test_url = test_url
        self.logger = logging.getLogger(__name__)
        self.last_check_time: Optional[datetime] = None
        self.last_check_result: bool = False
        self.consecutive_failures = 0
        self.max_failures = 3
        
    async def check_proxy_health(self, timeout: float = 5.0) -> Dict[str, Any]:
        """
        检查代理健康状态
        
        Returns:
            Dict with keys: 'healthy', 'error', 'response_time', 'check_time'
        """
        start_time = datetime.now()
        
        try:
            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=timeout)
            ) as session:
                async with session.get(
                    self.test_url,
                    proxy=self.proxy_url
                ) as response:
                    response_time = (datetime.now() - start_time).total_seconds()
                    
                    if response.status == 200:
                        data = await response.json()
                        # 验证OKX API响应格式
                        if data.get('code') == '0':
                            self.consecutive_failures = 0
                            self.last_check_result = True
                            self.last_check_time = datetime.now()
                            
                            return {
                                'healthy': True,
                                'error': None,
                                'response_time': response_time,
                                'check_time': self.last_check_time.isoformat()
                            }
                    
                    # 状态码不是200或响应格式不正确
                    raise Exception(f"Unexpected response: status={response.status}")
                    
        except Exception as e:
            self.consecutive_failures += 1
            self.last_check_result = False
            self.last_check_time = datetime.now()
            
            response_time = (datetime.now() - start_time).total_seconds()
            
            return {
                'healthy': False,
                'error': str(e),
                'response_time': response_time,
                'check_time': self.last_check_time.isoformat(),
                'consecutive_failures': self.consecutive_failures
            }
    
    async def should_disable_proxy(self) -> bool:
        """
        判断是否应该禁用代理
        
        Returns:
            True if proxy should be disabled
        """
        if self.consecutive_failures >= self.max_failures:
            self.logger.warning(
                f"代理连续失败 {self.consecutive_failures} 次，建议禁用代理"
            )
            return True
        return False
    
    async def test_direct_connection(self, timeout: float = 5.0) -> Dict[str, Any]:
        """
        测试直接连接（不使用代理）
        
        Returns:
            Dict with connection test results
        """
        start_time = datetime.now()
        
        try:
            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=timeout)
            ) as session:
                async with session.get(self.test_url) as response:
                    response_time = (datetime.now() - start_time).total_seconds()
                    
                    if response.status == 200:
                        data = await response.json()
                        if data.get('code') == '0':
                            return {
                                'healthy': True,
                                'error': None,
                                'response_time': response_time,
                                'method': 'direct'
                            }
                    
                    raise Exception(f"Unexpected response: status={response.status}")
                    
        except Exception as e:
            response_time = (datetime.now() - start_time).total_seconds()
            return {
                'healthy': False,
                'error': str(e),
                'response_time': response_time,
                'method': 'direct'
            }
    
    async def auto_detect_best_connection(self) -> Dict[str, Any]:
        """
        自动检测最佳连接方式
        
        Returns:
            Dict with recommended configuration
        """
        self.logger.info("🔍 自动检测最佳网络连接方式...")
        
        # 并行测试代理和直连
        proxy_task = self.check_proxy_health()
        direct_task = self.test_direct_connection()
        
        proxy_result, direct_result = await asyncio.gather(
            proxy_task, direct_task, return_exceptions=True
        )
        
        # 处理异常结果
        if isinstance(proxy_result, Exception):
            proxy_result = {
                'healthy': False,
                'error': str(proxy_result),
                'response_time': 999.0
            }
            
        if isinstance(direct_result, Exception):
            direct_result = {
                'healthy': False,
                'error': str(direct_result),
                'response_time': 999.0
            }
        
        # 决策逻辑
        recommendation = self._analyze_connection_results(proxy_result, direct_result)
        
        # 记录检测结果
        self.logger.info(f"📊 连接检测结果:")
        self.logger.info(f"   代理连接: {'✅' if proxy_result['healthy'] else '❌'} "
                        f"({proxy_result.get('response_time', 0):.2f}s)")
        self.logger.info(f"   直接连接: {'✅' if direct_result['healthy'] else '❌'} "
                        f"({direct_result.get('response_time', 0):.2f}s)")
        self.logger.info(f"🔧 推荐配置: {recommendation['method']} "
                        f"({'启用代理' if recommendation['use_proxy'] else '禁用代理'})")
        
        return {
            'proxy_result': proxy_result,
            'direct_result': direct_result,
            'recommendation': recommendation
        }
    
    def _analyze_connection_results(self, proxy_result: Dict, direct_result: Dict) -> Dict[str, Any]:
        """分析连接测试结果并给出推荐"""
        
        # 情况1: 代理和直连都正常 - 选择响应更快的
        if proxy_result['healthy'] and direct_result['healthy']:
            if proxy_result['response_time'] <= direct_result['response_time'] * 1.5:
                # 代理响应时间在可接受范围内（不超过直连的1.5倍）
                return {
                    'method': 'proxy',
                    'use_proxy': True,
                    'reason': f"代理连接正常且响应快速 ({proxy_result['response_time']:.2f}s)"
                }
            else:
                return {
                    'method': 'direct',
                    'use_proxy': False,
                    'reason': f"直连更快 ({direct_result['response_time']:.2f}s vs {proxy_result['response_time']:.2f}s)"
                }
        
        # 情况2: 只有代理正常
        if proxy_result['healthy'] and not direct_result['healthy']:
            return {
                'method': 'proxy',
                'use_proxy': True,
                'reason': f"只有代理连接可用，直连失败: {direct_result.get('error', 'Unknown error')}"
            }
        
        # 情况3: 只有直连正常
        if not proxy_result['healthy'] and direct_result['healthy']:
            return {
                'method': 'direct',
                'use_proxy': False,
                'reason': f"代理不可用，使用直连: {proxy_result.get('error', 'Unknown error')}"
            }
        
        # 情况4: 都不正常 - 尝试直连
        return {
            'method': 'direct',
            'use_proxy': False,
            'reason': f"代理和直连都有问题，尝试禁用代理。代理错误: {proxy_result.get('error', 'Unknown')}"
        }


async def test_proxy_health_checker():
    """测试代理健康检查器"""
    print("🧪 测试代理健康检查器")
    print("=" * 50)
    
    # 测试当前配置的代理
    checker = ProxyHealthChecker("socks5h://localhost:10809")
    
    # 执行自动检测
    result = await checker.auto_detect_best_connection()
    
    print("\n📋 检测结果:")
    recommendation = result['recommendation']
    print(f"推荐方式: {recommendation['method']}")
    print(f"使用代理: {recommendation['use_proxy']}")
    print(f"原因: {recommendation['reason']}")
    
    return recommendation['use_proxy']


if __name__ == "__main__":
    asyncio.run(test_proxy_health_checker())