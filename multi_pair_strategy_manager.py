"""
多交易对策略管理器 - 一个交易对一个策略实例的管理架构
"""
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from strategy import BasisArbitrageStrategy
from config import get_enabled_trading_pairs, get_pair_config, calculate_position_size
from log_utils import get_structured_logger
from data_standards import (
    DATA_STANDARDS, DATA_CONVERTER, DATA_VALIDATOR,
    StandardPositionData, StandardMarketData, StandardSystemData
)
from data_flow_monitor import DataFlowMonitor


@dataclass
class PairStatus:
    """交易对状态数据类"""
    pair_name: str
    spot_symbol: str
    futures_symbol: str
    strategy: BasisArbitrageStrategy
    last_signal_time: Optional[datetime] = None
    active_position: bool = False
    position_type: Optional[str] = None
    entry_price: Optional[float] = None
    current_pnl: Optional[float] = None
    priority: int = 1
    enabled: bool = True


class MultiPairStrategyManager:
    """多交易对策略管理器 - 每个交易对独立策略实例"""
    
    def __init__(self, config):
        self.config = config
        self.logger = get_structured_logger(__name__)
        
        # 交易对策略字典 {pair_name: PairStatus}
        self.pair_strategies: Dict[str, PairStatus] = {}
        
        # 全局状态
        self.total_active_positions = 0
        self.total_capital_used = 0.0
        self.last_signal_pair = None
        
        # 风险控制参数
        self.max_concurrent_pairs = config.RISK_MANAGEMENT.get("MAX_CONCURRENT_PAIRS", 3)
        self.max_positions_per_pair = config.RISK_MANAGEMENT.get("MAX_POSITIONS_PER_PAIR", 1)

        # 初始化数据流监控器
        self.data_flow_monitor = DataFlowMonitor()

        # 初始化所有启用的交易对策略
        self._initialize_pair_strategies()
    
    def _initialize_pair_strategies(self):
        """初始化所有启用的交易对策略"""
        enabled_pairs = get_enabled_trading_pairs()
        
        for pair_config in enabled_pairs:
            pair_name = pair_config["name"]
            
            try:
                # 为每个交易对创建独立的策略实例
                strategy = BasisArbitrageStrategy(self.config)
                
                # 创建交易对状态
                pair_status = PairStatus(
                    pair_name=pair_name,
                    spot_symbol=pair_config["spot_id"],
                    futures_symbol=pair_config["futures_id"],
                    strategy=strategy,
                    priority=pair_config.get("priority", 1),
                    enabled=pair_config.get("enabled", True)
                )
                
                self.pair_strategies[pair_name] = pair_status
                
                self.logger.info(
                    f"✅ 初始化交易对策略: {pair_name} "
                    f"(现货:{pair_status.spot_symbol}, 期货:{pair_status.futures_symbol})"
                )
                
            except Exception as e:
                self.logger.error(f"❌ 初始化交易对{pair_name}策略失败: {e}")
        
        self.logger.info(
            f"🚀 多交易对策略管理器初始化完成，共{len(self.pair_strategies)}个交易对"
        )
    
    def update_pair_prices(self, spot_symbol: str, futures_symbol: str, 
                          spot_price: float, futures_price: float,
                          high_price: Optional[float] = None, 
                          low_price: Optional[float] = None) -> bool:
        """
        更新指定交易对的价格数据
        
        Args:
            spot_symbol: 现货符号
            futures_symbol: 期货符号
            spot_price: 现货价格
            futures_price: 期货价格
            high_price: 最高价（可选）
            low_price: 最低价（可选）
            
        Returns:
            bool: 是否成功更新
        """
        # 查找对应的交易对
        pair_status = self._find_pair_by_symbols(spot_symbol, futures_symbol)
        if not pair_status:
            return False
        
        if not pair_status.enabled:
            return False
        
        try:
            # 更新策略价格数据
            updated = pair_status.strategy.update_prices(
                spot_price, futures_price, high_price, low_price
            )
            
            if updated:
                self.logger.debug(
                    f"📊 {pair_status.pair_name} 价格更新: "
                    f"现货={spot_price:.6f}, 期货={futures_price:.6f}, "
                    f"基差={(futures_price-spot_price)/spot_price*100:.4f}%"
                )
            
            return updated
            
        except Exception as e:
            self.logger.error(f"更新{pair_status.pair_name}价格失败: {e}")
            return False
    
    def check_all_entry_signals(self, funding_rates: Dict[str, float] = None) -> List[Dict[str, Any]]:
        """
        检查所有交易对的入场信号
        
        Args:
            funding_rates: 各交易对的资金费率 {futures_symbol: rate}
            
        Returns:
            List[Dict]: 按优先级排序的信号列表
        """
        signals = []
        funding_rates = funding_rates or {}
        
        for pair_name, pair_status in self.pair_strategies.items():
            if not pair_status.enabled or pair_status.active_position:
                continue
            
            try:
                # 检查全局风险限制
                if not self._can_open_new_position():
                    break
                
                # 计算该交易对的仓位大小
                current_spot_price = pair_status.strategy.current_spot_price
                if not current_spot_price:
                    continue
                
                position_size = calculate_position_size(
                    current_spot_price, pair_name
                )
                
                # 获取资金费率
                funding_rate = funding_rates.get(pair_status.futures_symbol, 0.0)
                
                # 检查净利润信号
                signal = pair_status.strategy.check_net_entry_signal(
                    position_size, funding_rate
                )
                
                if signal:
                    # 添加交易对信息
                    signal.update({
                        "pair_name": pair_name,
                        "spot_symbol": pair_status.spot_symbol,
                        "futures_symbol": pair_status.futures_symbol,
                        "position_size": position_size,
                        "priority": pair_status.priority,
                        "current_spot_price": current_spot_price,
                        "current_futures_price": pair_status.strategy.current_futures_price
                    })
                    
                    signals.append(signal)
                    
                    self.logger.info(
                        f"🎯 发现套利机会: {pair_name} - "
                        f"评级:{signal['risk_grade']}, 置信度:{signal['confidence']:.3f}"
                    )
                
            except Exception as e:
                self.logger.error(f"检查{pair_name}入场信号失败: {e}")
        
        # 按优先级和置信度排序
        signals.sort(key=lambda x: (x["priority"], -x["confidence"]))
        
        return signals
    
    def check_pair_exit_signal(self, pair_name: str) -> bool:
        """检查指定交易对的出场信号"""
        pair_status = self.pair_strategies.get(pair_name)
        if not pair_status or not pair_status.active_position:
            return False
        
        try:
            should_exit = pair_status.strategy.check_exit_signal(pair_status.position_type)
            
            if should_exit:
                self.logger.info(
                    f"🚪 {pair_name} 检测到出场信号 - 类型:{pair_status.position_type}"
                )
            
            return should_exit
            
        except Exception as e:
            self.logger.error(f"检查{pair_name}出场信号失败: {e}")
            return False
    
    def check_all_exit_signals(self) -> List[str]:
        """检查所有活跃持仓的出场信号"""
        exit_pairs = []
        
        for pair_name, pair_status in self.pair_strategies.items():
            if pair_status.active_position:
                if self.check_pair_exit_signal(pair_name):
                    exit_pairs.append(pair_name)
        
        return exit_pairs
    
    def set_position_entry(self, pair_name: str, position_type: str, entry_price: float):
        """设置交易对入场状态"""
        pair_status = self.pair_strategies.get(pair_name)
        if not pair_status:
            return
        
        try:
            # 更新策略状态
            pair_status.strategy.set_position_entry(position_type)
            
            # 更新管理器状态
            pair_status.active_position = True
            pair_status.position_type = position_type
            pair_status.entry_price = entry_price
            pair_status.last_signal_time = datetime.now()
            
            # 更新全局计数
            self.total_active_positions += 1
            self.last_signal_pair = pair_name
            
            self.logger.info(
                f"📈 {pair_name} 建立持仓: {position_type} @ {entry_price:.6f}"
            )
            
        except Exception as e:
            self.logger.error(f"设置{pair_name}入场状态失败: {e}")
    
    def clear_position_entry(self, pair_name: str):
        """清除交易对持仓状态"""
        pair_status = self.pair_strategies.get(pair_name)
        if not pair_status:
            return
        
        try:
            # 更新策略状态
            pair_status.strategy.clear_position_entry()
            
            # 更新管理器状态
            pair_status.active_position = False
            pair_status.position_type = None
            pair_status.entry_price = None
            pair_status.current_pnl = None
            
            # 更新全局计数
            self.total_active_positions = max(0, self.total_active_positions - 1)
            
            self.logger.info(f"📉 {pair_name} 清除持仓状态")
            
        except Exception as e:
            self.logger.error(f"清除{pair_name}持仓状态失败: {e}")
    
    def update_position_pnl(self, pair_name: str) -> Optional[float]:
        """更新并返回交易对的未实现盈亏"""
        pair_status = self.pair_strategies.get(pair_name)
        if not pair_status or not pair_status.active_position:
            return None
        
        try:
            pnl = pair_status.strategy.get_unrealized_pnl(pair_status.position_type)
            pair_status.current_pnl = pnl
            return pnl
            
        except Exception as e:
            self.logger.error(f"更新{pair_name} PnL失败: {e}")
            return None
    
    def get_pair_status(self, pair_name: str) -> Optional[Dict[str, Any]]:
        """获取交易对详细状态"""
        pair_status = self.pair_strategies.get(pair_name)
        if not pair_status:
            return None
        
        strategy_state = pair_status.strategy.get_current_state()
        
        return {
            "pair_name": pair_name,
            "spot_symbol": pair_status.spot_symbol,
            "futures_symbol": pair_status.futures_symbol,
            "priority": pair_status.priority,
            "enabled": pair_status.enabled,
            "active_position": pair_status.active_position,
            "position_type": pair_status.position_type,
            "entry_price": pair_status.entry_price,
            "current_pnl": pair_status.current_pnl,
            "last_signal_time": pair_status.last_signal_time,
            "strategy_state": strategy_state
        }
    
    def get_all_status(self) -> Dict[str, Any]:
        """获取所有交易对状态汇总"""
        pair_statuses = {}
        total_pnl = 0.0
        active_pairs = []
        
        for pair_name in self.pair_strategies:
            status = self.get_pair_status(pair_name)
            if status:
                pair_statuses[pair_name] = status
                
                if status["active_position"] and status["current_pnl"]:
                    total_pnl += status["current_pnl"]
                    active_pairs.append(pair_name)
        
        return {
            "total_pairs": len(self.pair_strategies),
            "active_positions": self.total_active_positions,
            "active_pairs": active_pairs,
            "total_unrealized_pnl": total_pnl,
            "last_signal_pair": self.last_signal_pair,
            "pair_statuses": pair_statuses
        }
    
    def _find_pair_by_symbols(self, spot_symbol: str, futures_symbol: str) -> Optional[PairStatus]:
        """根据现货和期货符号查找交易对"""
        for pair_status in self.pair_strategies.values():
            if (pair_status.spot_symbol == spot_symbol and 
                pair_status.futures_symbol == futures_symbol):
                return pair_status
        return None
    
    def _can_open_new_position(self) -> bool:
        """检查是否可以开新仓位"""
        return self.total_active_positions < self.max_concurrent_pairs
    
    def get_priority_signal(self, signals: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """从信号列表中选择最优信号"""
        if not signals:
            return None
        
        # 已经按优先级和置信度排序，返回第一个
        return signals[0]
    
    def set_microstructure_adapters(self, microstructure_adapter):
        """为所有策略设置微观结构分析适配器"""
        for pair_status in self.pair_strategies.values():
            pair_status.strategy.set_microstructure_adapter(microstructure_adapter)
        
        self.logger.info("✅ 所有交易对策略已设置微观结构分析适配器")
    
    def reset_all_strategies(self):
        """重置所有策略状态"""
        for pair_status in self.pair_strategies.values():
            pair_status.strategy.reset()
            pair_status.active_position = False
            pair_status.position_type = None
            pair_status.entry_price = None
            pair_status.current_pnl = None
            pair_status.last_signal_time = None
        
        self.total_active_positions = 0
        self.last_signal_pair = None
        
        self.logger.info("🔄 所有交易对策略状态已重置")

    async def start_data_flow_monitoring(self):
        """启动数据流监控"""
        try:
            await self.data_flow_monitor.start_monitoring()
            self.logger.info("🔍 数据流监控已启动")
        except Exception as e:
            self.logger.error(f"启动数据流监控失败: {e}")

    async def stop_data_flow_monitoring(self):
        """停止数据流监控"""
        try:
            await self.data_flow_monitor.stop_monitoring()
            self.logger.info("🔍 数据流监控已停止")
        except Exception as e:
            self.logger.error(f"停止数据流监控失败: {e}")

    def get_data_flow_status(self) -> Dict[str, Any]:
        """获取数据流监控状态"""
        return self.data_flow_monitor.get_current_status()

    def get_data_flow_metrics(self) -> Dict[str, Any]:
        """获取数据流指标摘要"""
        return self.data_flow_monitor.get_metrics_summary()
    
    def get_strategy(self, pair_name: str) -> Optional[BasisArbitrageStrategy]:
        """获取指定交易对的策略实例"""
        pair_status = self.pair_strategies.get(pair_name)
        return pair_status.strategy if pair_status else None
    
    def get_all_strategy_data(self, system_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        获取所有交易对的完整策略数据 - 作为唯一数据源
        
        Args:
            system_context: 系统上下文信息，包含启动时间、循环计数等
            
        Returns:
            包含策略数据和系统状态的完整数据包
        """
        try:
            # 获取系统上下文，如果没有提供则使用默认值
            if system_context is None:
                system_context = {}
            
            current_time = datetime.now()
            start_time = system_context.get('start_time')
            loop_counter = system_context.get('loop_counter', 0)
            is_running = system_context.get('is_running', True)
            
            all_data = {}
            
            for pair_name, pair_status in self.pair_strategies.items():
                strategy = pair_status.strategy
                
                # 获取策略的完整状态数据（包含技术指标）
                current_state = strategy.get_current_state()
                
                # 获取策略的完整状态数据
                strategy_state = {
                    # 基本信息
                    "pair_name": pair_name,
                    "spot_symbol": pair_status.spot_symbol,
                    "futures_symbol": pair_status.futures_symbol,
                    "priority": pair_status.priority,
                    "enabled": pair_status.enabled,
                    "last_update": DATA_CONVERTER.format_timestamp(datetime.now()),
                    
                    # 价格数据 - 使用标准化格式
                    "prices": {
                        "spot_price": round(strategy.current_spot_price, DATA_STANDARDS.PRICE_PRECISION) if strategy.current_spot_price else 0.0,
                        "futures_price": round(strategy.current_futures_price, DATA_STANDARDS.PRICE_PRECISION) if strategy.current_futures_price else 0.0,
                        "last_spot_update": DATA_CONVERTER.format_timestamp(strategy.last_update_time) if strategy.last_update_time else None,
                        "last_futures_update": DATA_CONVERTER.format_timestamp(strategy.last_update_time) if strategy.last_update_time else None
                    },
                    
                    # 基差数据 - 使用标准化格式
                    "basis": {
                        "current_basis": round(strategy.current_basis, DATA_STANDARDS.BASIS_DECIMAL_PRECISION) if strategy.current_basis else 0.0,
                        "basis_pct": round(strategy.current_basis * 100, DATA_STANDARDS.BASIS_PERCENT_PRECISION) if strategy.current_basis else 0.0,
                        "basis_bps": round(DATA_CONVERTER.basis_to_bps(strategy.current_basis), DATA_STANDARDS.BASIS_BPS_PRECISION) if strategy.current_basis else 0.0,
                        "entry_basis": round(strategy.entry_basis, DATA_STANDARDS.BASIS_DECIMAL_PRECISION) if strategy.entry_basis else 0.0,
                        "basis_change": round((strategy.entry_basis - strategy.current_basis), DATA_STANDARDS.BASIS_DECIMAL_PRECISION) if (strategy.entry_basis and strategy.current_basis) else 0.0,
                        "funding_rate": None,  # 资金费率由外部系统管理
                        "last_funding_update": None
                    },
                    
                    # 策略指标（保持向后兼容）
                    "indicators": {
                        "bollinger_bands": {
                            # 战略层布林带（主要指标）- 使用标准化精度
                            "strategic_upper": round(strategy.strategic_bb_upper, DATA_STANDARDS.BASIS_DECIMAL_PRECISION) if strategy.strategic_bb_upper else 0.0,
                            "strategic_middle": round(strategy.strategic_bb_middle, DATA_STANDARDS.BASIS_DECIMAL_PRECISION) if strategy.strategic_bb_middle else 0.0,
                            "strategic_lower": round(strategy.strategic_bb_lower, DATA_STANDARDS.BASIS_DECIMAL_PRECISION) if strategy.strategic_bb_lower else 0.0,
                            # 战术层布林带（辅助指标）- 使用标准化精度
                            "tactical_upper": round(strategy.tactical_bb_upper, DATA_STANDARDS.BASIS_DECIMAL_PRECISION) if strategy.tactical_bb_upper else 0.0,
                            "tactical_middle": round(strategy.tactical_bb_middle, DATA_STANDARDS.BASIS_DECIMAL_PRECISION) if strategy.tactical_bb_middle else 0.0,
                            "tactical_lower": round(strategy.tactical_bb_lower, DATA_STANDARDS.BASIS_DECIMAL_PRECISION) if strategy.tactical_bb_lower else 0.0,
                            # 兼容旧版本字段（使用战略层数据）
                            "upper": round(strategy.strategic_bb_upper, DATA_STANDARDS.BASIS_DECIMAL_PRECISION) if strategy.strategic_bb_upper else 0.0,
                            "middle": round(strategy.strategic_bb_middle, DATA_STANDARDS.BASIS_DECIMAL_PRECISION) if strategy.strategic_bb_middle else 0.0,
                            "lower": round(strategy.strategic_bb_lower, DATA_STANDARDS.BASIS_DECIMAL_PRECISION) if strategy.strategic_bb_lower else 0.0,
                            "position": getattr(strategy, 'bb_position', None)
                        },
                        "rolling_stats": {
                            "mean": getattr(strategy, 'rolling_mean', None),
                            "std": getattr(strategy, 'rolling_std', None),
                            "z_score": getattr(strategy, 'z_score', None)
                        },
                        # 添加从current_state获取的技术指标数据
                        "signal_strength": current_state.get('technical_indicators', {}).get('signal_strength', None)
                    },
                    
                    # 完整技术指标数据（供仪表盘使用）- 增强版本
                    "technical_indicators": self._get_enhanced_technical_indicators(strategy, current_state),
                    
                    # 🔥 添加数据传输调试信息
                    "data_transmission_debug": {
                        "pair_name": pair_status.pair_name,
                        "strategy_total_updates": strategy.total_updates,
                        "tech_indicators_ready": strategy.indicator_manager.is_all_ready() if hasattr(strategy, 'indicator_manager') else False,
                        "current_timestamp": datetime.now().isoformat(),
                        "has_atr": current_state.get('technical_indicators', {}).get('atr') is not None,
                        "has_adx": current_state.get('technical_indicators', {}).get('adx') is not None,
                        "transmission_count": getattr(self, '_transmission_count', 0)
                    },
                    
                    # 信号状态
                    "signals": {
                        "entry_signal": getattr(strategy, 'current_entry_signal', None),
                        "exit_signal": getattr(strategy, 'current_exit_signal', None),
                        "signal_strength": getattr(strategy, 'signal_strength', None),
                        "confidence": getattr(strategy, 'confidence', None),
                        "last_signal_time": pair_status.last_signal_time.isoformat() if pair_status.last_signal_time else None
                    },
                    
                    # 仓位状态 - 集成仓位管理器数据
                    "position": {
                        "active": pair_status.active_position,
                        "type": pair_status.position_type,
                        "entry_price": pair_status.entry_price,
                        "current_pnl": pair_status.current_pnl,
                        # 从仓位管理器获取详细信息
                        "manager_data": strategy.get_position_data() if pair_status.active_position else None
                    },
                    
                    # 风险指标
                    "risk": {
                        "volatility": getattr(strategy, 'volatility', None),
                        "max_drawdown": getattr(strategy, 'max_drawdown', None),
                        "risk_score": getattr(strategy, 'risk_score', None)
                    },
                    
                    # 统计数据
                    "statistics": {
                        "total_signals": getattr(strategy, 'total_signals', 0),
                        "successful_trades": getattr(strategy, 'successful_trades', 0),
                        "win_rate": getattr(strategy, 'win_rate', 0.0),
                        "avg_holding_time": getattr(strategy, 'avg_holding_time', 0.0)
                    }
                }

                # 添加增强数据计算
                try:
                    # 计算距离上轨的距离（基点）
                    distance_to_upper_bps = None
                    distance_to_lower_bps = None
                    
                    if strategy.current_basis and strategy.strategic_bb_upper and strategy.strategic_bb_lower:
                        # 计算当前基差距离战略层上轨和下轨的距离（转换为基点）
                        distance_to_upper_bps = (strategy.current_basis - strategy.strategic_bb_upper) * 10000  # 转换为基点
                        distance_to_lower_bps = (strategy.current_basis - strategy.strategic_bb_lower) * 10000
                    
                    # 将计算结果添加到策略状态
                    if distance_to_upper_bps is not None:
                        strategy_state["distance_to_upper_bps"] = distance_to_upper_bps
                        strategy_state["distance_to_lower_bps"] = distance_to_lower_bps
                    
                    # 准备仓位数据（如果有活跃仓位）- 优先使用仓位管理器数据
                    position_data = None
                    if pair_status.active_position:
                        # 尝试从仓位管理器获取数据
                        manager_position_data = strategy.get_position_data()
                        if manager_position_data:
                            position_data = {
                                'position_size': getattr(strategy, 'position_size', 100.0),
                                'entry_spot_price': manager_position_data['entry_spot_price'],
                                'entry_futures_price': manager_position_data['entry_futures_price'],
                                'entry_basis': manager_position_data['entry_basis'],
                                'position_id': manager_position_data['position_id'],
                                'open_time': manager_position_data['open_time']
                            }
                        else:
                            # 回退到原有数据
                            position_data = {
                                'position_size': getattr(strategy, 'position_size', 100.0),
                                'entry_spot_price': pair_status.entry_price,
                                'entry_futures_price': getattr(strategy, 'entry_futures_price', 0.0),
                                'entry_basis': getattr(strategy, 'entry_basis', 0.0)
                            }

                    # 计算增强数据
                    enhanced_data = strategy.calculate_enhanced_data_for_dashboard(position_data)

                    # 合并增强数据到策略状态
                    if enhanced_data:
                        # 更新基差数据
                        if 'basis_bps' in enhanced_data:
                            strategy_state["basis"]["basis_bps"] = enhanced_data['basis_bps']

                        # 更新指标数据
                        if 'distance_to_upper_bps' in enhanced_data:
                            strategy_state["indicators"]["distance_to_upper_bps"] = enhanced_data['distance_to_upper_bps']
                            strategy_state["indicators"]["distance_to_lower_bps"] = enhanced_data['distance_to_lower_bps']

                        # 添加微观结构数据
                        strategy_state["microstructure"] = {
                            "buy_pressure": enhanced_data.get('buy_pressure', 50.0),
                            "sell_pressure": enhanced_data.get('sell_pressure', 50.0),
                            "volume_pressure": enhanced_data.get('volume_pressure', 50.0),
                            "spread": enhanced_data.get('spread', 0.0),
                            "spread_bps": enhanced_data.get('spread_bps', 0.0),
                            "orderbook_depth": enhanced_data.get('orderbook_depth', []),
                            "trade_intensity": enhanced_data.get('trade_intensity', 0),
                            "recent_large_orders": enhanced_data.get('recent_large_orders', [])
                        }

                        # 更新仓位数据（如果有活跃仓位）
                        if pair_status.active_position and position_data:
                            strategy_state["position"].update({
                                "entry_cost": enhanced_data.get('entry_cost', 0.0),
                                "profit_target_basis": enhanced_data.get('profit_target_basis', 0.0),
                                "profit_distance_bps": enhanced_data.get('profit_distance_bps', 0.0),
                                "risk_level": enhanced_data.get('risk_level', 'safe'),
                                "stop_loss_distance_bps": enhanced_data.get('stop_loss_distance_bps', 100.0),
                                "basis_change_bps": enhanced_data.get('basis_change_bps', 0.0),
                                "position_duration": enhanced_data.get('position_duration', 'N/A'),
                                "position_duration_seconds": enhanced_data.get('position_duration_seconds', 0.0)
                            })

                except Exception as e:
                    self.logger.error(f"计算{pair_name}增强数据失败: {e}")
                    # 继续处理，不影响基础数据
                
                all_data[pair_name] = strategy_state
            
            # 计算系统健康状态（唯一数据源）
            uptime_seconds = (current_time - start_time).total_seconds() if start_time else 0
            uptime_minutes = uptime_seconds / 60
            active_pairs_count = sum(1 for p in self.pair_strategies.values() if p.active_position)
            
            # 完整的系统状态数据包 - 使用标准化格式
            system_status = {
                # 核心系统指标
                "timestamp": DATA_CONVERTER.format_timestamp(current_time),
                "loop_count": loop_counter,
                "is_running": is_running,
                "uptime_seconds": round(uptime_seconds, 1),
                "uptime_minutes": round(uptime_minutes, 1),
                "uptime_formatted": DATA_CONVERTER.format_duration(uptime_seconds),
                
                # 策略管理器状态
                "manager": {
                    "total_pairs": len(self.pair_strategies),
                    "active_pairs": active_pairs_count,
                    "total_active_positions": self.total_active_positions,
                    "total_capital_used": self.total_capital_used,
                    "last_signal_pair": self.last_signal_pair,
                    "max_concurrent_pairs": self.max_concurrent_pairs
                },
                
                # 交易对详细数据
                "pairs": all_data,
                
                # 心跳数据（专为日志格式化准备）
                "heartbeat": {
                    "message": (
                        f"❤️ 系统心跳: 正常运行中，已处理 {loop_counter} 个周期 "
                        f"(运行时长: {uptime_minutes:.1f}分钟, "
                        f"活跃交易对: {len(all_data)}, "
                        f"活跃仓位: {self.total_active_positions})"
                    ),
                    "should_log": loop_counter > 0 and loop_counter % 60 == 0,
                    "loop_counter": loop_counter,
                    "uptime_minutes": uptime_minutes,
                    "active_pairs": len(all_data),
                    "active_positions": self.total_active_positions
                },

                # 数据流监控状态
                "data_flow_monitoring": {
                    "status": self.get_data_flow_status(),
                    "metrics": self.get_data_flow_metrics()
                }
            }
            
            return system_status
            
        except Exception as e:
            self.logger.error(f"获取策略数据失败: {e}")
            current_time = datetime.now()
            return {
                "timestamp": current_time.isoformat(),
                "loop_count": system_context.get('loop_counter', 0),
                "is_running": False,
                "uptime_seconds": 0,
                "uptime_minutes": 0,
                "manager": {"error": str(e)},
                "pairs": {},
                "heartbeat": {
                    "message": f"❌ 系统错误: {str(e)}",
                    "should_log": True,
                    "error": True
                }
            }
    
    def _get_enhanced_technical_indicators(self, strategy, current_state: Dict[str, Any]) -> Dict[str, Any]:
        """获取增强的技术指标数据，包含调试信息和状态"""
        try:
            # 🔥 增加传输计数器
            if not hasattr(self, '_transmission_count'):
                self._transmission_count = 0
            self._transmission_count += 1
            
            # 获取原始技术指标数据
            tech_indicators = current_state.get('technical_indicators', {})
            
            # 获取增量指标的详细信息
            incremental_indicators = tech_indicators.get('incremental_indicators', {})
            
            # 获取仪表盘可用的值（包含调试信息）
            dashboard_values = strategy.indicator_manager.get_dashboard_ready_values()
            
            # 🔥 智能调试日志频率：未就绪时更频繁，就绪后降低频率
            log_frequency = 3 if not dashboard_values.get('all_ready', False) else 10
            if self._transmission_count % log_frequency == 0:
                incremental_data = tech_indicators.get('incremental_indicators', {})
                self.logger.info(
                    f"📡 [{strategy.pair_name}] 技术指标数据传输 #{self._transmission_count}: "
                    f"ATR={tech_indicators.get('atr'):.6f if tech_indicators.get('atr') else 'None'}, "
                    f"ADX={tech_indicators.get('adx'):.2f if tech_indicators.get('adx') else 'None'}, "
                    f"+DI={incremental_data.get('plus_di'):.2f if incremental_data.get('plus_di') else 'None'}, "
                    f"-DI={incremental_data.get('minus_di'):.2f if incremental_data.get('minus_di') else 'None'}, "
                    f"就绪={dashboard_values.get('all_ready', False)}, "
                    f"策略更新#{strategy.total_updates}"
                )

            # 🔥 新增：前20次传输每次都记录详细信息
            if self._transmission_count <= 20:
                self.logger.debug(
                    f"🔍 [{strategy.pair_name}] 详细传输信息 #{self._transmission_count}: "
                    f"原始技术指标={bool(tech_indicators)}, "
                    f"增量指标={bool(incremental_indicators)}, "
                    f"仪表盘值={bool(dashboard_values)}, "
                    f"指标就绪={tech_indicators.get('indicators_ready', False)}"
                )
            
            # 合并所有技术指标数据
            enhanced_indicators = {
                # 基本指标值
                "atr": tech_indicators.get('atr'),
                "adx": tech_indicators.get('adx'),
                "bb_position": tech_indicators.get('bb_position', 'unknown'),
                
                # 增量指标值
                "incremental_indicators": {
                    "atr": incremental_indicators.get('atr'),
                    "adx": incremental_indicators.get('adx'),
                    "plus_di": incremental_indicators.get('plus_di'),
                    "minus_di": incremental_indicators.get('minus_di'),
                    "atr_ready": incremental_indicators.get('atr_ready', False),
                    "adx_ready": incremental_indicators.get('adx_ready', False),
                    # 添加调试信息
                    "debug_info": incremental_indicators.get('debug_info', {})
                },
                
                # 仪表盘增强数据
                "dashboard_info": {
                    "all_ready": dashboard_values.get('all_ready', False),
                    "atr_progress": dashboard_values.get('atr_progress', 0),
                    "adx_progress": dashboard_values.get('adx_progress', 0),
                    "status_message": dashboard_values.get('status_message', '未知状态'),
                    "total_updates": dashboard_values.get('total_updates', 0),
                    "transmission_count": self._transmission_count  # 添加传输计数
                },
                
                # 就绪状态
                "indicators_ready": tech_indicators.get('indicators_ready', False),
                
                # 信号强度（从indicators获取）
                "signal_strength": current_state.get('indicators', {}).get('signal_strength')
            }
            
            return enhanced_indicators
            
        except Exception as e:
            self.logger.error(f"获取增强技术指标失败: {e}")
            # 返回基本的技术指标数据作为回退
            return current_state.get('technical_indicators', {})
    
    def __repr__(self):
        return (
            f"MultiPairStrategyManager("
            f"pairs={len(self.pair_strategies)}, "
            f"active={self.total_active_positions})"
        )