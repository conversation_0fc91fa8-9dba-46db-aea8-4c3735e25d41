"""
仓位管理器 - 套利仓位状态管理和风险控制
"""
import json
import logging
import os
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from enum import Enum
from dataclasses import dataclass, field, asdict
from data_standards import (
    DATA_STANDARDS, DATA_CONVERTER, DATA_VALIDATOR,
    StandardPositionData, RiskLevel
)


class PositionStatus(Enum):
    """仓位状态枚举"""
    IDLE = "idle"                      # 空仓
    OPENING = "opening"                # 开仓中
    OPEN = "open"                      # 已开仓
    CLOSING = "closing"                # 平仓中
    CLOSED = "closed"                  # 已平仓
    ERROR = "error"                    # 错误状态


class PositionType(Enum):
    """仓位类型枚举 - 简化为单一套利类型"""
    SHORT_FUTURES_LONG_SPOT = "short_futures_long_spot"  # 基差套利（买现货，卖期货）


@dataclass
class OrderInfo:
    """订单信息"""
    order_id: str
    inst_id: str
    side: str
    size: str
    price: Optional[str] = None
    status: str = "unknown"
    fill_size: str = "0"
    avg_px: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式（用于JSON序列化）"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat() if self.timestamp else None
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'OrderInfo':
        """从字典创建OrderInfo对象"""
        if 'timestamp' in data and data['timestamp']:
            try:
                timestamp = datetime.fromisoformat(data['timestamp'])
                # 确保时区一致性
                if timestamp.tzinfo is not None:
                    timestamp = timestamp.replace(tzinfo=None)
                data['timestamp'] = timestamp
            except Exception:
                data['timestamp'] = datetime.now()
        else:
            data['timestamp'] = datetime.now()
        return cls(**data)


@dataclass
class Position:
    """套利仓位信息"""
    position_id: str
    position_type: PositionType
    status: PositionStatus
    
    # 订单信息
    spot_order: Optional[OrderInfo] = None
    futures_order: Optional[OrderInfo] = None
    
    # 价格信息
    entry_spot_price: Optional[float] = None
    entry_futures_price: Optional[float] = None
    entry_basis: Optional[float] = None
    
    # 时间信息
    open_time: Optional[datetime] = None
    close_time: Optional[datetime] = None
    
    # 盈亏信息
    realized_pnl: float = 0.0
    unrealized_pnl: float = 0.0
    
    # 风险信息
    max_holding_duration: timedelta = timedelta(hours=24)
    stop_loss_threshold: float = 0.02  # 2%
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式（用于JSON序列化）- 使用标准化格式"""
        data = {
            'position_id': self.position_id,
            'position_type': self.position_type.value,
            'status': self.status.value,
            'entry_spot_price': round(self.entry_spot_price, DATA_STANDARDS.PRICE_PRECISION) if self.entry_spot_price else 0.0,
            'entry_futures_price': round(self.entry_futures_price, DATA_STANDARDS.PRICE_PRECISION) if self.entry_futures_price else 0.0,
            'entry_basis': round(self.entry_basis, DATA_STANDARDS.BASIS_DECIMAL_PRECISION) if self.entry_basis else 0.0,
            'open_time': DATA_CONVERTER.format_timestamp(self.open_time) if self.open_time else None,
            'close_time': DATA_CONVERTER.format_timestamp(self.close_time) if self.close_time else None,
            'realized_pnl': round(self.realized_pnl, DATA_STANDARDS.AMOUNT_PRECISION),
            'unrealized_pnl': round(self.unrealized_pnl, DATA_STANDARDS.AMOUNT_PRECISION),
            'max_holding_duration_seconds': self.max_holding_duration.total_seconds(),
            'stop_loss_threshold': self.stop_loss_threshold,
            'spot_order': self.spot_order.to_dict() if self.spot_order else None,
            'futures_order': self.futures_order.to_dict() if self.futures_order else None
        }
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Position':
        """从字典创建Position对象"""
        # 处理枚举类型
        position_type = PositionType(data['position_type'])
        status = PositionStatus(data['status'])
        
        # 处理时间字段 - 确保时区一致性
        open_time = None
        if data.get('open_time'):
            try:
                open_time = datetime.fromisoformat(data['open_time'])
                # 如果有时区信息，转换为本地时间（无时区）
                if open_time.tzinfo is not None:
                    open_time = open_time.replace(tzinfo=None)
            except Exception:
                open_time = None

        close_time = None
        if data.get('close_time'):
            try:
                close_time = datetime.fromisoformat(data['close_time'])
                # 如果有时区信息，转换为本地时间（无时区）
                if close_time.tzinfo is not None:
                    close_time = close_time.replace(tzinfo=None)
            except Exception:
                close_time = None
        
        # 处理时间间隔
        max_holding_duration = timedelta(seconds=data.get('max_holding_duration_seconds', 86400))
        
        # 处理订单信息
        spot_order = OrderInfo.from_dict(data['spot_order']) if data.get('spot_order') else None
        futures_order = OrderInfo.from_dict(data['futures_order']) if data.get('futures_order') else None
        
        return cls(
            position_id=data['position_id'],
            position_type=position_type,
            status=status,
            spot_order=spot_order,
            futures_order=futures_order,
            entry_spot_price=data.get('entry_spot_price'),
            entry_futures_price=data.get('entry_futures_price'),
            entry_basis=data.get('entry_basis'),
            open_time=open_time,
            close_time=close_time,
            realized_pnl=data.get('realized_pnl', 0.0),
            unrealized_pnl=data.get('unrealized_pnl', 0.0),
            max_holding_duration=max_holding_duration,
            stop_loss_threshold=data.get('stop_loss_threshold', 0.02)
        )


class PositionManager:
    """仓位管理器 - 管理套利策略的仓位状态和风险控制"""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 手动平仓检测相关
        self.last_position_check_time = None
        self.manual_close_check_interval = 30  # 30秒检查一次
        self.okx_connector = None  # 将在后续设置
        
        # 当前活跃仓位
        self.current_position: Optional[Position] = None
        
        # 历史仓位
        self.position_history: List[Position] = []
        
        # 风险参数 - 修复配置访问
        if hasattr(config, 'PAIR_CONFIG') and isinstance(config.PAIR_CONFIG, dict):
            self.max_holding_hours = config.PAIR_CONFIG.get("max_holding_duration_hours", 24)
        else:
            self.max_holding_hours = 24  # 默认24小时

        if hasattr(config, 'RISK_MANAGEMENT') and isinstance(config.RISK_MANAGEMENT, dict):
            self.stop_loss_percent = config.RISK_MANAGEMENT.get("STOP_LOSS_PERCENT", 0.02)
            self.max_concurrent_trades = config.RISK_MANAGEMENT.get("MAX_CONCURRENT_TRADES", 3)
        else:
            self.stop_loss_percent = 0.02  # 默认2%止损
            self.max_concurrent_trades = 3  # 默认最大3个并发交易
        
        # 统计信息
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_pnl = 0.0
        
        # 风险监控
        self.risk_alerts: List[str] = []

        # 自动保存配置
        self.auto_save_enabled = getattr(config, 'AUTO_SAVE_POSITION_STATE', True)

        self.logger.info("仓位管理器初始化完成")
    
    @property
    def in_position(self) -> bool:
        """是否持有仓位"""
        return (
            self.current_position is not None and 
            self.current_position.status in [PositionStatus.OPENING, PositionStatus.OPEN]
        )
    
    @property
    def is_opening(self) -> bool:
        """是否正在开仓"""
        return (
            self.current_position is not None and 
            self.current_position.status == PositionStatus.OPENING
        )
    
    @property
    def is_closing(self) -> bool:
        """是否正在平仓"""
        return (
            self.current_position is not None and 
            self.current_position.status == PositionStatus.CLOSING
        )
    
    def can_open_position(self) -> bool:
        """检查是否可以开新仓位"""
        # 检查当前是否已有仓位
        if self.in_position:
            return False
        
        # 注意：max_concurrent_trades应该基于当前活跃仓位数，而不是历史交易总数
        # 由于当前架构是单仓位管理，这里简化处理
        # 如果需要多仓位并发，应该维护一个活跃仓位计数器
        
        return True
    
    def create_position(self, position_type: PositionType, 
                       spot_price: float, futures_price: float) -> str:
        """
        创建新仓位
        
        Args:
            position_type: 仓位类型
            spot_price: 现货价格
            futures_price: 期货价格
            
        Returns:
            str: 仓位ID
        """
        if not self.can_open_position():
            raise RuntimeError("无法开新仓位：已有活跃仓位或达到最大交易数")
        
        # 生成仓位ID
        position_id = f"pos_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 计算基差
        basis = (futures_price - spot_price) / spot_price
        
        # 创建仓位
        self.current_position = Position(
            position_id=position_id,
            position_type=position_type,
            status=PositionStatus.OPENING,
            entry_spot_price=spot_price,
            entry_futures_price=futures_price,
            entry_basis=basis,
            open_time=datetime.now(),
            max_holding_duration=timedelta(hours=self.max_holding_hours),
            stop_loss_threshold=self.stop_loss_percent
        )
        
        self.logger.info(
            f"创建新仓位 - ID:{position_id}, 类型:{position_type.value}, "
            f"基差:{basis:.6f}, 现货:{spot_price}, 期货:{futures_price}"
        )

        # 自动保存状态
        if self.auto_save_enabled:
            try:
                self.save_state_to_json()
            except Exception as e:
                self.logger.error(f"创建仓位后保存状态失败: {e}")

        return position_id
    
    def update_position_orders(self, spot_order: OrderInfo, futures_order: OrderInfo):
        """更新仓位的订单信息"""
        if not self.current_position:
            raise RuntimeError("没有活跃仓位可更新")
        
        self.current_position.spot_order = spot_order
        self.current_position.futures_order = futures_order
        
        self.logger.info(
            f"更新仓位订单 - 现货订单:{spot_order.order_id}, "
            f"期货订单:{futures_order.order_id}"
        )

        # 自动保存状态
        if self.auto_save_enabled:
            try:
                self.save_state_to_json()
            except Exception as e:
                self.logger.error(f"更新订单信息后保存状态失败: {e}")
    
    def confirm_position_open(self):
        """确认仓位开仓成功"""
        if not self.current_position or self.current_position.status != PositionStatus.OPENING:
            raise RuntimeError("没有开仓中的仓位可确认")
        
        self.current_position.status = PositionStatus.OPEN
        self.total_trades += 1
        
        self.logger.info(
            f"仓位开仓确认 - ID:{self.current_position.position_id}, "
            f"总交易数:{self.total_trades}"
        )

        # 自动保存状态
        if self.auto_save_enabled:
            try:
                self.save_state_to_json()
            except Exception as e:
                self.logger.error(f"确认开仓后保存状态失败: {e}")
    
    def start_position_close(self):
        """开始平仓流程"""
        if not self.current_position or self.current_position.status != PositionStatus.OPEN:
            raise RuntimeError("没有开仓状态的仓位可平仓")
        
        self.current_position.status = PositionStatus.CLOSING
        
        self.logger.info(f"开始平仓 - ID:{self.current_position.position_id}")

        # 自动保存状态
        if self.auto_save_enabled:
            try:
                self.save_state_to_json()
            except Exception as e:
                self.logger.error(f"开始平仓后保存状态失败: {e}")
    
    def confirm_position_close(self, realized_pnl: float = 0.0):
        """确认仓位平仓完成"""
        if not self.current_position or self.current_position.status != PositionStatus.CLOSING:
            raise RuntimeError("没有平仓中的仓位可确认")
        
        # 更新仓位信息
        self.current_position.status = PositionStatus.CLOSED
        self.current_position.close_time = datetime.now()
        self.current_position.realized_pnl = realized_pnl
        
        # 更新统计信息
        self.total_pnl += realized_pnl
        if realized_pnl > 0:
            self.winning_trades += 1
        else:
            self.losing_trades += 1
        
        # 移动到历史记录
        self.position_history.append(self.current_position)
        
        self.logger.info(
            f"仓位平仓确认 - ID:{self.current_position.position_id}, "
            f"已实现盈亏:{realized_pnl:.4f}, 累计盈亏:{self.total_pnl:.4f}"
        )
        
        # 清除当前仓位
        self.current_position = None

        # 自动保存状态（空仓状态）
        if self.auto_save_enabled:
            try:
                self.save_state_to_json()
            except Exception as e:
                self.logger.error(f"确认平仓后保存状态失败: {e}")
    
    def set_position_error(self, error_message: str):
        """设置仓位错误状态"""
        if not self.current_position:
            return
        
        self.current_position.status = PositionStatus.ERROR
        self.risk_alerts.append(f"仓位错误: {error_message}")
        
        self.logger.error(
            f"仓位错误 - ID:{self.current_position.position_id}, "
            f"错误:{error_message}"
        )
    
    def update_unrealized_pnl(self, current_spot_price: float, current_futures_price: float):
        """更新未实现盈亏"""
        if not self.current_position or self.current_position.status != PositionStatus.OPEN:
            return
        
        try:
            # 计算当前基差
            current_basis = (current_futures_price - current_spot_price) / current_spot_price
            entry_basis = self.current_position.entry_basis
            
            # 计算基差变化 - 简化为单一套利类型
            # 基差套利：基差收敛为盈利
            basis_pnl = entry_basis - current_basis
            
            # 这里简化计算，实际应该考虑仓位大小和价格变化
            self.current_position.unrealized_pnl = basis_pnl * 100  # 转换为基点
            
        except Exception as e:
            self.logger.error(f"更新未实现盈亏失败: {e}")
    
    def check_time_stop_loss(self) -> bool:
        """检查时间止损"""
        if not self.current_position or self.current_position.status != PositionStatus.OPEN:
            return False
        
        if not self.current_position.open_time:
            return False

        # 确保时区一致性
        open_time = self.current_position.open_time
        if open_time.tzinfo is not None:
            open_time = open_time.replace(tzinfo=None)

        holding_duration = datetime.now() - open_time
        max_duration = self.current_position.max_holding_duration
        
        if holding_duration > max_duration:
            self.risk_alerts.append(
                f"时间止损触发: 持仓时长{holding_duration} > 最大允许{max_duration}"
            )
            return True
        
        return False
    
    def check_pnl_stop_loss(self) -> bool:
        """检查盈亏止损"""
        if not self.current_position or self.current_position.status != PositionStatus.OPEN:
            return False
        
        unrealized_pnl = self.current_position.unrealized_pnl
        stop_loss_threshold = self.current_position.stop_loss_threshold * 100  # 转换为基点
        
        if unrealized_pnl < -stop_loss_threshold:
            self.risk_alerts.append(
                f"PnL止损触发: 未实现盈亏{unrealized_pnl:.2f} < -{stop_loss_threshold:.2f}"
            )
            return True
        
        return False
    
    def check_margin_risk(self, position_data: Dict[str, Any]) -> bool:
        """检查保证金风险"""
        if not position_data:
            return False
        
        try:
            # 获取unrealized profit ratio (未实现盈亏比率)
            upl_ratio = float(position_data.get('uplRatio', 0))
            margin_ratio = float(position_data.get('mgnRatio', 0))
            
            # 检查保证金比率是否过低
            margin_call_threshold = self.config.RISK_MANAGEMENT.get("MARGIN_CALL_THRESHOLD", 0.15)
            
            if margin_ratio > 0 and margin_ratio < margin_call_threshold:
                self.risk_alerts.append(
                    f"保证金风险警告: 保证金比率{margin_ratio:.3f} < {margin_call_threshold:.3f}"
                )
                return True
            
            # 检查未实现亏损是否过大
            max_drawdown = self.config.RISK_MANAGEMENT.get("MAX_TOTAL_DRAWDOWN_RATIO", 0.10)
            if upl_ratio < -max_drawdown:
                self.risk_alerts.append(
                    f"最大回撤警告: 未实现盈亏比率{upl_ratio:.3f} < -{max_drawdown:.3f}"
                )
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查保证金风险失败: {e}")
            return False
    
    def get_position_info(self) -> Optional[Dict[str, Any]]:
        """获取当前仓位信息"""
        if not self.current_position:
            return None
        
        holding_duration = None
        if self.current_position.open_time:
            # 确保时区一致性
            open_time = self.current_position.open_time
            if open_time.tzinfo is not None:
                open_time = open_time.replace(tzinfo=None)
            holding_duration = datetime.now() - open_time
        
        return {
            "position_id": self.current_position.position_id,
            "position_type": self.current_position.position_type.value,
            "status": self.current_position.status.value,
            "entry_basis": self.current_position.entry_basis,
            "entry_spot_price": self.current_position.entry_spot_price,
            "entry_futures_price": self.current_position.entry_futures_price,
            "open_time": self.current_position.open_time.isoformat() if self.current_position.open_time else None,
            "holding_duration_seconds": holding_duration.total_seconds() if holding_duration else 0,
            "unrealized_pnl": self.current_position.unrealized_pnl,
            "max_holding_hours": self.max_holding_hours,
            "stop_loss_percent": self.stop_loss_percent
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        win_rate = 0.0
        if self.total_trades > 0:
            win_rate = self.winning_trades / self.total_trades
        
        return {
            "total_trades": self.total_trades,
            "winning_trades": self.winning_trades,
            "losing_trades": self.losing_trades,
            "win_rate": win_rate,
            "total_pnl": self.total_pnl,
            "avg_pnl_per_trade": self.total_pnl / max(self.total_trades, 1),
            "current_position": self.get_position_info(),
            "recent_alerts": self.risk_alerts[-5:] if self.risk_alerts else []
        }
    
    def clear_alerts(self):
        """清除风险警报"""
        self.risk_alerts.clear()
    
    def reset(self):
        """重置管理器状态"""
        if self.current_position:
            self.position_history.append(self.current_position)
        
        self.current_position = None
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_pnl = 0.0
        self.risk_alerts.clear()
        
        self.logger.info("仓位管理器状态已重置")
    
    def save_state_to_json(self, filepath: str = 'position_state.json'):
        """
        保存当前仓位状态到JSON文件
        
        Args:
            filepath: JSON文件路径
        """
        try:
            # 构建要保存的状态数据
            state_data = {
                'version': '1.0',
                'timestamp': datetime.now().isoformat(),
                'current_position': None,
                'statistics': {
                    'total_trades': self.total_trades,
                    'winning_trades': self.winning_trades,
                    'losing_trades': self.losing_trades,
                    'total_pnl': self.total_pnl
                },
                'risk_alerts': self.risk_alerts[-10:] if self.risk_alerts else []  # 保存最近10条警报
            }
            
            # 如果有活跃仓位，序列化它
            if self.current_position:
                state_data['current_position'] = self.current_position.to_dict()
            
            # 创建目录（如果不存在）
            os.makedirs(os.path.dirname(os.path.abspath(filepath)), exist_ok=True)
            
            # 写入JSON文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"仓位状态已保存到 {filepath}")
            
            # 如果是空仓状态，记录相应信息
            if not self.current_position:
                self.logger.debug("保存的状态: 无活跃仓位")
            else:
                self.logger.debug(
                    f"保存的状态: 仓位ID={self.current_position.position_id}, "
                    f"状态={self.current_position.status.value}"
                )
                
        except Exception as e:
            self.logger.error(f"保存仓位状态失败: {e}")
            raise
    
    def load_state_from_json(self, filepath: str = 'position_state.json') -> bool:
        """
        从JSON文件加载仓位状态
        
        Args:
            filepath: JSON文件路径
            
        Returns:
            bool: 是否成功加载状态
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(filepath):
                self.logger.info(f"状态文件 {filepath} 不存在，使用默认状态启动")
                return False
            
            # 读取JSON文件
            with open(filepath, 'r', encoding='utf-8') as f:
                state_data = json.load(f)
            
            # 验证文件格式
            if 'version' not in state_data:
                self.logger.warning(f"状态文件格式不正确，忽略恢复")
                return False
            
            # 恢复统计信息 (沙盒模式下重置统计数据)
            if 'statistics' in state_data and not self.config.USE_SANDBOX:
                stats = state_data['statistics']
                self.total_trades = stats.get('total_trades', 0)
                self.winning_trades = stats.get('winning_trades', 0)
                self.losing_trades = stats.get('losing_trades', 0)
                self.total_pnl = stats.get('total_pnl', 0.0)

                self.logger.info(
                    f"恢复统计信息: 总交易={self.total_trades}, 盈利={self.winning_trades}, "
                    f"亏损={self.losing_trades}, 总盈亏={self.total_pnl:.4f}"
                )
            elif self.config.USE_SANDBOX:
                # 沙盒模式下重置统计数据，避免显示历史交易数据
                self.total_trades = 0
                self.winning_trades = 0
                self.losing_trades = 0
                self.total_pnl = 0.0
                self.logger.info("沙盒模式: 已重置统计数据")
            
            # 恢复风险警报
            if 'risk_alerts' in state_data:
                self.risk_alerts = state_data['risk_alerts']
                if self.risk_alerts:
                    self.logger.info(f"恢复了 {len(self.risk_alerts)} 条风险警报")
            
            # 恢复当前仓位
            if state_data.get('current_position'):
                try:
                    self.current_position = Position.from_dict(state_data['current_position'])
                    
                    self.logger.info(
                        f"✅ 成功恢复仓位状态: "
                        f"ID={self.current_position.position_id}, "
                        f"类型={self.current_position.position_type.value}, "
                        f"状态={self.current_position.status.value}, "
                        f"入场基差={self.current_position.entry_basis:.6f}"
                    )
                    
                    # 如果仓位处于错误状态，添加警告
                    if self.current_position.status == PositionStatus.ERROR:
                        self.logger.warning("⚠️  恢复的仓位处于错误状态，请检查!")
                    
                    # 检查仓位是否超时
                    if (self.current_position.open_time and
                        self.current_position.status == PositionStatus.OPEN):
                        # 确保时区一致性
                        open_time = self.current_position.open_time
                        if open_time.tzinfo is not None:
                            open_time = open_time.replace(tzinfo=None)

                        holding_duration = datetime.now() - open_time
                        max_duration = self.current_position.max_holding_duration

                        if holding_duration > max_duration:
                            self.logger.warning(
                                f"⚠️  恢复的仓位已超时: 持仓{holding_duration} > 最大允许{max_duration}"
                            )
                            self.risk_alerts.append("恢复的仓位已超时，建议立即平仓")
                            
                except Exception as e:
                    self.logger.error(f"恢复仓位状态失败: {e}")
                    self.current_position = None
                    return False
            else:
                self.logger.info("恢复状态: 无活跃仓位")
            
            # 记录恢复时间
            load_time = state_data.get('timestamp', 'unknown')
            self.logger.info(f"状态恢复完成，原保存时间: {load_time}")
            
            return True
            
        except json.JSONDecodeError as e:
            self.logger.error(f"状态文件JSON格式错误: {e}")
            return False
        except Exception as e:
            self.logger.error(f"加载仓位状态失败: {e}")
            return False
    
    def set_okx_connector(self, connector):
        """设置OKX连接器用于手动平仓检测"""
        self.okx_connector = connector
        self.logger.info("✅ OKX连接器已设置，启用手动平仓检测")
    
    async def check_manual_close_detection(self) -> bool:
        """
        检测用户是否手动平仓了
        
        Returns:
            bool: True表示检测到手动平仓，False表示无变化
        """
        # 如果没有活跃仓位，无需检测
        if not self.in_position or not self.okx_connector:
            return False
        
        # 检查时间间隔，避免频繁查询
        current_time = datetime.now()
        if (self.last_position_check_time and 
            (current_time - self.last_position_check_time).total_seconds() < self.manual_close_check_interval):
            return False
        
        self.last_position_check_time = current_time
        
        try:
            self.logger.debug("🔍 检查是否存在手动平仓操作...")
            
            # 检查期货仓位状态
            position_detected = await self._check_exchange_positions()
            
            if not position_detected:
                # 交易所无仓位，但程序认为有仓位 - 可能被手动平仓
                self.logger.warning("⚠️  检测到手动平仓：交易所无仓位但程序状态显示有仓位")
                await self._handle_manual_close_detected()
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"手动平仓检测失败: {e}")
            return False
    
    async def _check_exchange_positions(self) -> bool:
        """
        检查交易所实际仓位状态
        
        Returns:
            bool: True表示交易所有仓位，False表示无仓位
        """
        try:
            # 查询所有期货仓位
            positions_result = await self.okx_connector.get_positions("SWAP")
            
            if positions_result.get('code') != '0':
                self.logger.error(f"查询仓位失败: {positions_result.get('msg')}")
                return True  # 查询失败时保守处理，假设有仓位
            
            positions_data = positions_result.get('data', [])
            
            # 检查是否有活跃的期货仓位
            for pos in positions_data:
                inst_id = pos.get('instId', '')
                pos_size = float(pos.get('pos', 0))
                
                # 检查与当前仓位相关的合约
                if (inst_id.endswith('-SWAP') and abs(pos_size) > 0.001):
                    self.logger.debug(f"发现活跃期货仓位: {inst_id}, 大小: {pos_size}")
                    return True
            
            self.logger.debug("交易所无活跃期货仓位")
            return False
            
        except Exception as e:
            self.logger.error(f"检查交易所仓位状态失败: {e}")
            return True  # 错误时保守处理
    
    async def _handle_manual_close_detected(self):
        """处理检测到的手动平仓操作"""
        try:
            position_id = self.current_position.position_id if self.current_position else "unknown"
            
            self.logger.warning(f"🔧 处理手动平仓: 仓位ID {position_id}")
            
            # 记录手动平仓事件
            if self.current_position:
                self.current_position.status = PositionStatus.CLOSED
                self.current_position.close_time = datetime.now()
                
                # 简单估算PnL（如果没有精确数据）
                if self.current_position.realized_pnl == 0.0:
                    self.current_position.realized_pnl = self.current_position.unrealized_pnl
                
                # 添加到历史记录
                self.position_history.append(self.current_position)
                self.total_trades += 1
                self.total_pnl += self.current_position.realized_pnl
                
                if self.current_position.realized_pnl > 0:
                    self.winning_trades += 1
                else:
                    self.losing_trades += 1
            
            # 清除当前仓位
            self.current_position = None
            
            # 保存状态
            self.save_state_to_json()
            
            self.logger.info(f"✅ 手动平仓处理完成，系统状态已同步")
            
        except Exception as e:
            self.logger.error(f"处理手动平仓失败: {e}")
    
    async def validate_position_consistency(self) -> bool:
        """
        验证程序仓位状态与交易所的一致性
        
        Returns:
            bool: True表示一致，False表示不一致
        """
        if not self.okx_connector:
            return True  # 无连接器时假设一致
        
        try:
            exchange_has_position = await self._check_exchange_positions()
            program_has_position = self.in_position
            
            consistency = exchange_has_position == program_has_position
            
            if not consistency:
                self.logger.warning(
                    f"⚠️  仓位状态不一致: 程序={program_has_position}, 交易所={exchange_has_position}"
                )
            
            return consistency
            
        except Exception as e:
            self.logger.error(f"验证仓位一致性失败: {e}")
            return True  # 错误时假设一致
    
    def __repr__(self):
        status = "有仓位" if self.in_position else "空仓"
        return (
            f"PositionManager(status={status}, "
            f"total_trades={self.total_trades}, total_pnl={self.total_pnl:.4f})"
        )